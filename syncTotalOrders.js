const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const OrderStoreModel = require('./lib/models/orderStore');
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const tool = require('./lib/utils/tool');
const locationHelper = require('./lib/utils/location');

StoreModel
  .find({member: {$exists: true}}, '_id')
  .lean()
  .exec((err, results) => {
    if (err) {
      return console.log('haha:err', err);
    }

    if (!results || !results.length) {
      return console.log('haha:Store not found');
    }

    results.map((item, i) => {
      OrderStoreModel
        .count({store: item._id, status: 4})
        .lean()
        .exec((err, count) => {
          if (err) {
            return console.log('haha:err1', i, err);
          }

          StoreModel
            .update({
              _id: item._id
            }, {
              totalOrders: count
            })
            .lean()
            .exec((err, result) => {
              if (err) {
                return console.log('haha:err2', i, err);
              }

              console.log('haha:result', i, result);
            })
        })
    })
  })
