
var x = 2;
var n = 5;
function tinhtoan(tinh){
    var S = 0;
    for(var i = 1 ; i <= n ; i++ )
    {
        S = S + 1 / ((2 * i) + 1);
    }
    return S;
}
var tong = tinhtoan(n)
console.log('b1', tong)

/*
function tinhtoan2(tinh2){
    var S2 = 0;
    for (var i = 1 ; i <= n ; i++ )
    {
        S2 = S2 + Math.pow(x, (2 * i));
    }
    return S2;
}
var tinh2 = tinhtoan2(x);
console.log('b2', tinh2);

function tinhtoan3(tinh3){
    var S3 = 0;
    for(var i = 1 ; i <= n ; i++)
    {
        S3 = S3 + Math.pow(x, (2 * i)+ 1);
    }
    return S3;
}
var tinh3 = tinhtoan3(x);
console.log('b3', tinh3)


function tinhtoan4(tinh4){
    var S4 = 0;
    for (var i = 1 ; i <= n ; i++)
    {
        S4 = S4 + 1 / ( i * ( i + 1 ));
    }
    return S4;
}
var tinh4 = tinhtoan4(n);
console.log('b4 = ',tinh4)
*/
// de quy

// let n = 5;
let i = 1;
let sum = 0;

const tinh = () => {
  sum += 1 / i;

  if (i >= 2 * n + 1) {
    console.log('sum = ', sum);
  } else if (i < 2 * n + 1) {
    i += 2;
    tinh();
  }
}
tinh();

// let x = 2;
// let sum2 = 0;
// const tinh2 = () => {
//   sum2 += i;

//   if (i >= (Math.pow(x, 2 * n))) {
//     console.log('sum2 = ', sum2);
//   }
//   else if (i < (Math.pow(x, 2 * n))) {
//     i += 2;
//     tinh2();
//   }
// }
// tinh2();

// let sum4 = 0;
// const tinh4 = () => {
//   sum4 += 1 / i;
//   if (i >= (n * (n + 1))) {
//     console.log('sum4 = ', sum4);
//   }
//   else if (i < (n * (n + 1))) {
//     i += 1;
//     tinh4();
//   }
// }
// tinh4();