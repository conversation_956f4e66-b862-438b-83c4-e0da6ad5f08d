{"header": {"reportVersion": 2, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20221123.115449.43924.0.001.json", "dumpEventTime": "2022-11-23T11:54:49Z", "dumpEventTimeStamp": "1669179289295", "processId": 43924, "threadId": null, "cwd": "/Users/<USER>/Work/HeyU/ss-product-service", "commandLine": ["node", "syncStore.js"], "nodejsVersion": "v13.9.0", "wordSize": 64, "arch": "x64", "platform": "darwin", "componentVersions": {"node": "13.9.0", "v8": "7.9.317.25-node.28", "uv": "1.34.2", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "79", "nghttp2": "1.40.0", "napi": "5", "llhttp": "2.0.4", "openssl": "1.1.1d", "cldr": "36.0", "icu": "65.1", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "headersUrl": "https://nodejs.org/download/release/v13.9.0/node-v13.9.0-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v13.9.0/node-v13.9.0.tar.gz"}, "osName": "<PERSON>", "osRelease": "21.6.0", "osVersion": "Darwin Kernel Version 21.6.0: Mon Aug 22 20:17:10 PDT 2022; root:xnu-8020.140.49~2/RELEASE_X86_64", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 99502440, "nice": 0, "sys": 51504970, "idle": 123434940, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 12283110, "nice": 0, "sys": 16858980, "idle": 245280550, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 87968590, "nice": 0, "sys": 42934700, "idle": 143521280, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 10683020, "nice": 0, "sys": 12080650, "idle": 251658800, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 83932040, "nice": 0, "sys": 40293920, "idle": 150198340, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 10748030, "nice": 0, "sys": 10691970, "idle": 252982320, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 83970270, "nice": 0, "sys": 38157050, "idle": 152296690, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 10936750, "nice": 0, "sys": 9900890, "idle": 253584510, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 83800890, "nice": 0, "sys": 35040640, "idle": 155582190, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 11188210, "nice": 0, "sys": 9243140, "idle": 253990630, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 83334740, "nice": 0, "sys": 33458990, "idle": 157629710, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 11339350, "nice": 0, "sys": 8622910, "idle": 254459530, "irq": 0}], "networkInterfaces": [{"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}, {"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "fe80::1", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 1}, {"name": "en5", "internal": false, "mac": "ac:de:48:00:11:22", "address": "fe80::aede:48ff:fe00:1122", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 4}, {"name": "en0", "internal": false, "mac": "f0:18:98:0a:16:b8", "address": "fe80::cfc:8fcb:fe14:9859", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 6}, {"name": "en0", "internal": false, "mac": "f0:18:98:0a:16:b8", "address": "************", "netmask": "*************", "family": "IPv4"}, {"name": "awdl0", "internal": false, "mac": "2e:4c:e6:74:00:41", "address": "fe80::2c4c:e6ff:fe74:41", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 7}, {"name": "llw0", "internal": false, "mac": "2e:4c:e6:74:00:41", "address": "fe80::2c4c:e6ff:fe74:41", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 8}, {"name": "utun0", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::749:bf25:2064:569", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 14}, {"name": "utun1", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::e383:62b6:26b:a944", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 15}, {"name": "utun2", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::ce81:b1c:bd2c:69e", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 16}, {"name": "utun3", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::ee9b:5334:cb18:3796", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 17}, {"name": "utun4", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::57eb:b799:2cd3:1d5d", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 18}], "host": "Thai-Son-2.local"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00000001001a038c", "symbol": "report::TriggerNodeReport(v8::Isolate*, node::Environment*, char const*, char const*, std::__1::basic_string<char, std::__1::char_traits<char>, std::__1::allocator<char> > const&, v8::Local<v8::String>) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100093d77", "symbol": "node::OnFatalError(char const*, char const*) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x00000001001d40f7", "symbol": "v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, bool) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x00000001001d4093", "symbol": "v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, bool) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100356b65", "symbol": "v8::internal::Heap::FatalProcessOutOfMemory(char const*) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100358244", "symbol": "v8::internal::Heap::RecomputeLimits(v8::internal::GarbageCollector) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100354fde", "symbol": "v8::internal::Heap::PerformGarbageCollection(v8::internal::GarbageCollector, v8::GCCallbackFlags) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100352fc6", "symbol": "v8::internal::Heap::CollectGarbage(v8::internal::AllocationSpace, v8::internal::GarbageCollectionReason, v8::GCCallbackFlags) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010035e94a", "symbol": "v8::internal::Heap::AllocateRawWithLightRetrySlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010035e9d1", "symbol": "v8::internal::Heap::AllocateRawWithRetryOrFailSlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010032bd16", "symbol": "v8::internal::Factory::NewFillerObject(int, bool, v8::internal::AllocationType, v8::internal::AllocationOrigin) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010066bfd0", "symbol": "v8::internal::Runtime_AllocateInOldGeneration(int, unsigned long*, v8::internal::Isolate*) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x00000001009a7dd9", "symbol": "Builtins_CEntry_Return1_DontSaveFPRegs_ArgvOnStack_NoBuiltinExit [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x00003589d2447b18", "symbol": ""}], "javascriptHeap": {"totalMemory": 2155847680, "totalCommittedMemory": 2152630616, "usedMemory": 2148605032, "availableMemory": 45870192, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 33328, "capacity": 33040, "used": 33040, "available": 0}, "new_space": {"memorySize": 3670016, "committedMemory": 1203344, "capacity": 1832992, "used": 161832, "available": 1671160}, "old_space": {"memorySize": 2137403392, "committedMemory": 2137070456, "capacity": 2134922080, "used": 2134523656, "available": 398424}, "code_space": {"memorySize": 430080, "committedMemory": 411328, "capacity": 386432, "used": 386432, "available": 0}, "map_space": {"memorySize": 1314816, "committedMemory": 1144928, "capacity": 822640, "used": 822640, "available": 0}, "large_object_space": {"memorySize": 12718080, "committedMemory": 12718080, "capacity": 12674648, "used": 12674648, "available": 0}, "code_large_object_space": {"memorySize": 49152, "committedMemory": 49152, "capacity": 2784, "used": 2784, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 1832992, "used": 0, "available": 1832992}}}, "resourceUsage": {"userCpuSeconds": 56.2262, "kernelCpuSeconds": 2.47827, "cpuConsumptionPercent": 136.522, "maxRss": 2300542189568, "pageFaults": {"IORequired": 24, "IONotRequired": 588762}, "fsActivity": {"reads": 0, "writes": 0}}, "libuv": [], "workers": [], "environmentVariables": {"TERM_PROGRAM": "Apple_Terminal", "NVM_CD_FLAGS": "", "ANDROID_HOME": "/Users/<USER>/Library/Android/sdk", "TERM": "xterm-256color", "SHELL": "/bin/bash", "TMPDIR": "/var/folders/c7/lydhxyxx28l1yf7js90g4bw00000gn/T/", "TERM_PROGRAM_VERSION": "445", "OLDPWD": "/Users/<USER>/Work/HeyU", "TERM_SESSION_ID": "A80C7C2D-B1E1-47E8-8EDB-CFB6F7E552D2", "NVM_DIR": "/Users/<USER>/.nvm", "USER": "ownere", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.s2WCS493u3/Listeners", "PATH": "/Users/<USER>/.nvm/versions/node/v13.9.0/bin:/Library/Java/JavaVirtualMachines/adoptopenjdk-8.jdk/Contents/Home/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Library/Apple/usr/bin:/Users/<USER>/Library/Android/sdk/emulator:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/tools/bin:/Users/<USER>/Library/Android/sdk/platform-tools", "__CFBundleIdentifier": "com.apple.Terminal", "PWD": "/Users/<USER>/Work/HeyU/ss-product-service", "JAVA_HOME": "/Library/Java/JavaVirtualMachines/adoptopenjdk-8.jdk/Contents/Home", "XPC_FLAGS": "0x0", "XPC_SERVICE_NAME": "0", "HOME": "/Users/<USER>", "SHLVL": "1", "LOGNAME": "ownere", "LC_CTYPE": "UTF-8", "NVM_BIN": "/Users/<USER>/.nvm/versions/node/v13.9.0/bin", "ANDROID_NDK_HOME": "/usr/local/share/android-ndk", "_": "/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node", "__CF_USER_TEXT_ENCODING": "0x1F5:0x0:0x0"}, "userLimits": {"core_file_size_blocks": {"soft": 0, "hard": "unlimited"}, "data_seg_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "file_size_blocks": {"soft": "unlimited", "hard": "unlimited"}, "max_locked_memory_bytes": {"soft": "unlimited", "hard": "unlimited"}, "max_memory_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "open_files": {"soft": 1048575, "hard": "unlimited"}, "stack_size_bytes": {"soft": 8388608, "hard": 67104768}, "cpu_time_seconds": {"soft": "unlimited", "hard": "unlimited"}, "max_user_processes": {"soft": 5568, "hard": 8352}, "virtual_memory_kbytes": {"soft": "unlimited", "hard": "unlimited"}}, "sharedObjects": ["/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node", "/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation", "/usr/lib/libobjc.A.dylib", "/usr/lib/liboah.dylib", "/usr/lib/libfakelink.dylib", "/usr/lib/libicucore.A.dylib", "/usr/lib/libSystem.B.dylib", "/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking", "/usr/lib/libc++abi.dylib", "/usr/lib/libc++.1.dylib", "/usr/lib/system/libcache.dylib", "/usr/lib/system/libcommonCrypto.dylib", "/usr/lib/system/libcompiler_rt.dylib", "/usr/lib/system/libcopyfile.dylib", "/usr/lib/system/libcorecrypto.dylib", "/usr/lib/system/libdispatch.dylib", "/usr/lib/system/libdyld.dylib", "/usr/lib/system/libkeymgr.dylib", "/usr/lib/system/libmacho.dylib", "/usr/lib/system/libquarantine.dylib", "/usr/lib/system/libremovefile.dylib", "/usr/lib/system/libsystem_asl.dylib", "/usr/lib/system/libsystem_blocks.dylib", "/usr/lib/system/libsystem_c.dylib", "/usr/lib/system/libsystem_collections.dylib", "/usr/lib/system/libsystem_configuration.dylib", "/usr/lib/system/libsystem_containermanager.dylib", "/usr/lib/system/libsystem_coreservices.dylib", "/usr/lib/system/libsystem_darwin.dylib", "/usr/lib/system/libsystem_dnssd.dylib", "/usr/lib/system/libsystem_featureflags.dylib", "/usr/lib/system/libsystem_info.dylib", "/usr/lib/system/libsystem_m.dylib", "/usr/lib/system/libsystem_malloc.dylib", "/usr/lib/system/libsystem_networkextension.dylib", "/usr/lib/system/libsystem_notify.dylib", "/usr/lib/system/libsystem_product_info_filter.dylib", "/usr/lib/system/libsystem_sandbox.dylib", "/usr/lib/system/libsystem_secinit.dylib", "/usr/lib/system/libsystem_kernel.dylib", "/usr/lib/system/libsystem_platform.dylib", "/usr/lib/system/libsystem_pthread.dylib", "/usr/lib/system/libsystem_symptoms.dylib", "/usr/lib/system/libsystem_trace.dylib", "/usr/lib/system/libunwind.dylib", "/usr/lib/system/libxpc.dylib"]}