const axios = require('axios');
const cheerio = require('cheerio');

async function testAddressLines() {
    try {
        const testUrl = 'https://stores.winmart.vn/vietnam-sieu-thi-winmart-trung-hoa-supermarket-hoang-dao-thuy-cau-giay-390058/Home';
        
        console.log(`Testing address lines from: ${testUrl}`);
        
        const response = await axios.get(testUrl);
        const $ = cheerio.load(response.data);
        
        console.log('\n=== ANALYZING ADDRESS STRUCTURE ===');
        
        // Get the address container
        const addressContainer = $('.store_information_02__text').first();
        console.log(`Address container text: "${addressContainer.text()}"`);
        
        // Try to get HTML structure
        console.log(`\nAddress container HTML:`);
        console.log(addressContainer.html());
        
        // Check if there are separate elements or if we need to split the text
        const addressText = addressContainer.text().trim();
        console.log(`\nFull address text: "${addressText}"`);
        
        // Try different ways to split the address
        console.log('\n=== TRYING DIFFERENT SPLIT METHODS ===');
        
        // Method 1: Split by common patterns
        const patterns = [
            /^(.*?)(Phố.*?)(.*?)$/,
            /^(.*?TTTM.*?)(Phố.*?)(.*?)$/,
            /^(.*?)(Đường.*?|Phố.*?)(.*?)$/
        ];
        
        patterns.forEach((pattern, i) => {
            const match = addressText.match(pattern);
            if (match) {
                console.log(`Pattern ${i+1} match:`);
                console.log(`  Line 1: "${match[1].trim()}"`);
                console.log(`  Line 2: "${match[2].trim()}"`);
                console.log(`  Line 3: "${match[3].trim()}"`);
            }
        });
        
        // Method 2: Split by capital letters (new words)
        console.log('\n=== SPLIT BY CAPITAL LETTERS ===');
        const capitalSplit = addressText.split(/(?=[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ])/);
        capitalSplit.forEach((part, i) => {
            if (part.trim()) {
                console.log(`  Part ${i+1}: "${part.trim()}"`);
            }
        });
        
        // Method 3: Manual parsing based on known structure
        console.log('\n=== MANUAL PARSING ===');
        
        // For "Số 5, Tầng B1, TTTM Ocean Mall Trung HòaPhố Hoàng Đạo ThúyCầu Giấy"
        // Try to identify the 3 parts
        let line1 = '';
        let line2 = '';
        let line3 = '';
        
        // Look for patterns
        const line1Match = addressText.match(/^(.*?TTTM[^A-Z]*)/);
        if (line1Match) {
            line1 = line1Match[1].trim();
            const remaining = addressText.replace(line1, '').trim();
            
            const line2Match = remaining.match(/^(Phố[^A-Z]*)/);
            if (line2Match) {
                line2 = line2Match[1].trim();
                line3 = remaining.replace(line2, '').trim();
            } else {
                // Try other patterns
                const parts = remaining.split(/(?=[A-Z])/);
                if (parts.length >= 2) {
                    line2 = parts[0] || '';
                    line3 = parts.slice(1).join('').trim();
                }
            }
        }
        
        console.log(`Line 1 (Address): "${line1}"`);
        console.log(`Line 2 (Street): "${line2}"`);
        console.log(`Line 3 (District): "${line3}"`);
        
        // Method 4: Try to find specific patterns
        console.log('\n=== PATTERN-BASED PARSING ===');
        
        // Pattern for this specific format
        const specificMatch = addressText.match(/^(.*?)(Phố\s+[^A-Z]*)(.*?)$/);
        if (specificMatch) {
            console.log(`Specific pattern match:`);
            console.log(`  Address: "${specificMatch[1].trim()}"`);
            console.log(`  Street: "${specificMatch[2].trim()}"`);
            console.log(`  District: "${specificMatch[3].trim()}"`);
        }
        
        // Try another pattern
        const pattern2 = addressText.match(/^(.*?TTTM[^P]*)(Phố[^C]*)(Cầu\s+\w+)$/);
        if (pattern2) {
            console.log(`Pattern 2 match:`);
            console.log(`  Address: "${pattern2[1].trim()}"`);
            console.log(`  Street: "${pattern2[2].trim()}"`);
            console.log(`  District: "${pattern2[3].trim()}"`);
        }
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

testAddressLines();
