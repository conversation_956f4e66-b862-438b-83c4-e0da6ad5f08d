const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

class WinmartFullAddressScraper {
    constructor() {
        this.baseUrl = 'https://stores.winmart.vn';
        this.stores = [];
        this.delay = 2000; // 2 seconds delay between requests
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get store list from main pages
    async getStoreList() {
        try {
            console.log('Getting store list from main pages...');

            // Get total pages first
            const response = await axios.get(this.baseUrl);
            const $ = cheerio.load(response.data);

            // Find pagination to get total pages
            let totalPages = 1;
            const paginationLinks = $('a[href*="?page="]');
            paginationLinks.each((i, el) => {
                const href = $(el).attr('href');
                const pageMatch = href.match(/page=(\d+)/);
                if (pageMatch) {
                    const pageNum = parseInt(pageMatch[1]);
                    if (pageNum > totalPages) {
                        totalPages = pageNum;
                    }
                }
            });

            console.log(`Found ${totalPages} pages to scrape`);

            // Get store URLs from all pages
            const storeUrls = new Set();

            for (let page = 1; page <= totalPages; page++) {
                try {
                    const pageUrl = page === 1 ? this.baseUrl : `${this.baseUrl}/?page=${page}`;
                    console.log(`Scraping page ${page}: ${pageUrl}`);

                    const pageResponse = await axios.get(pageUrl);
                    const page$ = cheerio.load(pageResponse.data);

                    // Find all store links
                    const storeLinks = page$('a[href*="/vietnam-"], a[href*="/winmart-"]').filter((i, el) => {
                        const href = page$(el).attr('href') || '';
                        return href.includes('supermarket-');
                    });

                    storeLinks.each((i, element) => {
                        const $link = page$(element);
                        const storeName = $link.text().trim();
                        const storeUrl = $link.attr('href');

                        if (storeName && storeName.includes('WinMart') && storeUrl) {
                            const fullUrl = storeUrl.startsWith('http') ? storeUrl : this.baseUrl + storeUrl;
                            storeUrls.add(fullUrl);
                        }
                    });

                    console.log(`Found ${storeLinks.length} store links on page ${page}`);

                    // Add delay between pages
                    if (page < totalPages) {
                        await this.sleep(1000);
                    }
                } catch (error) {
                    console.error(`Error scraping page ${page}:`, error.message);
                }
            }

            console.log(`Total unique store URLs found: ${storeUrls.size}`);
            return Array.from(storeUrls);

        } catch (error) {
            console.error('Error getting store list:', error.message);
            return [];
        }
    }

    // Get full address from individual store page
    async getStoreDetails(storeUrl) {
        try {
            console.log(`Getting details from: ${storeUrl}`);

            const response = await axios.get(storeUrl);
            const $ = cheerio.load(response.data);

            // Extract store name
            let storeName = '';
            const nameSelectors = [
                'h1',
                '.store-name',
                '.title',
                '[class*="name"]',
                '[class*="title"]'
            ];

            for (const selector of nameSelectors) {
                const nameEl = $(selector).first();
                if (nameEl.length > 0) {
                    const name = nameEl.text().trim();
                    if (name && name.includes('WinMart')) {
                        storeName = name;
                        break;
                    }
                }
            }

            // If no name found, extract from URL
            if (!storeName) {
                const urlMatch = storeUrl.match(/\/([^\/]+)-supermarket-/);
                if (urlMatch) {
                    storeName = urlMatch[1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                }
            }

            // Extract full address from specific class
            let fullAddress = '';

            // First try the specific class we found in testing
            const addressEl = $('.store_information_02__text').first();
            if (addressEl.length > 0) {
                let address = addressEl.text().trim();
                // Clean up the address
                address = address.replace(/\s+/g, ' ').trim();

                // Check if this looks like a real address (not a phone number or other info)
                if (address && address.length > 10 && !address.match(/^\+?\d+$/) && !address.includes('Mở cửa')) {
                    fullAddress = address;
                }
            }

            // If no address found, try other selectors
            if (!fullAddress) {
                const addressSelectors = [
                    '.store_information_02__item-list',
                    '.address',
                    '.location',
                    '[class*="address"]',
                    'p:contains("Địa chỉ")',
                    'div:contains("Địa chỉ")',
                    'span:contains("Địa chỉ")'
                ];

                for (const selector of addressSelectors) {
                    const elements = $(selector);
                    elements.each((i, el) => {
                        let address = $(el).text().trim();
                        // Clean up the address
                        address = address.replace(/^Địa chỉ:?\s*/i, '');
                        address = address.replace(/\s+/g, ' ').trim();

                        // Check if this looks like a real address
                        if (address && address.length > 10 &&
                            !address.match(/^\+?\d+$/) &&
                            !address.includes('Mở cửa') &&
                            !address.includes('Gọi') &&
                            !address.includes('Chỉ đường') &&
                            (address.includes('Số ') || address.includes('Tầng ') || address.includes('TTTM') || address.includes('Đường ') || address.includes('Phố '))) {
                            fullAddress = address;
                            return false; // Break out of each loop
                        }
                    });

                    if (fullAddress) break;
                }
            }

            // If still no address found, try to extract from page text
            if (!fullAddress) {
                const pageText = $('body').text();
                const addressPatterns = [
                    /Địa chỉ:?\s*([^\.]+)/i,
                    /Address:?\s*([^\.]+)/i,
                    /(Số \d+[^\.]+)/,
                    /(Tầng [^\.]+)/,
                    /(TTTM [^\.]+)/
                ];

                for (const pattern of addressPatterns) {
                    const match = pageText.match(pattern);
                    if (match && match[1]) {
                        fullAddress = match[1].trim();
                        break;
                    }
                }
            }

            // Extract phone number
            let phone = '';
            const phoneSelectors = [
                'a[href^="tel:"]',
                '.phone',
                '[class*="phone"]',
                'span:contains("+")',
                'div:contains("+")'
            ];

            for (const selector of phoneSelectors) {
                const phoneEl = $(selector);
                if (phoneEl.length > 0) {
                    if (selector === 'a[href^="tel:"]') {
                        phone = phoneEl.attr('href').replace('tel:', '');
                    } else {
                        const phoneText = phoneEl.text().trim();
                        const phoneMatch = phoneText.match(/(\+84\d{9,10}|\d{10,11})/);
                        if (phoneMatch) {
                            phone = phoneMatch[1];
                        }
                    }
                    if (phone) break;
                }
            }

            // Extract city from URL or page content
            let city = '';
            const cityKeywords = [
                'Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ',
                'Ninh Bình', 'Hạ Long', 'Việt Trì', 'Thái Bình', 'Pleiku',
                'Buôn Ma Thuột', 'Nha Trang', 'Cam Ranh', 'Bạc Liêu', 'Cà Mau',
                'Sóc Trăng', 'Vĩnh Long', 'Trà Vinh', 'Long Xuyên', 'Rạch Giá',
                'Vị Thanh', 'Kỳ Anh', 'Tuy Hòa', 'Lạng Sơn', 'Đồng Hới',
                'Tân An', 'Tây Ninh', 'Bảo Lộc', 'Phủ Lý', 'Thanh Hóa',
                'Quảng Ngãi', 'Hà Tĩnh', 'Bắc Ninh', 'Sa Đéc', 'Cao Lãnh',
                'Móng Cái', 'Hòa Bình', 'Phú Thọ', 'Biên Hòa', 'Dĩ An',
                'Long Thành', 'Mỹ Tho', 'Vũng Tàu', 'Đức Trọng', 'Yên Bái',
                'Sơn La', 'Thái Nguyên', 'Huế', 'Nam Đàn', 'Uông Bí',
                'Chí Linh', 'Tĩnh Gia', 'Tuyên Quang', 'Vinh', 'Kon Tum',
                'Quy Nhơn', 'Lai Châu', 'Ninh Hòa', 'Bắc Kạn', 'Cẩm Phả',
                'Thái Hòa'
            ];

            // Try to find city in URL first
            for (const cityName of cityKeywords) {
                if (storeUrl.toLowerCase().includes(cityName.toLowerCase().replace(/\s/g, '-'))) {
                    city = cityName;
                    break;
                }
            }

            // If not found in URL, try in page content
            if (!city) {
                const pageText = $('body').text();
                for (const cityName of cityKeywords) {
                    if (pageText.includes(cityName)) {
                        city = cityName;
                        break;
                    }
                }
            }

            return {
                name: storeName,
                address: fullAddress,
                city: city,
                phone: phone,
                url: storeUrl
            };

        } catch (error) {
            console.error(`Error getting details for ${storeUrl}:`, error.message);
            return {
                name: '',
                address: '',
                city: '',
                phone: '',
                url: storeUrl
            };
        }
    }

    // Main scraping function
    async scrapeAllStores() {
        try {
            console.log('Starting WinMart full address scraping...');

            // Get all store URLs
            const storeUrls = await this.getStoreList();

            if (storeUrls.length === 0) {
                console.log('No store URLs found');
                return [];
            }

            console.log(`\nGetting detailed information for ${storeUrls.length} stores...`);

            // Get details for each store
            for (let i = 0; i < storeUrls.length; i++) {
                const storeUrl = storeUrls[i];
                console.log(`\nProcessing store ${i + 1}/${storeUrls.length}`);

                const storeDetails = await this.getStoreDetails(storeUrl);

                if (storeDetails.name || storeDetails.address) {
                    this.stores.push(storeDetails);
                    console.log(`✓ Added: ${storeDetails.name}`);
                    console.log(`  Address: ${storeDetails.address}`);
                    console.log(`  City: ${storeDetails.city}`);
                } else {
                    console.log(`✗ Skipped: No valid data found`);
                }

                // Add delay between requests
                if (i < storeUrls.length - 1) {
                    console.log(`Waiting ${this.delay}ms...`);
                    await this.sleep(this.delay);
                }
            }

            console.log(`\nCompleted! Found ${this.stores.length} stores with full addresses.`);
            return this.stores;

        } catch (error) {
            console.error('Error in scrapeAllStores:', error.message);
            return this.stores;
        }
    }

    // Save to JSON
    saveToJson(filename = 'winmart_full_addresses.json') {
        try {
            fs.writeFileSync(filename, JSON.stringify(this.stores, null, 2), 'utf8');
            console.log(`\nStores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving JSON:', error.message);
        }
    }

    // Save to CSV
    saveToCsv(filename = 'winmart_full_addresses.csv') {
        try {
            if (this.stores.length === 0) {
                console.log('No stores to save');
                return;
            }

            const headers = ['Name', 'Address', 'City', 'Phone', 'URL'];
            const csvContent = [
                headers.join(','),
                ...this.stores.map(store => [
                    `"${store.name || ''}"`,
                    `"${store.address || ''}"`,
                    `"${store.city || ''}"`,
                    `"${store.phone || ''}"`,
                    `"${store.url || ''}"`
                ].join(','))
            ].join('\n');

            fs.writeFileSync(filename, csvContent, 'utf8');
            console.log(`Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving CSV:', error.message);
        }
    }
}

// Usage
async function main() {
    const scraper = new WinmartFullAddressScraper();

    try {
        // Scrape all stores with full addresses
        const stores = await scraper.scrapeAllStores();

        // Save results
        scraper.saveToJson('winmart_full_addresses.json');
        scraper.saveToCsv('winmart_full_addresses.csv');

        console.log(`\nScraping completed! Found ${stores.length} stores.`);
        console.log('Files saved:');
        console.log('- winmart_full_addresses.json');
        console.log('- winmart_full_addresses.csv');

    } catch (error) {
        console.error('Scraping failed:', error.message);
    }
}

// Export for use as module
module.exports = WinmartFullAddressScraper;

// Run if called directly
if (require.main === module) {
    main();
}
