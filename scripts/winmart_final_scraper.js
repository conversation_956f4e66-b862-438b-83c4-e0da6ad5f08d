const axios = require('axios');
const fs = require('fs');

class WinmartFinalScraper {
    constructor() {
        this.baseApiUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1';
        this.stores = [];
        this.delay = 2000; // 2 seconds delay between provinces
        this.pageDelay = 500; // 0.5 second delay between pages
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get all provinces
    async getProvinces() {
        try {
            console.log('Getting all provinces...');
            const response = await axios.get(`${this.baseApiUrl}/provinces/all-winmart`);
            
            if (response.data && response.data.data) {
                console.log(`✓ Found ${response.data.data.length} provinces`);
                return response.data.data;
            }
            
            return [];
        } catch (error) {
            console.error('Error getting provinces:', error.message);
            return [];
        }
    }

    // Get all stores for a specific province using correct pagination
    async getStoresByProvince(provinceCode, provinceName) {
        try {
            console.log(`\nGetting stores for: ${provinceName} (${provinceCode})`);
            
            const allStores = [];
            let pageNumber = 1;
            let totalPages = 1;
            
            // Get first page to determine total pages
            const firstResponse = await axios.get(`${this.baseApiUrl}/store-by-province`, {
                params: {
                    PageNumber: 1,
                    PageSize: 1, // Get 1 district per page as requested
                    ProvinceCode: provinceCode
                }
            });
            
            if (firstResponse.data && firstResponse.data.paging) {
                totalPages = firstResponse.data.paging.totalPages || 1;
                console.log(`  Total pages to fetch: ${totalPages}`);
            }
            
            // Process first page
            this.extractStoresFromResponse(firstResponse.data, allStores);
            
            // Get remaining pages
            for (let page = 2; page <= totalPages; page++) {
                console.log(`  Fetching page ${page}/${totalPages}...`);
                
                const response = await axios.get(`${this.baseApiUrl}/store-by-province`, {
                    params: {
                        PageNumber: page,
                        PageSize: 1,
                        ProvinceCode: provinceCode
                    }
                });
                
                this.extractStoresFromResponse(response.data, allStores);
                
                // Add delay between pages
                if (page < totalPages) {
                    await this.sleep(this.pageDelay);
                }
            }
            
            console.log(`✓ Found ${allStores.length} stores in ${provinceName}`);
            return allStores;
            
        } catch (error) {
            console.error(`Error getting stores for ${provinceCode}:`, error.message);
            return [];
        }
    }

    // Extract stores from API response
    extractStoresFromResponse(responseData, storesArray) {
        if (!responseData || !responseData.data) return;
        
        responseData.data.forEach(district => {
            if (district.wardStores) {
                district.wardStores.forEach(ward => {
                    if (ward.stores && ward.stores.length > 0) {
                        ward.stores.forEach(store => {
                            const cleanStore = {
                                storeCode: store.storeCode || '',
                                storeName: store.storeName || '',
                                address: store.officeAddress || '',
                                ward: store.wardName || '',
                                district: store.districtName || '',
                                province: store.provinceName || '',
                                phone: store.contactMobile || '',
                                latitude: store.latitude || null,
                                longitude: store.longitude || null,
                                isOpen: store.isOpen || false,
                                activeStatus: store.activeStatus || '',
                                supportDelivering: store.supportDelivering || false,
                                deliveryStatus: store.deliveryStatus || '',
                                chainId: store.chainId || '',
                                kindOfStore: store.kindOfStore || '',
                                isDeliveryTime: store.isDeliveryTime || false,
                                allowCod: store.allowCod || false
                            };
                            
                            storesArray.push(cleanStore);
                        });
                    }
                });
            }
        });
    }

    // Main scraping function
    async scrapeAllStores() {
        try {
            console.log('Starting WinMart API scraping with PageSize=1...\n');
            
            // Get all provinces
            const provinces = await this.getProvinces();
            
            if (provinces.length === 0) {
                console.log('No provinces found');
                return [];
            }
            
            console.log(`Processing ${provinces.length} provinces...\n`);
            
            // Get stores for each province
            for (let i = 0; i < provinces.length; i++) {
                const province = provinces[i];
                console.log(`[${i + 1}/${provinces.length}] Processing: ${province.description || province.code}`);
                
                const provinceStores = await this.getStoresByProvince(province.code, province.description);
                this.stores.push(...provinceStores);
                
                // Add delay between provinces
                if (i < provinces.length - 1) {
                    console.log(`  Waiting ${this.delay}ms before next province...`);
                    await this.sleep(this.delay);
                }
            }
            
            console.log(`\n✅ Completed! Total stores found: ${this.stores.length}`);
            return this.stores;
            
        } catch (error) {
            console.error('Error in scrapeAllStores:', error.message);
            return this.stores;
        }
    }

    // Get statistics
    getStatistics() {
        const stats = {
            totalStores: this.stores.length,
            byProvince: {},
            byStatus: {},
            byChain: {},
            withCoordinates: 0,
            withPhone: 0,
            openStores: 0
        };
        
        this.stores.forEach(store => {
            // By province
            const province = store.province || 'Unknown';
            stats.byProvince[province] = (stats.byProvince[province] || 0) + 1;
            
            // By status
            const status = store.activeStatus || 'Unknown';
            stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
            
            // By chain
            const chain = store.chainId || 'Unknown';
            stats.byChain[chain] = (stats.byChain[chain] || 0) + 1;
            
            // With coordinates
            if (store.latitude && store.longitude) {
                stats.withCoordinates++;
            }
            
            // With phone
            if (store.phone) {
                stats.withPhone++;
            }
            
            // Open stores
            if (store.isOpen) {
                stats.openStores++;
            }
        });
        
        return stats;
    }

    // Filter only WinMart stores (exclude WM+)
    filterWinMartOnly() {
        const winmartStores = this.stores.filter(store => {
            const name = store.storeName.toLowerCase();
            return name.includes('winmart') && !name.includes('wm+');
        });
        
        console.log(`Filtered ${winmartStores.length} WinMart stores from ${this.stores.length} total stores`);
        return winmartStores;
    }

    // Save to JSON
    saveToJson(filename = 'winmart_complete_data.json') {
        try {
            fs.writeFileSync(filename, JSON.stringify(this.stores, null, 2), 'utf8');
            console.log(`✓ Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving JSON:', error.message);
        }
    }

    // Save to CSV
    saveToCsv(filename = 'winmart_complete_data.csv') {
        try {
            if (this.stores.length === 0) {
                console.log('No stores to save');
                return;
            }

            const headers = [
                'Store Code', 'Store Name', 'Address', 'Ward', 'District', 'Province',
                'Phone', 'Latitude', 'Longitude', 'Is Open', 'Active Status',
                'Support Delivering', 'Delivery Status', 'Chain ID', 'Kind Of Store',
                'Is Delivery Time', 'Allow COD'
            ];
            
            const csvContent = [
                headers.join(','),
                ...this.stores.map(store => [
                    `"${store.storeCode || ''}"`,
                    `"${store.storeName || ''}"`,
                    `"${store.address || ''}"`,
                    `"${store.ward || ''}"`,
                    `"${store.district || ''}"`,
                    `"${store.province || ''}"`,
                    `"${store.phone || ''}"`,
                    store.latitude || '',
                    store.longitude || '',
                    store.isOpen || false,
                    `"${store.activeStatus || ''}"`,
                    store.supportDelivering || false,
                    `"${store.deliveryStatus || ''}"`,
                    `"${store.chainId || ''}"`,
                    `"${store.kindOfStore || ''}"`,
                    store.isDeliveryTime || false,
                    store.allowCod || false
                ].join(','))
            ].join('\n');

            fs.writeFileSync(filename, csvContent, 'utf8');
            console.log(`✓ Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving CSV:', error.message);
        }
    }

    // Print summary
    printSummary() {
        const stats = this.getStatistics();
        
        console.log('\n📊 SCRAPING SUMMARY:');
        console.log(`Total stores: ${stats.totalStores}`);
        console.log(`Open stores: ${stats.openStores}`);
        console.log(`Stores with coordinates: ${stats.withCoordinates}`);
        console.log(`Stores with phone: ${stats.withPhone}`);
        
        console.log('\nTop 10 provinces by store count:');
        const sortedProvinces = Object.entries(stats.byProvince)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);
        
        sortedProvinces.forEach(([province, count]) => {
            console.log(`  ${province}: ${count} stores`);
        });
        
        console.log('\nStore chains:');
        Object.entries(stats.byChain).forEach(([chain, count]) => {
            console.log(`  ${chain}: ${count} stores`);
        });
        
        console.log('\nStore status distribution:');
        Object.entries(stats.byStatus).forEach(([status, count]) => {
            console.log(`  ${status}: ${count} stores`);
        });
    }
}

// Usage
async function main() {
    const scraper = new WinmartFinalScraper();
    
    try {
        // Scrape all stores
        const stores = await scraper.scrapeAllStores();
        
        if (stores.length === 0) {
            console.log('No stores found');
            return;
        }
        
        // Print summary
        scraper.printSummary();
        
        // Save all data
        scraper.saveToJson('winmart_all_stores.json');
        scraper.saveToCsv('winmart_all_stores.csv');
        
        // Filter and save only WinMart stores (not WM+)
        const winmartStores = scraper.filterWinMartOnly();
        if (winmartStores.length > 0) {
            const originalStores = scraper.stores;
            scraper.stores = winmartStores;
            
            scraper.saveToJson('winmart_stores_only.json');
            scraper.saveToCsv('winmart_stores_only.csv');
            
            scraper.stores = originalStores;
        }
        
        console.log('\n🎉 Scraping completed successfully!');
        console.log('\nFiles created:');
        console.log('- winmart_all_stores.json (all stores including WM+)');
        console.log('- winmart_all_stores.csv (all stores including WM+)');
        console.log('- winmart_stores_only.json (WinMart stores only)');
        console.log('- winmart_stores_only.csv (WinMart stores only)');
        
    } catch (error) {
        console.error('Scraping failed:', error.message);
    }
}

// Export for use as module
module.exports = WinmartFinalScraper;

// Run if called directly
if (require.main === module) {
    main();
}
