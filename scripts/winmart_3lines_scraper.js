const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

class WinmartThreeLinesScraper {
    constructor() {
        this.baseUrl = 'https://stores.winmart.vn';
        this.stores = [];
        this.delay = 2000;
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get store list from main pages
    async getStoreList() {
        try {
            console.log('Getting store list from main pages...');

            const response = await axios.get(this.baseUrl);
            const $ = cheerio.load(response.data);

            // Find pagination
            let totalPages = 1;
            const paginationLinks = $('a[href*="?page="]');
            paginationLinks.each((i, el) => {
                const href = $(el).attr('href');
                const pageMatch = href.match(/page=(\d+)/);
                if (pageMatch) {
                    const pageNum = parseInt(pageMatch[1]);
                    if (pageNum > totalPages) {
                        totalPages = pageNum;
                    }
                }
            });

            console.log(`Found ${totalPages} pages to scrape`);

            const storeUrls = new Set();

            for (let page = 1; page <= totalPages; page++) {
                try {
                    const pageUrl = page === 1 ? this.baseUrl : `${this.baseUrl}/?page=${page}`;
                    console.log(`Scraping page ${page}: ${pageUrl}`);

                    const pageResponse = await axios.get(pageUrl);
                    const page$ = cheerio.load(pageResponse.data);

                    const storeLinks = page$('a[href*="/vietnam-"], a[href*="/winmart-"]').filter((i, el) => {
                        const href = page$(el).attr('href') || '';
                        return href.includes('supermarket-');
                    });

                    storeLinks.each((i, element) => {
                        const $link = page$(element);
                        const storeName = $link.text().trim();
                        const storeUrl = $link.attr('href');

                        if (storeName && storeName.includes('WinMart') && storeUrl) {
                            const fullUrl = storeUrl.startsWith('http') ? storeUrl : this.baseUrl + storeUrl;
                            storeUrls.add(fullUrl);
                        }
                    });

                    console.log(`Found ${storeLinks.length} store links on page ${page}`);

                    if (page < totalPages) {
                        await this.sleep(1000);
                    }
                } catch (error) {
                    console.error(`Error scraping page ${page}:`, error.message);
                }
            }

            console.log(`Total unique store URLs found: ${storeUrls.size}`);
            return Array.from(storeUrls);

        } catch (error) {
            console.error('Error getting store list:', error.message);
            return [];
        }
    }

    // Get store details with 3 separate address lines
    async getStoreDetails(storeUrl) {
        try {
            console.log(`Getting details from: ${storeUrl}`);

            const response = await axios.get(storeUrl);
            const $ = cheerio.load(response.data);

            // Extract store name
            let storeName = '';
            const nameEl = $('h1').first();
            if (nameEl.length > 0) {
                storeName = nameEl.text().trim();
            }

            // Extract 3 address lines from the address element
            let addressLine1 = '';
            let addressLine2 = '';
            let addressLine3 = '';

            const addressContainer = $('.store_information_02__text').first();
            if (addressContainer.length > 0) {
                // Get all span elements within the address
                const spans = addressContainer.find('span');

                if (spans.length >= 3) {
                    // Extract the 3 unique lines from spans
                    // Based on the HTML structure: there are 5 spans but they repeat
                    // Span 1: Address line
                    // Span 2 & 3: Street/Ward line (duplicated)
                    // Span 4 & 5: District line (duplicated)

                    addressLine1 = $(spans[0]).text().trim(); // First span - address
                    addressLine2 = $(spans[1]).text().trim(); // Second span - street/ward

                    // For the third line, take the last unique span
                    if (spans.length >= 4) {
                        addressLine3 = $(spans[3]).text().trim(); // Fourth span - district
                    } else {
                        addressLine3 = $(spans[2]).text().trim(); // Fallback to third span
                    }

                    // Clean up duplicates - if line 2 and 3 are the same, try to get different content
                    if (addressLine2 === addressLine3 && spans.length >= 5) {
                        addressLine3 = $(spans[4]).text().trim();
                    }
                } else {
                    // Method 2: Parse the combined text
                    const fullText = addressContainer.text().trim();

                    // Use regex to split the address into 3 parts
                    const match = fullText.match(/^(.*?TTTM[^P]*)(Phố[^C]*)(Cầu\s+\w+.*?)$/);
                    if (match) {
                        addressLine1 = match[1].trim();
                        addressLine2 = match[2].trim();
                        addressLine3 = match[3].trim();
                    } else {
                        // Fallback: try another pattern
                        const match2 = fullText.match(/^(.*?)(Phố\s+[^A-Z]*)(.*?)$/);
                        if (match2) {
                            addressLine1 = match2[1].trim();
                            addressLine2 = match2[2].trim();
                            addressLine3 = match2[3].trim();
                        } else {
                            // Last resort: put everything in line 1
                            addressLine1 = fullText;
                        }
                    }
                }
            }

            // Extract phone
            let phone = '';
            const phoneLink = $('a[href^="tel:"]').first();
            if (phoneLink.length > 0) {
                phone = phoneLink.attr('href').replace('tel:', '');
            }

            // Extract city from URL
            let city = '';
            const cityKeywords = [
                'Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ',
                'Ninh Bình', 'Hạ Long', 'Việt Trì', 'Thái Bình', 'Pleiku',
                'Buôn Ma Thuột', 'Nha Trang', 'Cam Ranh', 'Bạc Liêu', 'Cà Mau',
                'Sóc Trăng', 'Vĩnh Long', 'Trà Vinh', 'Long Xuyên', 'Rạch Giá',
                'Vị Thanh', 'Kỳ Anh', 'Tuy Hòa', 'Lạng Sơn', 'Đồng Hới',
                'Tân An', 'Tây Ninh', 'Bảo Lộc', 'Phủ Lý', 'Thanh Hóa',
                'Quảng Ngãi', 'Hà Tĩnh', 'Bắc Ninh', 'Sa Đéc', 'Cao Lãnh',
                'Móng Cái', 'Hòa Bình', 'Phú Thọ', 'Biên Hòa', 'Dĩ An',
                'Long Thành', 'Mỹ Tho', 'Vũng Tàu', 'Đức Trọng', 'Yên Bái',
                'Sơn La', 'Thái Nguyên', 'Huế', 'Nam Đàn', 'Uông Bí',
                'Chí Linh', 'Tĩnh Gia', 'Tuyên Quang', 'Vinh', 'Kon Tum',
                'Quy Nhơn', 'Lai Châu', 'Ninh Hòa', 'Bắc Kạn', 'Cẩm Phả',
                'Thái Hòa'
            ];

            for (const cityName of cityKeywords) {
                if (storeUrl.toLowerCase().includes(cityName.toLowerCase().replace(/\s/g, '-'))) {
                    city = cityName;
                    break;
                }
            }

            const result = {
                name: storeName,
                address: addressLine1,
                street: addressLine2,
                district: addressLine3,
                city: city,
                phone: phone,
                url: storeUrl
            };

            console.log(`✓ Name: ${result.name}`);
            console.log(`✓ Address: ${result.address}`);
            console.log(`✓ Street: ${result.street}`);
            console.log(`✓ District: ${result.district}`);
            console.log(`✓ City: ${result.city}`);
            console.log(`✓ Phone: ${result.phone}`);

            return result;

        } catch (error) {
            console.error(`Error getting details for ${storeUrl}:`, error.message);
            return {
                name: '',
                address: '',
                street: '',
                district: '',
                city: '',
                phone: '',
                url: storeUrl
            };
        }
    }

    // Main scraping function
    async scrapeAllStores() {
        try {
            console.log('Starting WinMart 3-lines address scraping...');

            const storeUrls = await this.getStoreList();

            if (storeUrls.length === 0) {
                console.log('No store URLs found');
                return [];
            }

            console.log(`\nGetting detailed information for ${storeUrls.length} stores...`);

            for (let i = 0; i < storeUrls.length; i++) {
                const storeUrl = storeUrls[i];
                console.log(`\nProcessing store ${i + 1}/${storeUrls.length}`);

                const storeDetails = await this.getStoreDetails(storeUrl);

                if (storeDetails.name || storeDetails.address) {
                    this.stores.push(storeDetails);
                } else {
                    console.log(`✗ Skipped: No valid data found`);
                }

                if (i < storeUrls.length - 1) {
                    console.log(`Waiting ${this.delay}ms...`);
                    await this.sleep(this.delay);
                }
            }

            console.log(`\nCompleted! Found ${this.stores.length} stores.`);
            return this.stores;

        } catch (error) {
            console.error('Error in scrapeAllStores:', error.message);
            return this.stores;
        }
    }

    // Save to JSON
    saveToJson(filename = 'winmart_3lines.json') {
        try {
            fs.writeFileSync(filename, JSON.stringify(this.stores, null, 2), 'utf8');
            console.log(`\nStores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving JSON:', error.message);
        }
    }

    // Save to CSV
    saveToCsv(filename = 'winmart_3lines.csv') {
        try {
            if (this.stores.length === 0) {
                console.log('No stores to save');
                return;
            }

            const headers = ['Name', 'Address', 'Street', 'District', 'City', 'Phone', 'URL'];
            const csvContent = [
                headers.join(','),
                ...this.stores.map(store => [
                    `"${store.name || ''}"`,
                    `"${store.address || ''}"`,
                    `"${store.street || ''}"`,
                    `"${store.district || ''}"`,
                    `"${store.city || ''}"`,
                    `"${store.phone || ''}"`,
                    `"${store.url || ''}"`
                ].join(','))
            ].join('\n');

            fs.writeFileSync(filename, csvContent, 'utf8');
            console.log(`Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving CSV:', error.message);
        }
    }
}

// Usage
async function main() {
    const scraper = new WinmartThreeLinesScraper();

    try {
        const stores = await scraper.scrapeAllStores();

        scraper.saveToJson('winmart_3lines.json');
        scraper.saveToCsv('winmart_3lines.csv');

        console.log(`\nScraping completed! Found ${stores.length} stores.`);
        console.log('Files saved:');
        console.log('- winmart_3lines.json');
        console.log('- winmart_3lines.csv');

    } catch (error) {
        console.error('Scraping failed:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = WinmartThreeLinesScraper;
