const axios = require('axios');
const fs = require('fs');

class WinmartAPIScraper {
    constructor() {
        this.baseApiUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1';
        this.stores = [];
        this.delay = 2000; // 2 seconds delay between provinces
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get all provinces
    async getProvinces() {
        try {
            console.log('Getting all provinces...');
            const response = await axios.get(`${this.baseApiUrl}/provinces/all-winmart`);

            if (response.data && response.data.data) {
                console.log(`✓ Found ${response.data.data.length} provinces`);
                return response.data.data;
            }

            return [];
        } catch (error) {
            console.error('Error getting provinces:', error.message);
            return [];
        }
    }

    // Get stores for a specific province with pagination
    async getStoresByProvince(provinceCode) {
        try {
            console.log(`Getting stores for province: ${provinceCode}`);

            const allStores = [];
            let pageNumber = 1;
            let hasMorePages = true;

            while (hasMorePages) {
                console.log(`  Fetching page ${pageNumber} for ${provinceCode}...`);

                const response = await axios.get(`${this.baseApiUrl}/store-by-province`, {
                    params: {
                        PageNumber: pageNumber,
                        PageSize: 1,
                        ProvinceCode: provinceCode
                    }
                });

                if (response.data && response.data.data && response.data.data.length > 0) {
                    // Extract stores from the nested structure
                    response.data.data.forEach(district => {
                        if (district.wardStores) {
                            district.wardStores.forEach(ward => {
                                if (ward.stores && ward.stores.length > 0) {
                                    ward.stores.forEach(store => {
                                        // Clean and format store data
                                        const cleanStore = {
                                            storeCode: store.storeCode || '',
                                            storeName: store.storeName || '',
                                            address: store.officeAddress || '',
                                            ward: store.wardName || '',
                                            district: store.districtName || '',
                                            province: store.provinceName || '',
                                            phone: store.contactMobile || '',
                                            latitude: store.latitude || null,
                                            longitude: store.longitude || null,
                                            isOpen: store.isOpen || false,
                                            activeStatus: store.activeStatus || '',
                                            supportDelivering: store.supportDelivering || false,
                                            chainId: store.chainId || ''
                                        };

                                        allStores.push(cleanStore);
                                    });
                                }
                            });
                        }
                    });

                    // Check pagination info
                    if (response.data.paging) {
                        const totalPages = response.data.paging.totalPage || 1;
                        console.log(`    Page ${pageNumber}/${totalPages} - Found ${response.data.data.length} districts`);

                        if (pageNumber >= totalPages) {
                            hasMorePages = false;
                        } else {
                            pageNumber++;
                            // Add delay between page requests
                            await this.sleep(500); // 0.5 second between pages
                        }
                    } else {
                        // No pagination info, assume single page
                        hasMorePages = false;
                    }
                } else {
                    // No more data
                    hasMorePages = false;
                }
            }

            console.log(`✓ Found ${allStores.length} total stores in ${provinceCode}`);
            return allStores;

        } catch (error) {
            console.error(`Error getting stores for ${provinceCode}:`, error.message);
            return [];
        }
    }

    // Main scraping function
    async scrapeAllStores() {
        try {
            console.log('Starting WinMart API scraping...\n');

            // Get all provinces
            const provinces = await this.getProvinces();

            if (provinces.length === 0) {
                console.log('No provinces found');
                return [];
            }

            console.log(`\nProcessing ${provinces.length} provinces...\n`);

            // Get stores for each province
            for (let i = 0; i < provinces.length; i++) {
                const province = provinces[i];
                console.log(`\n[${i + 1}/${provinces.length}] Processing: ${province.name || province.code}`);

                const provinceStores = await this.getStoresByProvince(province.code);
                this.stores.push(...provinceStores);

                // Add delay between requests
                if (i < provinces.length - 1) {
                    console.log(`Waiting ${this.delay}ms...`);
                    await this.sleep(this.delay);
                }
            }

            console.log(`\n✅ Completed! Total stores found: ${this.stores.length}`);
            return this.stores;

        } catch (error) {
            console.error('Error in scrapeAllStores:', error.message);
            return this.stores;
        }
    }

    // Filter only active WinMart stores
    filterActiveStores() {
        const activeStores = this.stores.filter(store => {
            // Filter for WinMart stores (not WM+ or other chains)
            const isWinMart = store.storeName.toLowerCase().includes('winmart') &&
                             !store.storeName.toLowerCase().includes('wm+');

            // You can also filter by activeStatus if needed
            // const isActive = store.activeStatus === 'Mở cửa';

            return isWinMart;
        });

        console.log(`Filtered ${activeStores.length} WinMart stores from ${this.stores.length} total stores`);
        return activeStores;
    }

    // Get statistics
    getStatistics() {
        const stats = {
            totalStores: this.stores.length,
            byProvince: {},
            byStatus: {},
            withCoordinates: 0,
            withPhone: 0
        };

        this.stores.forEach(store => {
            // By province
            const province = store.province || 'Unknown';
            stats.byProvince[province] = (stats.byProvince[province] || 0) + 1;

            // By status
            const status = store.activeStatus || 'Unknown';
            stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;

            // With coordinates
            if (store.latitude && store.longitude) {
                stats.withCoordinates++;
            }

            // With phone
            if (store.phone) {
                stats.withPhone++;
            }
        });

        return stats;
    }

    // Save to JSON
    saveToJson(filename = 'winmart_api_stores.json') {
        try {
            fs.writeFileSync(filename, JSON.stringify(this.stores, null, 2), 'utf8');
            console.log(`\n✓ Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving JSON:', error.message);
        }
    }

    // Save to CSV
    saveToCsv(filename = 'winmart_api_stores.csv') {
        try {
            if (this.stores.length === 0) {
                console.log('No stores to save');
                return;
            }

            const headers = [
                'Store Code', 'Store Name', 'Address', 'Ward', 'District', 'Province',
                'Phone', 'Latitude', 'Longitude', 'Is Open', 'Active Status',
                'Support Delivering', 'Chain ID'
            ];

            const csvContent = [
                headers.join(','),
                ...this.stores.map(store => [
                    `"${store.storeCode || ''}"`,
                    `"${store.storeName || ''}"`,
                    `"${store.address || ''}"`,
                    `"${store.ward || ''}"`,
                    `"${store.district || ''}"`,
                    `"${store.province || ''}"`,
                    `"${store.phone || ''}"`,
                    store.latitude || '',
                    store.longitude || '',
                    store.isOpen || false,
                    `"${store.activeStatus || ''}"`,
                    store.supportDelivering || false,
                    `"${store.chainId || ''}"`
                ].join(','))
            ].join('\n');

            fs.writeFileSync(filename, csvContent, 'utf8');
            console.log(`✓ Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving CSV:', error.message);
        }
    }

    // Save statistics
    saveStatistics(filename = 'winmart_statistics.json') {
        try {
            const stats = this.getStatistics();
            fs.writeFileSync(filename, JSON.stringify(stats, null, 2), 'utf8');
            console.log(`✓ Statistics saved to ${filename}`);

            // Print summary
            console.log('\n📊 STATISTICS SUMMARY:');
            console.log(`Total stores: ${stats.totalStores}`);
            console.log(`Stores with coordinates: ${stats.withCoordinates}`);
            console.log(`Stores with phone: ${stats.withPhone}`);

            console.log('\nTop provinces by store count:');
            const sortedProvinces = Object.entries(stats.byProvince)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10);

            sortedProvinces.forEach(([province, count]) => {
                console.log(`  ${province}: ${count} stores`);
            });

            console.log('\nStore status distribution:');
            Object.entries(stats.byStatus).forEach(([status, count]) => {
                console.log(`  ${status}: ${count} stores`);
            });

        } catch (error) {
            console.error('Error saving statistics:', error.message);
        }
    }
}

// Usage
async function main() {
    const scraper = new WinmartAPIScraper();

    try {
        // Scrape all stores
        const stores = await scraper.scrapeAllStores();

        if (stores.length === 0) {
            console.log('No stores found');
            return;
        }

        // Save all data
        scraper.saveToJson('winmart_api_all_stores.json');
        scraper.saveToCsv('winmart_api_all_stores.csv');

        // Filter and save only WinMart stores (not WM+)
        const winmartStores = scraper.filterActiveStores();
        if (winmartStores.length > 0) {
            // Temporarily replace stores with filtered ones for saving
            const originalStores = scraper.stores;
            scraper.stores = winmartStores;

            scraper.saveToJson('winmart_stores_only.json');
            scraper.saveToCsv('winmart_stores_only.csv');

            // Restore original stores
            scraper.stores = originalStores;
        }

        // Save statistics
        scraper.saveStatistics();

        console.log('\n🎉 Scraping completed successfully!');
        console.log('\nFiles created:');
        console.log('- winmart_api_all_stores.json (all stores including WM+)');
        console.log('- winmart_api_all_stores.csv (all stores including WM+)');
        console.log('- winmart_stores_only.json (WinMart stores only)');
        console.log('- winmart_stores_only.csv (WinMart stores only)');
        console.log('- winmart_statistics.json (statistics)');

    } catch (error) {
        console.error('Scraping failed:', error.message);
    }
}

// Export for use as module
module.exports = WinmartAPIScraper;

// Run if called directly
if (require.main === module) {
    main();
}
