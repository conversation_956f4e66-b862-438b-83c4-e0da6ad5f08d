const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

// Sample store URLs to test
const sampleStores = [
    'https://stores.winmart.vn/vietnam-sieu-thi-winmart-trung-hoa-supermarket-hoang-dao-thuy-cau-giay-390058/Home',
    'https://stores.winmart.vn/winmart-supermarket-ninh-binh-tan-thanh-ninh-binh-vietnam-390059/Home',
    'https://stores.winmart.vn/vietnam-sieu-thi-winmart-ha-long-supermarket-bach-dang-ha-long-390060/Home',
    'https://stores.winmart.vn/winmart-da-nang-supermarket-an-hai-bac-son-tra-vietnam/Home',
    'https://stores.winmart.vn/vietnam-sieu-thi-winmart-quang-trung-supermarket-phuong-10-ho-chi-minh-390062/Home'
];

async function getStoreDetails(storeUrl) {
    try {
        console.log(`\nGetting details from: ${storeUrl}`);
        
        const response = await axios.get(storeUrl);
        const $ = cheerio.load(response.data);
        
        // Extract store name
        let storeName = '';
        const nameEl = $('h1').first();
        if (nameEl.length > 0) {
            storeName = nameEl.text().trim();
        }
        
        // Extract full address from specific class
        let fullAddress = '';
        const addressEl = $('.store_information_02__text').first();
        if (addressEl.length > 0) {
            let address = addressEl.text().trim();
            address = address.replace(/\s+/g, ' ').trim();
            
            // Check if this looks like a real address
            if (address && address.length > 10 && !address.match(/^\+?\d+$/) && !address.includes('Mở cửa')) {
                fullAddress = address;
            }
        }
        
        // Extract phone
        let phone = '';
        const phoneLink = $('a[href^="tel:"]').first();
        if (phoneLink.length > 0) {
            phone = phoneLink.attr('href').replace('tel:', '');
        }
        
        // Extract city from URL
        let city = '';
        const cityKeywords = [
            'Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ',
            'Ninh Bình', 'Hạ Long', 'Việt Trì', 'Thái Bình', 'Pleiku',
            'Buôn Ma Thuột', 'Nha Trang', 'Cam Ranh', 'Bạc Liêu', 'Cà Mau'
        ];
        
        for (const cityName of cityKeywords) {
            if (storeUrl.toLowerCase().includes(cityName.toLowerCase().replace(/\s/g, '-'))) {
                city = cityName;
                break;
            }
        }
        
        const result = {
            name: storeName,
            address: fullAddress,
            city: city,
            phone: phone,
            url: storeUrl
        };
        
        console.log(`✓ Name: ${result.name}`);
        console.log(`✓ Address: ${result.address}`);
        console.log(`✓ City: ${result.city}`);
        console.log(`✓ Phone: ${result.phone}`);
        
        return result;
        
    } catch (error) {
        console.error(`Error getting details for ${storeUrl}:`, error.message);
        return {
            name: '',
            address: '',
            city: '',
            phone: '',
            url: storeUrl
        };
    }
}

async function main() {
    console.log('Testing address extraction from sample stores...\n');
    
    const results = [];
    
    for (let i = 0; i < sampleStores.length; i++) {
        const storeUrl = sampleStores[i];
        const storeDetails = await getStoreDetails(storeUrl);
        results.push(storeDetails);
        
        // Add delay between requests
        if (i < sampleStores.length - 1) {
            console.log('Waiting 2 seconds...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    // Save results
    fs.writeFileSync('sample_addresses.json', JSON.stringify(results, null, 2), 'utf8');
    
    console.log(`\n=== SUMMARY ===`);
    console.log(`Processed ${results.length} stores`);
    console.log(`Results saved to sample_addresses.json`);
    
    // Show results
    results.forEach((store, i) => {
        console.log(`\n${i+1}. ${store.name}`);
        console.log(`   Address: ${store.address}`);
        console.log(`   City: ${store.city}`);
        console.log(`   Phone: ${store.phone}`);
    });
}

main().catch(console.error);
