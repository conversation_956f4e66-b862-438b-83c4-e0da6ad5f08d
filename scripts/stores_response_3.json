{"provinceName": "T<PERSON><PERSON> <PERSON>", "provinceSlug": "thanh-pho-ha-noi", "data": [{"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101132", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101101", "wardName": "TT. T<PERSON> Đằng", "stores": [{"storeCode": "GH35", "storeName": "[Block] WM+ HNI Gian hàng hchợ 2 (U", "officeAddress": "T<PERSON><PERSON> <PERSON><PERSON> Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. T<PERSON> Đằng", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6050", "storeName": "WM+ HNI 188 Quảng Oai", "officeAddress": "<PERSON><PERSON> nhà 188 đườ<PERSON>, <PERSON><PERSON><PERSON> trấn Tâ<PERSON> Đằng, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStore": "", "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. T<PERSON> Đằng", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101102", "wardName": "X. Ba Trại", "stores": [{"storeCode": "6849", "storeName": "WM+ HNI Ba Trại, Ba Vì", "officeAddress": "Thôn 5, <PERSON><PERSON>, Huyện Ba Vì TP. <PERSON><PERSON> Nội Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "X. Ba Trại", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101103", "wardName": "X. <PERSON>", "stores": [{"storeCode": "GH32", "storeName": "WM+ HNI Gian hàng hội chợ 1 (Urban)", "officeAddress": "T<PERSON><PERSON> <PERSON><PERSON> Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "X. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101104", "wardName": "<PERSON><PERSON> <PERSON><PERSON>", "stores": [{"storeCode": "6866", "storeName": "WM+ H<PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON><PERSON>ệ<PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> <PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101105", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101106", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101107", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5741", "storeName": "WM+ HNI 329 Phố Mới, Ba Vì", "officeAddress": "Số 329 <PERSON><PERSON>, th<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.192472, "longitude": 105.43203, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101107", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101110", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101111", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101113", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101115", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101116", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101117", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5952", "storeName": "WM+ HNI <PERSON>ơn, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101120", "wardName": "X. <PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101121", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101122", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5662", "storeName": "WM+ HNI <PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.114, "longitude": 105.407, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101122", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6676", "storeName": "WM+ H<PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101122", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101123", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101124", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101125", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6076", "storeName": "WM+ <PERSON><PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101125", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101126", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101127", "wardName": "<PERSON><PERSON> Bạt", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101128", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101129", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6072", "storeName": "WM+ HNI Chợ Mơ, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101129", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101130", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6293", "storeName": "WM+ HNI Tân Phú Mỹ, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Ba Vì Việt Nam", "latitude": 21.214487, "longitude": 105.40997, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101130", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101131", "wardName": "<PERSON><PERSON>", "stores": []}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102101", "wardName": "TT. <PERSON>", "stores": [{"storeCode": "6321", "storeName": "WM+ HNI 118 Hòa Sơn", "officeAddress": "Số <PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.919994, "longitude": 105.699234, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6157", "storeName": "WM+ HNI 15 <PERSON><PERSON><PERSON>, Chương Mỹ", "officeAddress": "Số 15 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5063", "storeName": "WM+ HNI Số 16 Hòa Sơn", "officeAddress": "Số 16 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.92208, "longitude": 105.7022, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102102", "wardName": "<PERSON><PERSON><PERSON>", "stores": [{"storeCode": "6173", "storeName": "WM+ HNI 13 Tổ 3 Tân Xuân", "officeAddress": "<PERSON><PERSON> 13, <PERSON><PERSON> 3 <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102103", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102104", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102105", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102106", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102107", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102110", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6664", "storeName": "WM+ HNI <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102110", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102111", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102113", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6403", "storeName": "WM+ H<PERSON>, Chương <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.865273, "longitude": 105.659935, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102113", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102114", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6683", "storeName": "WM+ HNI Ứng <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> Ứng <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102114", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102115", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102116", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102117", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102119", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102120", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6131", "storeName": "WM+ HNI <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, huy<PERSON><PERSON>, T<PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102120", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102121", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6247", "storeName": "WM+ HNI 68-70 Quảng Bị", "officeAddress": "<PERSON><PERSON> 68-70, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102122", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102123", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102124", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102125", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102126", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5817", "storeName": "WM+ H<PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102126", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102127", "wardName": "<PERSON><PERSON> <PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102128", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6184", "storeName": "WM+ HNI <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102128", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102129", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102130", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102131", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5803", "storeName": "WM+ H<PERSON> 2, <PERSON><PERSON><PERSON><PERSON> M", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.913239, "longitude": 105.646576, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102131", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102132", "wardName": "<PERSON><PERSON>", "stores": []}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103101", "wardName": "<PERSON><PERSON><PERSON>", "stores": [{"storeCode": "1569", "storeName": "WM HNI <PERSON>", "officeNumber": "0559701569", "officeAddress": "<PERSON><PERSON> 188 ph<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.08345, "longitude": 105.67184, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON>", "chainId": "VMT", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6847", "storeName": "WM+ HNI 158 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "officeAddress": "158 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> TP. <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6054", "storeName": "WM+ HNI 8 Ngõ 62 Thụy Ứng", "officeAddress": "<PERSON><PERSON> 8, <PERSON><PERSON> 62, <PERSON><PERSON><PERSON> Ứng, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103102", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6613", "storeName": "WM+ HNI 35 Đông Khê", "officeAddress": "Số 35 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103103", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103104", "wardName": "X. H<PERSON> Mỗ", "stores": []}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103105", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6486", "storeName": "WM+ HNI 165 <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "officeAddress": "Số 165 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.13288, "longitude": 105.68864, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103106", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6465", "storeName": "WM+ HNI Cụm 11 Võng Xuyên", "officeAddress": "<PERSON><PERSON><PERSON> 11, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.140533, "longitude": 105.55352, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103106", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103107", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103109", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6891", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON>", "officeAddress": "Số 42 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> T<PERSON><PERSON> <PERSON><PERSON>ội Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103109", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103110", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103111", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5469", "storeName": "WM+ HNI 153 <PERSON><PERSON><PERSON>", "officeAddress": "153 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6631", "storeName": "WM+ HNI 219 <PERSON><PERSON>", "officeAddress": "219 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5579", "storeName": "WM+ HNI 43-45 <PERSON><PERSON>", "officeAddress": "43-45 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103112", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "1588", "storeName": "WM HNI Ho<PERSON>", "officeNumber": "0559701588", "officeAddress": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> đô thị <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.082825, "longitude": 105.71325, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103112", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMT", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5535", "storeName": "WM+ HNI 174 – 176 <PERSON><PERSON>", "officeAddress": "174 – 176 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103112", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6728", "storeName": "WM+ HNI 55 Đường 422 <PERSON><PERSON> L<PERSON>", "officeAddress": "Số 55 Đường 422 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> TP. <PERSON><PERSON>iệ<PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103112", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103113", "wardName": "<PERSON>. <PERSON>", "stores": [{"storeCode": "6481", "storeName": "WM+ HNI 42 <PERSON><PERSON>, <PERSON><PERSON>", "officeAddress": "42 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "latitude": 21.12741, "longitude": 105.63829, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103113", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON>. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103115", "wardName": "<PERSON><PERSON> Mỗ", "stores": [{"storeCode": "6601", "storeName": "WM+ H<PERSON> Mỗ, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 5, <PERSON><PERSON> Mỗ, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI103", "wardCode": "HNI103115", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> Mỗ", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI103", "districtName": "<PERSON><PERSON>", "wardCode": "HNI103116", "wardName": "<PERSON><PERSON>", "stores": []}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104101", "wardName": "TT<PERSON>", "stores": [{"storeCode": "5792", "storeName": "WM+ HNI 107 Tổ 8 TT Đông Anh", "officeAddress": "<PERSON><PERSON> 107, <PERSON><PERSON> 8, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5906", "storeName": "WM+ HNI 15 Tổ 4 Đông Anh", "officeAddress": "<PERSON><PERSON> 15, <PERSON><PERSON> 4, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3131", "storeName": "WM+ HNI 19 tổ 22 TT Đông Anh", "officeAddress": "<PERSON><PERSON> 19, <PERSON><PERSON> 22, thị <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.16979, "longitude": 105.857, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3089", "storeName": "WM+ HNI 44 <PERSON><PERSON><PERSON>", "officeAddress": "44 tổ 12 p<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.162693, "longitude": 105.84954, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3793", "storeName": "WM+ <PERSON><PERSON> 62, <PERSON><PERSON> 4 <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON> 62, <PERSON><PERSON> 4, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3132", "storeName": "WM+ HNI Tổ 25 TT Đông Anh", "officeAddress": "<PERSON><PERSON> dân ph<PERSON> 25, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.1706, "longitude": 105.844, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104102", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5284", "storeName": "WM+ HNI T<PERSON>ôn <PERSON>n Trung X Bắc Hồng", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104103", "wardName": "X. Cổ <PERSON>", "stores": [{"storeCode": "6400", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.11207, "longitude": 21.112497, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "X. Cổ <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6585", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "X. Cổ <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104104", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6128", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4197", "storeName": "WM+ HNI <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.11829, "longitude": 105.76098, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104105", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "1706", "storeName": "WM HNI Đông H<PERSON>i", "officeNumber": "0559701706", "officeAddress": "Trong tổ hợp dự án <PERSON>window River Park, khu tái định cư <PERSON>, xã <PERSON>, <PERSON><PERSON> TP. <PERSON><PERSON>ội Việt Nam", "latitude": 21.077408, "longitude": 105.87367, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMT", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5090", "storeName": "WM+ HNI Số 83 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON> 83, <PERSON><PERSON> 384, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104106", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6153", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.115671, "longitude": 105.89134, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104106", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104107", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3324", "storeName": "WM+ HNI C<PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 3, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.109692, "longitude": 105.789406, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104107", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5207", "storeName": "WM+ HNI KDC Bắc Thăng Long", "officeAddress": "<PERSON><PERSON> <PERSON>ân <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104107", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104108", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4008", "storeName": "WM+ HNI 28 <PERSON><PERSON><PERSON> Dưỡng, <PERSON><PERSON><PERSON>", "officeAddress": "Số 28 đườ<PERSON> mớ<PERSON> Dưỡng, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5380", "storeName": "WM+ HNI 53 <PERSON><PERSON><PERSON> Dưỡng", "officeAddress": "53 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3910", "storeName": "WM+ HNI 58 <PERSON><PERSON><PERSON> - <PERSON>", "officeAddress": "<PERSON><PERSON> 58, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.139368, "longitude": 105.78305, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3754", "storeName": "WM+ H<PERSON> 7, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 7, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.127459, "longitude": 105.77611, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104109", "wardName": "<PERSON><PERSON> Nỗ", "stores": [{"storeCode": "3876", "storeName": "WM+ <PERSON><PERSON>, <PERSON> Nỗ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.128399, "longitude": 105.79648, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104109", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> Nỗ", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104110", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3499", "storeName": "WM+ HNI Hà Phong", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.152515, "longitude": 105.89999, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104110", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4122", "storeName": "WM+ HNI Lỗ Khê", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> Lỗ <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.153673, "longitude": 105.89034, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104110", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104111", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3776", "storeName": "WM+ HNI 11 <PERSON><PERSON><PERSON>, <PERSON>", "officeAddress": "11 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.088463, "longitude": 105.90319, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4968", "storeName": "WM+ HNI QL3 Phố Lộc Hà", "officeAddress": "QL3 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.100267, "longitude": 105.88573, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104112", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5394", "storeName": "WM+ HNI Thôn Tằng My", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> Tằng <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104112", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104113", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104114", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5936", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104114", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104115", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3995", "storeName": "WM+ HNI Khu 6 Th<PERSON>y Lôi", "officeAddress": "<PERSON><PERSON>  <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.176094, "longitude": 105.9075, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104115", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104116", "wardName": "<PERSON><PERSON> <PERSON>", "stores": [{"storeCode": "5177", "storeName": "WM+ H<PERSON>-Tiên <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.154718, "longitude": 105.82919, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104117", "wardName": "<PERSON>. <PERSON> Nỗ", "stores": [{"storeCode": "3290", "storeName": "WM+ HNI 371 Cao Lỗ", "officeAddress": "Số 371 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.138023, "longitude": 105.85933, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104117", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON>. <PERSON> Nỗ", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6629", "storeName": "WM+ HNI Ấp Tó, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> Ấp T<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.149096, "longitude": 105.85942, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104117", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON>. <PERSON> Nỗ", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3277", "storeName": "WM+ HNI Xóm Ng<PERSON>à<PERSON> U<PERSON> Nỗ", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.138489, "longitude": 105.8559, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104117", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON>. <PERSON> Nỗ", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104118", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6312", "storeName": "WM+ HNI <PERSON>, Đông Anh", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> T<PERSON><PERSON>ội Việt Nam", "latitude": 21.147387, "longitude": 105.91669, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104118", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5501", "storeName": "WM+ HNI <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104118", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3617", "storeName": "WM+ HNI Phố Vân Trì", "officeAddress": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.154184, "longitude": 105.814606, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4287", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.14914, "longitude": 105.81499, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104120", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5619", "storeName": "WM+ HNI 07-09 Cổ Vân", "officeAddress": "Số nhà 07-09 đ<PERSON><PERSON><PERSON>, th<PERSON><PERSON>, x<PERSON> <PERSON>, huy<PERSON><PERSON>h TP. <PERSON><PERSON> Nội Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104120", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3088", "storeName": "WM+ HNI 38 <PERSON><PERSON><PERSON>", "officeAddress": "38 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.141253, "longitude": 105.868576, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104120", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104121", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5628", "storeName": "WM+ HNI Intracom Vĩnh <PERSON>", "officeAddress": "Kiot 14-15 tầng 1 <PERSON><PERSON> <PERSON>n “Nhà ở cao tầng kết hợp văn phòng” lô đất III-B1-<PERSON> <PERSON>hu tái định c<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> TP. <PERSON><PERSON> Nội Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5190", "storeName": "WM+ HNI <PERSON> tư <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> ( <PERSON><PERSON><PERSON>), <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.11486, "longitude": 105.830765, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3092", "storeName": "WM+ HNI Phương Trạch", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4110", "storeName": "WM+ HNI T<PERSON>ôn <PERSON> Trạch", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.1213, "longitude": 105.81502, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104122", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5313", "storeName": "WM+ HNI 32 <PERSON><PERSON><PERSON> Mai", "officeAddress": "32 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104122", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104123", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI104", "districtName": "<PERSON><PERSON>", "wardCode": "HNI104124", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4484", "storeName": "WM+ <PERSON><PERSON>, <PERSON>ổ 49 TT Đông Anh", "officeAddress": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.178135, "longitude": 105.865944, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104124", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3990", "storeName": "WM+ HNI Ngã Ba Lương Quy", "officeAddress": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.166365, "longitude": 105.8663, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI104", "wardCode": "HNI104124", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardStores": [{"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105101", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "stores": [{"storeCode": "F205", "storeName": "FWMP HNI R105-01 S16 Vinhomes Oceanpark", "officeNumber": "0983543394", "officeAddress": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>", "latitude": 21.00143, "longitude": 105.94405, "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "2924", "storeName": "WM+ HNI 391 <PERSON><PERSON> Quảng", "officeAddress": "Số 391 <PERSON><PERSON> Quảng, TT<PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.007994, "longitude": 105.936714, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6939", "storeName": "WM+ HNI 69 <PERSON><PERSON>", "officeAddress": "69 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Gia Lâm TP. <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "2AR5", "storeName": "WM+ HNI R1.05 Ocean Park", "officeAddress": "<PERSON><PERSON><PERSON> 01S16, <PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON> R1.05, <PERSON><PERSON> đô thị <PERSON>homes Ocean Park Thị trấn <PERSON><PERSON><PERSON><PERSON>, Hu<PERSON>ện Gia Lâm TP. <PERSON><PERSON> Nội Việt Nam", "latitude": 21.00143, "longitude": 105.94405, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5582", "storeName": "WM+ HNI S2.06 Ocean Park", "officeAddress": "1S5A, Tầng 1 <PERSON>òa nhà số P06, <PERSON><PERSON> <PERSON>es Ocean Park, <PERSON><PERSON><PERSON> trấn <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "latitude": 20.992807, "longitude": 105.94075, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4801", "storeName": "WM+ HNI Số 2 ngõ 239 <PERSON><PERSON><PERSON><PERSON>_An Đà", "officeAddress": "Số 2 ngõ 239 đườ<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, thị trấn <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.003477, "longitude": 105.93877, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6990", "storeName": "WM+ HNI T1 - TM3 Hanhomes Blue Star", "officeAddress": "<PERSON><PERSON><PERSON> số T1-TM3, <PERSON><PERSON><PERSON> 01, <PERSON><PERSON> T1, <PERSON><PERSON> <PERSON>n <PERSON> ở chung cư cao tầng tại ô đất CT2, <PERSON><PERSON><PERSON> trấn <PERSON><PERSON><PERSON><PERSON>, Huyện Gia Lâm TP. <PERSON><PERSON> Nội Việt Nam", "latitude": 21.01014, "longitude": 105.94051, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "TT. <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105102", "wardName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stores": [{"storeCode": "3960", "storeName": "WM+ HNI 173 <PERSON><PERSON>", "officeAddress": "173 <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.081287, "longitude": 105.91363, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5285", "storeName": "WM+ HNI Thôn Lã Côi", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105103", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5812", "storeName": "WM+ HNI 211 <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON> nhà 211 <PERSON><PERSON><PERSON><PERSON> 3 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5665", "storeName": "WM+ HNI 256 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON>ố <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6485", "storeName": "WM+ HNI 95 <PERSON><PERSON><PERSON>", "officeAddress": "Số <PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.983837, "longitude": 105.91532, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105104", "wardName": "X. <PERSON>ổ B<PERSON>", "stores": [{"storeCode": "3275", "storeName": "WM+ HNI 254 Cổ Bi", "officeAddress": "<PERSON><PERSON> 254 đưởng <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.031588, "longitude": 105.94253, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "X. <PERSON>ổ B<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105105", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "1699", "storeName": "WM VMM HNI Ocean Park", "officeNumber": "0559701699", "officeAddress": "Tầng 2 và Tầng 3, TTTM Vincom Mega Mall Ocean Park, <PERSON><PERSON> đất số CCTP-10 thuộc DA KĐT Gia Lâm, TT Trâu Quỳ và các xã <PERSON>, <PERSON><PERSON><PERSON> Kỵ, <PERSON><PERSON> Nội Việt Nam", "latitude": 20.993734, "longitude": 105.95952, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMT", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3261", "storeName": "WM+ HNI <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.98606, "longitude": 105.930756, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5580", "storeName": "WM+ HNI Dốc Đa Tốn", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5635", "storeName": "WM+ HNI S1.05 Ocean Park", "officeAddress": "1<PERSON>12, <PERSON>ầng 1 <PERSON>òa nhà số S1.05, <PERSON><PERSON> <PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Hà Nội Việt Nam", "latitude": 20.99491, "longitude": 105.94096, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5636", "storeName": "WM+ HNI S1.09 Ocean Park", "officeAddress": "1<PERSON>18, Tầng 1 <PERSON>òa nhà số S1.09, <PERSON><PERSON> <PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Hà Nội Việt Nam", "latitude": 20.9954, "longitude": 105.94357, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5622", "storeName": "WM+ HNI S1.11 Ocean Park", "officeAddress": "1S02, <PERSON><PERSON><PERSON> 1 <PERSON>òa nhà số S1.11, <PERSON><PERSON> <PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Hà Nội Việt Nam", "latitude": 20.99606, "longitude": 105.944275, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5617", "storeName": "WM+ HNI S2.01 Ocean Park", "officeAddress": "1<PERSON><PERSON>, <PERSON>ầng 1 <PERSON>òa n<PERSON>à số S2.01, <PERSON><PERSON> <PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>ộ<PERSON>i<PERSON> Nam", "latitude": 20.990616, "longitude": 105.94092, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5616", "storeName": "WM+ HNI S2.03 Ocean Park", "officeAddress": "1S02, <PERSON><PERSON><PERSON> 1 <PERSON>òa n<PERSON>à số S2.03, <PERSON><PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.991379, "longitude": 105.93952, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5605", "storeName": "WM+ HNI S2.09 Ocean Park", "officeAddress": "1<PERSON>09, <PERSON><PERSON><PERSON> 1 <PERSON>òa n<PERSON>à số S2.09, <PERSON><PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.99257, "longitude": 105.942024, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5621", "storeName": "WM+ HNI S2.10 Ocean Park", "officeAddress": "1<PERSON>17, <PERSON>ầng 1 <PERSON>òa nhà số S2.10, <PERSON><PERSON> <PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Hà Nội Việt Nam", "latitude": 20.992205, "longitude": 105.94297, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5609", "storeName": "WM+ HNI S2.16 Ocean Park", "officeAddress": "1S5A, Tầng 1 <PERSON>òa n<PERSON>à số S2.16, <PERSON><PERSON> <PERSON>es Ocean Park, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "latitude": 20.99035, "longitude": 105.942795, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105106", "wardName": "X. Đặng Xá", "stores": []}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105107", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4124", "storeName": "WM+ HNI <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.077303, "longitude": 105.93522, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105107", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105108", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4504", "storeName": "WM+ HNI Xóm 4 Đông Dư", "officeAddress": "<PERSON><PERSON><PERSON> 4, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.996859, "longitude": 105.91493, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105110", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105111", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5666", "storeName": "WM+ HNI <PERSON>, Gia Lâm", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105112", "wardName": "<PERSON><PERSON> Kỵ", "stores": [{"storeCode": "4442", "storeName": "WM+ H<PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> Kỵ, <PERSON><PERSON><PERSON> Kỵ, Huyện <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>i<PERSON>", "latitude": 20.983515, "longitude": 105.95428, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105112", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON> Kỵ", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105113", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6226", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 3, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.963287, "longitude": 105.90503, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105113", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105114", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3994", "storeName": "WM+ HNI <PERSON>, Gia <PERSON>", "officeAddress": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.02592, "longitude": 105.9922, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105114", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105115", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6225", "storeName": "WM+ HNI TDP <PERSON><PERSON>, Gia Lâm", "officeAddress": "TD<PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.028957, "longitude": 106.0065, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105115", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105116", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4566", "storeName": "WM+ HNI Số 7 Hoa Viên", "officeAddress": "BT4-B-1.3-7-<PERSON><PERSON> đô thị mới Đặng X<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.0176, "longitude": 105.953, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3178", "storeName": "WM+ HNI Thôn 2 Ninh Hiệp", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.082354, "longitude": 105.947495, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4192", "storeName": "WM+ HNI <PERSON> 6 <PERSON><PERSON>", "officeAddress": "Thôn 6 x<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.08206, "longitude": 105.9437, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3837", "storeName": "WM+ HNI Thôn 7 Ninh Hiệp", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 7, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.082254, "longitude": 105.94002, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3659", "storeName": "WM+ HNI X<PERSON> 8, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 8, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.07855, "longitude": 105.940765, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105117", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4357", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.053375, "longitude": 105.9598, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105117", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105118", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "2797", "storeName": "WM+ HNI TTTM Chợ Sủi", "officeAddress": "Số 42 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.0179, "longitude": 105.965, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105118", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5347", "storeName": "WM+ HNI Khu <PERSON>o ông <PERSON>u", "officeAddress": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105120", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105121", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4262", "storeName": "WM+ HNI 18 Dốc Lã", "officeAddress": "18 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.05219, "longitude": 105.90908, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5667", "storeName": "WM+ HNI <PERSON>, G<PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5925", "storeName": "WM+ H<PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI105", "wardCode": "HNI105121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI105", "districtName": "<PERSON><PERSON> <PERSON><PERSON>", "wardCode": "HNI105122", "wardName": "<PERSON><PERSON>", "stores": []}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106101", "wardName": "TT. Tr<PERSON><PERSON>ô<PERSON>", "stores": [{"storeCode": "3999", "storeName": "WM+ HNI 17 K5 Trạm Trôi", "officeAddress": "<PERSON><PERSON> 17, <PERSON><PERSON> 5, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.066408, "longitude": 105.70698, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. Tr<PERSON><PERSON>ô<PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106102", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3072", "storeName": "WM+ HNI 18T2 The Golden An Khánh", "officeAddress": "<PERSON> <PERSON><PERSON> 04, <PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON> <PERSON> 18T2, <PERSON><PERSON> <PERSON>o tầ<PERSON>, DVTM HH6, <PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.0063, "longitude": 105.727, "contactMobile": "", "isOpen": true, "deliveryStatus": "<PERSON>h<PERSON>ng hỗ trợ giao hàng", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4520", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.989325, "longitude": 105.721855, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3239", "storeName": "WM+ HNI Thăng Long Victory", "officeAddress": "<PERSON> dịch vụ số 1, <PERSON><PERSON><PERSON> <PERSON>hà T1 – <PERSON><PERSON> án <PERSON>, khu đô thị mới <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> V<PERSON> Nam", "latitude": 21.008783, "longitude": 105.7224, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3690", "storeName": "WM+ HNI <PERSON> , An <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.0054, "longitude": 105.72, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4000", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>), <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "<PERSON>h<PERSON>ng hỗ trợ giao hàng", "supportDelivering": false, "kindOfStore": "Z500", "kindOfStoreName": "Store", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106103", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106104", "wardName": "<PERSON><PERSON> <PERSON>", "stores": [{"storeCode": "6685", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5509", "storeName": "WM+ HNI Thôn 4 Xã Cát Quế", "officeAddress": "Thôn 4 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> Nam", "latitude": 21.051167, "longitude": 105.677124, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "2A69", "storeName": "WM+ H<PERSON> 9, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 9, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON><PERSON>ội Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106105", "wardName": "<PERSON><PERSON> Sở", "stores": []}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106106", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4924", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.04725, "longitude": 105.722435, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106106", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106107", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6163", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.965395, "longitude": 105.72662, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106107", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106108", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6405", "storeName": "WM+ HNI 40 <PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "Số <PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.064617, "longitude": 105.699776, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6768", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "332 <PERSON><PERSON><PERSON>, <PERSON><PERSON> , <PERSON><PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106110", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106111", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6075", "storeName": "WM+ HNI 74 <PERSON><PERSON><PERSON>", "officeAddress": "Số 74 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "3722", "storeName": "WM+ H<PERSON>, <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> c<PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.06107, "longitude": 105.72222, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106113", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5586", "storeName": "WM+ HNI Thôn 2 Xã Lại Yên", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106113", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106115", "wardName": "<PERSON>. <PERSON>", "stores": [{"storeCode": "3729", "storeName": "WM+ HNI <PERSON> tư <PERSON>ơn Đồng", "officeAddress": "<PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.047308, "longitude": 105.70412, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106115", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON>. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106116", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4521", "storeName": "WM+ <PERSON><PERSON> 6, <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 6, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.015062, "longitude": 105.69773, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106117", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106118", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "3133", "storeName": "[Block] WM+ HNI An Trai", "officeAddress": "<PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.03473, "longitude": 105.736786, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106118", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4767", "storeName": "WM+ HNI 31-LK41 KĐT Vân Canh", "officeAddress": "31-<PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.036833, "longitude": 105.73335, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106118", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6760", "storeName": "WM+ HNI 164 <PERSON><PERSON><PERSON><PERSON> 72, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "Số 164 đ<PERSON><PERSON><PERSON> 72, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> TP. <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6801", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI106", "wardCode": "HNI106119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI106", "districtName": "<PERSON><PERSON>", "wardCode": "HNI106120", "wardName": "<PERSON><PERSON> Sở", "stores": []}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108101", "wardName": "T<PERSON><PERSON> <PERSON>", "stores": [{"storeCode": "5874", "storeName": "WM+ HNI 99 Đại Nghĩa", "officeAddress": "Số 99 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "T<PERSON><PERSON> <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108102", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5686", "storeName": "WM+ HNI Xóm 4 <PERSON><PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 4, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.75361, "longitude": 105.69651, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108103", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108104", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108105", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108106", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108107", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108109", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6892", "storeName": "WM+ HNI <PERSON>, Mỹ Đức", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON><PERSON> Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108109", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108110", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5470", "storeName": "WM+ HNI Thôn Vài X<PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.667768, "longitude": 105.70282, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108110", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108111", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6929", "storeName": "WM+ H<PERSON>, Mỹ Đức", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108113", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6856", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 3, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108113", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6402", "storeName": "WM+ <PERSON><PERSON>, Mỹ Đức", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.609911, "longitude": 105.780495, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108113", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108115", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108116", "wardName": "<PERSON><PERSON> <PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108117", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6880", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON>ứ<PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 6, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108117", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6444", "storeName": "WM+ H<PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.793598, "longitude": 105.67613, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108120", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6610", "storeName": "WM+ <PERSON><PERSON>, Mỹ Đức", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.777279, "longitude": 105.68249, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI108", "wardCode": "HNI108120", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108121", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI108", "districtName": "<PERSON><PERSON>", "wardCode": "HNI108122", "wardName": "<PERSON><PERSON>", "stores": []}]}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110101", "wardName": "TT. <PERSON>", "stores": [{"storeCode": "4887", "storeName": "WM+ HNI Cụm 6 TT Phúc Thọ", "officeAddress": "Cụm 6 T<PERSON><PERSON> tr<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.109728, "longitude": 105.53976, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI110", "wardCode": "HNI110101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110122", "wardName": "<PERSON><PERSON> <PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110102", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6455", "storeName": "WM+ HNI 136 Phố H<PERSON>", "officeAddress": "136 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.132883, "longitude": 105.61926, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI110", "wardCode": "HNI110102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110103", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110104", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110105", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110106", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110107", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110108", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5423", "storeName": "WM+ HNI Cụm 5 Xã Phụng Thượng", "officeAddress": "<PERSON><PERSON><PERSON> 5, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI110", "wardCode": "HNI110108", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110123", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110110", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5722", "storeName": "WM+ H<PERSON> 6 <PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "Thôn 6 xã <PERSON>, <PERSON><PERSON><PERSON><PERSON> ph<PERSON> Hà Nội Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI110", "wardCode": "HNI110110", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110111", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110113", "wardName": "<PERSON>. <PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110115", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110116", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110117", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6543", "storeName": "WM+ HNI <PERSON>, Phúc T<PERSON>ọ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 5, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.148224, "longitude": 105.59444, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI110", "wardCode": "HNI110119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110120", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI110", "districtName": "<PERSON><PERSON>", "wardCode": "HNI110121", "wardName": "<PERSON><PERSON>", "stores": []}]}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109128", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109101", "wardName": "<PERSON><PERSON><PERSON> <PERSON>", "stores": [{"storeCode": "5295", "storeName": "WM+ HNI 158 <PERSON><PERSON><PERSON><PERSON>hu <PERSON>", "officeAddress": "Số <PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.782305, "longitude": 105.91719, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI109", "wardCode": "HNI109101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON> <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109102", "wardName": "T<PERSON>. <PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109103", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109104", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109105", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109106", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109107", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109109", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5429", "storeName": "WM+ HNI Xóm C<PERSON><PERSON>n <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.791527, "longitude": 105.82189, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI109", "wardCode": "HNI109109", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109110", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109111", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109113", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109115", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109116", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109117", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5896", "storeName": "WM+ HNI Giẽ <PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> Giẽ <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI109", "wardCode": "HNI109117", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109119", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109120", "wardName": "<PERSON><PERSON> <PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109121", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5690", "storeName": "WM+ H<PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.742, "longitude": 105.887, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI109", "wardCode": "HNI109121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109122", "wardName": "<PERSON>. <PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109123", "wardName": "X. Tân Dân", "stores": [{"storeCode": "5804", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI109", "wardCode": "HNI109123", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "X. Tân Dân", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109129", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109124", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109125", "wardName": "<PERSON>. <PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109126", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI109", "districtName": "<PERSON><PERSON>", "wardCode": "HNI109127", "wardName": "<PERSON><PERSON>", "stores": []}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111101", "wardName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "stores": [{"storeCode": "4109", "storeName": "WM+ HNI 51 Phố Huyện", "officeAddress": "Số 51 p<PERSON><PERSON>, thị <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.992117, "longitude": 105.641716, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6322", "storeName": "WM+ HNI 98 Đồng Hương", "officeAddress": "98 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.99479, "longitude": 105.64545, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111102", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6482", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.952408, "longitude": 105.61495, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111103", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5323", "storeName": "WM+ HNI Thôn 5 Cộng Hòa", "officeAddress": "Thôn 5 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> Việt Nam", "latitude": 20.972414, "longitude": 105.676094, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111104", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6466", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "Xóm 4 <PERSON><PERSON>nh Lam, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111105", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5422", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.977123, "longitude": 105.66348, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6441", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON> 3, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.979769, "longitude": 105.650024, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111105", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111106", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5669", "storeName": "WM+ HNI 15 <PERSON><PERSON><PERSON>ợ <PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "Số nhà 15 <PERSON><PERSON><PERSON>ợ <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON> <PERSON><PERSON> Vi<PERSON> Nam", "latitude": 21.213848, "longitude": 105.86659, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111106", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5788", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON>c <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>à Nội   TP. <PERSON><PERSON> Nội Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111106", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111107", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111110", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111111", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4167", "storeName": "WM+ HNI Đồng B<PERSON>t", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.995176, "longitude": 105.60892, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111111", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111112", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6108", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111112", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4326", "storeName": "WM+ H<PERSON>, <PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111112", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111113", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111115", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5341", "storeName": "WM+ HNI Thôn 3 Xã Phượng Cách", "officeAddress": "Thôn 3 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>i Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111115", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111116", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "4138", "storeName": "WM+ HNI  Xóm 8 Thụy Khuê", "officeAddress": "Xóm 8 thô<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.023405, "longitude": 105.64619, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "4241", "storeName": "WM+ HNI CT9A Sunny Garden", "officeAddress": "TM 11-A - Tầng 1 Tòa nhà hỗn hợp cao tầng CT9A, KĐT Sunny Garden city, Huyện Quốc Oai, TP. <PERSON>à Nội Việt Nam", "latitude": 21.012823, "longitude": 105.64206, "contactMobile": "", "isOpen": true, "deliveryStatus": "Hỗ trợ giao hàng", "supportDelivering": true, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111116", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111117", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6236", "storeName": "WM+ HNI <PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> 2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.968462, "longitude": 105.68052, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111117", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5546", "storeName": "WM+ HNI Xóm 6 Thôn 3 Xã Thạch Thán", "officeAddress": "<PERSON><PERSON><PERSON> 6,<PERSON><PERSON><PERSON><PERSON> 3 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.989223, "longitude": 105.63456, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111120", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI111", "districtName": "<PERSON><PERSON>", "wardCode": "HNI111121", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6858", "storeName": "WM+ <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "officeAddress": "Đội 6 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON>à <PERSON>ội V<PERSON> Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI111", "wardCode": "HNI111121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}]}], "paging": {"totalCount": 28, "pageNumber": 1, "pageSize": 10, "totalPages": 3}}