const axios = require('axios');
const fs = require('fs');

async function analyzeAPIStructure() {
    try {
        console.log('Analyzing WinMart API structure...\n');
        
        const baseApiUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1';
        
        // Test 1: Get provinces first
        console.log('=== 1. ANALYZING PROVINCES API ===');
        const provincesResponse = await axios.get(`${baseApiUrl}/provinces/all-winmart`);
        
        console.log('Provinces Response Structure:');
        console.log('- Status:', provincesResponse.status);
        console.log('- Data keys:', Object.keys(provincesResponse.data));
        console.log('- Sample province:', provincesResponse.data.data[0]);
        console.log('- Total provinces:', provincesResponse.data.data.length);
        
        // Save provinces for reference
        fs.writeFileSync('provinces_structure.json', JSON.stringify(provincesResponse.data, null, 2), 'utf8');
        
        // Test 2: Analyze stores API with different parameters
        console.log('\n=== 2. ANALYZING STORES API ===');
        
        const testCases = [
            { PageNumber: 1, PageSize: 1, ProvinceCode: 'HNI' },
            { PageNumber: 1, PageSize: 5, ProvinceCode: 'HNI' },
            { PageNumber: 1, PageSize: 10, ProvinceCode: 'HNI' },
            { PageNumber: 2, PageSize: 1, ProvinceCode: 'HNI' },
            { PageNumber: 1, PageSize: 1, ProvinceCode: 'HCM' }
        ];
        
        for (let i = 0; i < testCases.length; i++) {
            const params = testCases[i];
            console.log(`\n--- Test Case ${i + 1}: ${JSON.stringify(params)} ---`);
            
            try {
                const response = await axios.get(`${baseApiUrl}/store-by-province`, { params });
                
                console.log('Response Structure:');
                console.log('- Status:', response.status);
                console.log('- Data keys:', Object.keys(response.data));
                console.log('- Province Name:', response.data.provinceName);
                console.log('- Province Slug:', response.data.provinceSlug);
                
                if (response.data.paging) {
                    console.log('- Paging Info:', response.data.paging);
                }
                
                if (response.data.data) {
                    console.log('- Number of districts returned:', response.data.data.length);
                    
                    if (response.data.data.length > 0) {
                        const firstDistrict = response.data.data[0];
                        console.log('- First district structure:');
                        console.log('  - District Code:', firstDistrict.districtCode);
                        console.log('  - District Name:', firstDistrict.districtName);
                        console.log('  - Ward Stores count:', firstDistrict.wardStores?.length || 0);
                        
                        if (firstDistrict.wardStores && firstDistrict.wardStores.length > 0) {
                            const firstWard = firstDistrict.wardStores[0];
                            console.log('  - First ward structure:');
                            console.log('    - Ward Code:', firstWard.wardCode);
                            console.log('    - Ward Name:', firstWard.wardName);
                            console.log('    - Stores count:', firstWard.stores?.length || 0);
                            
                            if (firstWard.stores && firstWard.stores.length > 0) {
                                const firstStore = firstWard.stores[0];
                                console.log('    - First store structure:');
                                console.log('      - Store Code:', firstStore.storeCode);
                                console.log('      - Store Name:', firstStore.storeName);
                                console.log('      - Address:', firstStore.officeAddress);
                                console.log('      - Phone:', firstStore.contactMobile);
                                console.log('      - Coordinates:', firstStore.latitude, firstStore.longitude);
                                console.log('      - Status:', firstStore.activeStatus);
                                console.log('      - Is Open:', firstStore.isOpen);
                                console.log('      - Chain ID:', firstStore.chainId);
                                console.log('      - All store keys:', Object.keys(firstStore));
                            }
                        }
                    }
                }
                
                // Count total stores in this response
                let totalStores = 0;
                if (response.data.data) {
                    response.data.data.forEach(district => {
                        if (district.wardStores) {
                            district.wardStores.forEach(ward => {
                                if (ward.stores) {
                                    totalStores += ward.stores.length;
                                }
                            });
                        }
                    });
                }
                console.log('- Total stores in this response:', totalStores);
                
                // Save detailed response for analysis
                fs.writeFileSync(`stores_response_${i + 1}.json`, JSON.stringify(response.data, null, 2), 'utf8');
                
            } catch (error) {
                console.error(`Error in test case ${i + 1}:`, error.message);
            }
            
            // Add delay between requests
            if (i < testCases.length - 1) {
                console.log('Waiting 2 seconds...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        // Test 3: Check if pagination actually works
        console.log('\n=== 3. TESTING PAGINATION BEHAVIOR ===');
        
        // Get first page
        const page1 = await axios.get(`${baseApiUrl}/store-by-province`, {
            params: { PageNumber: 1, PageSize: 1, ProvinceCode: 'HNI' }
        });
        
        // Get second page
        const page2 = await axios.get(`${baseApiUrl}/store-by-province`, {
            params: { PageNumber: 2, PageSize: 1, ProvinceCode: 'HNI' }
        });
        
        console.log('Page 1 vs Page 2 comparison:');
        console.log('- Page 1 districts:', page1.data.data?.length || 0);
        console.log('- Page 2 districts:', page2.data.data?.length || 0);
        
        if (page1.data.data?.[0] && page2.data.data?.[0]) {
            console.log('- Page 1 first district:', page1.data.data[0].districtName);
            console.log('- Page 2 first district:', page2.data.data[0].districtName);
            console.log('- Are they different?', page1.data.data[0].districtCode !== page2.data.data[0].districtCode);
        }
        
        // Test 4: Try to get all pages for a small province
        console.log('\n=== 4. TESTING COMPLETE PAGINATION ===');
        
        // Try with a smaller province first
        const smallProvinceTest = await axios.get(`${baseApiUrl}/store-by-province`, {
            params: { PageNumber: 1, PageSize: 1, ProvinceCode: 'AGG' } // An Giang
        });
        
        console.log('Small province test (AGG):');
        console.log('- Paging info:', smallProvinceTest.data.paging);
        console.log('- Districts returned:', smallProvinceTest.data.data?.length || 0);
        
        if (smallProvinceTest.data.paging) {
            const totalPages = smallProvinceTest.data.paging.totalPages || smallProvinceTest.data.paging.totalPage;
            console.log(`- Will need to fetch ${totalPages} pages to get all data`);
        }
        
        console.log('\n=== ANALYSIS COMPLETE ===');
        console.log('Check the generated JSON files for detailed structure analysis.');
        
    } catch (error) {
        console.error('Error:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

analyzeAPIStructure();
