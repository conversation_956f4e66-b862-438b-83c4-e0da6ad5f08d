const axios = require('axios');
const fs = require('fs');

async function scrapeAllWinMartStores() {
    try {
        console.log('🚀 Starting complete WinMart scraping with PageSize=1...\n');
        
        const baseApiUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1';
        const allStores = [];
        
        // Step 1: Get all provinces
        console.log('📍 Getting all provinces...');
        const provincesResponse = await axios.get(`${baseApiUrl}/provinces/all-winmart`);
        const provinces = provincesResponse.data.data;
        console.log(`✓ Found ${provinces.length} provinces\n`);
        
        // Step 2: Process each province
        for (let i = 0; i < provinces.length; i++) {
            const province = provinces[i];
            const provinceCode = province.code;
            const provinceName = province.description;
            
            console.log(`[${i + 1}/${provinces.length}] Processing: ${provinceName} (${provinceCode})`);
            
            try {
                // Get first page to determine total pages
                const firstResponse = await axios.get(`${baseApiUrl}/store-by-province`, {
                    params: {
                        PageNumber: 1,
                        PageSize: 1,
                        ProvinceCode: provinceCode
                    }
                });
                
                const totalPages = firstResponse.data.paging?.totalPages || 1;
                console.log(`  📄 Total pages: ${totalPages}`);
                
                // Process all pages for this province
                for (let page = 1; page <= totalPages; page++) {
                    console.log(`    Fetching page ${page}/${totalPages}...`);
                    
                    const response = await axios.get(`${baseApiUrl}/store-by-province`, {
                        params: {
                            PageNumber: page,
                            PageSize: 1,
                            ProvinceCode: provinceCode
                        }
                    });
                    
                    // Extract stores from response
                    if (response.data && response.data.data) {
                        response.data.data.forEach(district => {
                            if (district.wardStores) {
                                district.wardStores.forEach(ward => {
                                    if (ward.stores && ward.stores.length > 0) {
                                        ward.stores.forEach(store => {
                                            allStores.push({
                                                storeCode: store.storeCode || '',
                                                storeName: store.storeName || '',
                                                address: store.officeAddress || '',
                                                ward: store.wardName || '',
                                                district: store.districtName || '',
                                                province: store.provinceName || '',
                                                phone: store.contactMobile || '',
                                                latitude: store.latitude || null,
                                                longitude: store.longitude || null,
                                                isOpen: store.isOpen || false,
                                                activeStatus: store.activeStatus || '',
                                                supportDelivering: store.supportDelivering || false,
                                                deliveryStatus: store.deliveryStatus || '',
                                                chainId: store.chainId || '',
                                                kindOfStore: store.kindOfStore || '',
                                                isDeliveryTime: store.isDeliveryTime || false,
                                                allowCod: store.allowCod || false
                                            });
                                        });
                                    }
                                });
                            }
                        });
                    }
                    
                    // Small delay between pages
                    if (page < totalPages) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
                
                console.log(`  ✅ Completed ${provinceName} - Found ${allStores.length} total stores so far`);
                
            } catch (error) {
                console.error(`  ❌ Error processing ${provinceName}:`, error.message);
            }
            
            // Delay between provinces (2 seconds as requested)
            if (i < provinces.length - 1) {
                console.log(`  ⏳ Waiting 2 seconds before next province...\n`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        console.log(`\n🎉 SCRAPING COMPLETED!`);
        console.log(`📊 Total stores found: ${allStores.length}`);
        
        // Generate statistics
        const stats = generateStatistics(allStores);
        printStatistics(stats);
        
        // Save all data
        console.log('\n💾 Saving data...');
        
        // Save complete data
        fs.writeFileSync('winmart_all_stores_complete.json', JSON.stringify(allStores, null, 2), 'utf8');
        console.log('✓ Saved: winmart_all_stores_complete.json');
        
        // Save CSV
        saveToCSV(allStores, 'winmart_all_stores_complete.csv');
        console.log('✓ Saved: winmart_all_stores_complete.csv');
        
        // Filter and save WinMart stores only (exclude WM+)
        const winmartOnly = allStores.filter(store => {
            const name = store.storeName.toLowerCase();
            return name.includes('winmart') && !name.includes('wm+');
        });
        
        if (winmartOnly.length > 0) {
            fs.writeFileSync('winmart_stores_only.json', JSON.stringify(winmartOnly, null, 2), 'utf8');
            saveToCSV(winmartOnly, 'winmart_stores_only.csv');
            console.log(`✓ Saved: winmart_stores_only.json (${winmartOnly.length} WinMart stores)`);
            console.log(`✓ Saved: winmart_stores_only.csv (${winmartOnly.length} WinMart stores)`);
        }
        
        // Save statistics
        fs.writeFileSync('winmart_statistics.json', JSON.stringify(stats, null, 2), 'utf8');
        console.log('✓ Saved: winmart_statistics.json');
        
        console.log('\n🏁 All files saved successfully!');
        
    } catch (error) {
        console.error('❌ Scraping failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

function generateStatistics(stores) {
    const stats = {
        totalStores: stores.length,
        byProvince: {},
        byStatus: {},
        byChain: {},
        withCoordinates: 0,
        withPhone: 0,
        openStores: 0
    };
    
    stores.forEach(store => {
        // By province
        const province = store.province || 'Unknown';
        stats.byProvince[province] = (stats.byProvince[province] || 0) + 1;
        
        // By status
        const status = store.activeStatus || 'Unknown';
        stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
        
        // By chain
        const chain = store.chainId || 'Unknown';
        stats.byChain[chain] = (stats.byChain[chain] || 0) + 1;
        
        // With coordinates
        if (store.latitude && store.longitude) {
            stats.withCoordinates++;
        }
        
        // With phone
        if (store.phone) {
            stats.withPhone++;
        }
        
        // Open stores
        if (store.isOpen) {
            stats.openStores++;
        }
    });
    
    return stats;
}

function printStatistics(stats) {
    console.log('\n📈 STATISTICS:');
    console.log(`Total stores: ${stats.totalStores}`);
    console.log(`Open stores: ${stats.openStores}`);
    console.log(`Stores with coordinates: ${stats.withCoordinates}`);
    console.log(`Stores with phone: ${stats.withPhone}`);
    
    console.log('\n🏆 Top 10 provinces by store count:');
    const sortedProvinces = Object.entries(stats.byProvince)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10);
    
    sortedProvinces.forEach(([province, count], index) => {
        console.log(`  ${index + 1}. ${province}: ${count} stores`);
    });
    
    console.log('\n🏪 Store chains:');
    Object.entries(stats.byChain).forEach(([chain, count]) => {
        console.log(`  ${chain}: ${count} stores`);
    });
    
    console.log('\n📊 Store status:');
    Object.entries(stats.byStatus).forEach(([status, count]) => {
        console.log(`  ${status}: ${count} stores`);
    });
}

function saveToCSV(stores, filename) {
    const headers = [
        'Store Code', 'Store Name', 'Address', 'Ward', 'District', 'Province',
        'Phone', 'Latitude', 'Longitude', 'Is Open', 'Active Status',
        'Support Delivering', 'Delivery Status', 'Chain ID', 'Kind Of Store',
        'Is Delivery Time', 'Allow COD'
    ];
    
    const csvContent = [
        headers.join(','),
        ...stores.map(store => [
            `"${store.storeCode || ''}"`,
            `"${store.storeName || ''}"`,
            `"${store.address || ''}"`,
            `"${store.ward || ''}"`,
            `"${store.district || ''}"`,
            `"${store.province || ''}"`,
            `"${store.phone || ''}"`,
            store.latitude || '',
            store.longitude || '',
            store.isOpen || false,
            `"${store.activeStatus || ''}"`,
            store.supportDelivering || false,
            `"${store.deliveryStatus || ''}"`,
            `"${store.chainId || ''}"`,
            `"${store.kindOfStore || ''}"`,
            store.isDeliveryTime || false,
            store.allowCod || false
        ].join(','))
    ].join('\n');

    fs.writeFileSync(filename, csvContent, 'utf8');
}

// Run the scraper
scrapeAllWinMartStores();
