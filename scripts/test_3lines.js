const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

// Test with a few sample stores
const sampleStores = [
    'https://stores.winmart.vn/vietnam-sieu-thi-winmart-trung-hoa-supermarket-hoang-dao-thuy-cau-giay-390058/Home',
    'https://stores.winmart.vn/vietnam-sieu-thi-winmart-quang-trung-supermarket-phuong-10-ho-chi-minh-390062/Home',
    'https://stores.winmart.vn/winmart-da-nang-supermarket-an-hai-bac-son-tra-vietnam/Home'
];

async function getStoreDetails(storeUrl) {
    try {
        console.log(`\nGetting details from: ${storeUrl}`);

        const response = await axios.get(storeUrl);
        const $ = cheerio.load(response.data);

        // Extract store name
        let storeName = '';
        const nameEl = $('h1').first();
        if (nameEl.length > 0) {
            storeName = nameEl.text().trim();
        }

        // Extract 3 address lines
        let addressLine1 = '';
        let addressLine2 = '';
        let addressLine3 = '';

        const addressContainer = $('.store_information_02__text').first();
        if (addressContainer.length > 0) {
            console.log(`Address HTML: ${addressContainer.html()}`);

            // Get all span elements within the address
            const spans = addressContainer.find('span');
            console.log(`Found ${spans.length} span elements`);

            spans.each((i, span) => {
                console.log(`Span ${i+1}: "${$(span).text().trim()}"`);
            });

            if (spans.length >= 3) {
                // Extract the 3 unique lines from spans
                addressLine1 = $(spans[0]).text().trim(); // First span - address
                addressLine2 = $(spans[1]).text().trim(); // Second span - street/ward

                // For the third line, take the fourth span (index 3) to avoid duplicates
                if (spans.length >= 4) {
                    addressLine3 = $(spans[3]).text().trim(); // Fourth span - district
                } else {
                    addressLine3 = $(spans[2]).text().trim(); // Fallback to third span
                }

                // Clean up duplicates
                if (addressLine2 === addressLine3 && spans.length >= 5) {
                    addressLine3 = $(spans[4]).text().trim();
                }
            } else {
                // Fallback: parse the combined text
                const fullText = addressContainer.text().trim();
                console.log(`Full text: "${fullText}"`);

                // Use regex to split
                const match = fullText.match(/^(.*?TTTM[^P]*)(Phố[^C]*)(Cầu\s+\w+.*?)$/);
                if (match) {
                    addressLine1 = match[1].trim();
                    addressLine2 = match[2].trim();
                    addressLine3 = match[3].trim();
                } else {
                    // Try another pattern
                    const match2 = fullText.match(/^(.*?)(Phố\s+[^A-Z]*)(.*?)$/);
                    if (match2) {
                        addressLine1 = match2[1].trim();
                        addressLine2 = match2[2].trim();
                        addressLine3 = match2[3].trim();
                    } else {
                        addressLine1 = fullText;
                    }
                }
            }
        }

        // Extract phone
        let phone = '';
        const phoneLink = $('a[href^="tel:"]').first();
        if (phoneLink.length > 0) {
            phone = phoneLink.attr('href').replace('tel:', '');
        }

        const result = {
            name: storeName,
            address: addressLine1,
            street: addressLine2,
            district: addressLine3,
            phone: phone,
            url: storeUrl
        };

        console.log(`✓ Name: ${result.name}`);
        console.log(`✓ Address: ${result.address}`);
        console.log(`✓ Street: ${result.street}`);
        console.log(`✓ District: ${result.district}`);
        console.log(`✓ Phone: ${result.phone}`);

        return result;

    } catch (error) {
        console.error(`Error getting details for ${storeUrl}:`, error.message);
        return null;
    }
}

async function main() {
    console.log('Testing 3-lines address extraction...\n');

    const results = [];

    for (let i = 0; i < sampleStores.length; i++) {
        const storeUrl = sampleStores[i];
        const storeDetails = await getStoreDetails(storeUrl);

        if (storeDetails) {
            results.push(storeDetails);
        }

        if (i < sampleStores.length - 1) {
            console.log('\nWaiting 2 seconds...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    // Save results
    fs.writeFileSync('test_3lines_results.json', JSON.stringify(results, null, 2), 'utf8');

    console.log(`\n=== FINAL RESULTS ===`);
    console.log(`Processed ${results.length} stores`);
    console.log(`Results saved to test_3lines_results.json`);

    results.forEach((store, i) => {
        console.log(`\n${i+1}. ${store.name}`);
        console.log(`   Address: ${store.address}`);
        console.log(`   Street: ${store.street}`);
        console.log(`   District: ${store.district}`);
        console.log(`   Phone: ${store.phone}`);
    });
}

main().catch(console.error);
