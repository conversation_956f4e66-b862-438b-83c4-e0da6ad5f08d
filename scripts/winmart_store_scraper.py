#!/usr/bin/env python3
"""
WinMart Store Scraper
Scrapes store information from https://stores.winmart.vn
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import time
import re
from urllib.parse import urljoin
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WinmartStoreScraper:
    def __init__(self):
        self.base_url = 'https://stores.winmart.vn'
        self.stores = []
        self.delay = 1  # 1 second delay between requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def get_total_pages(self):
        """Get the total number of pages to scrape"""
        try:
            response = self.session.get(self.base_url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find pagination links
            pagination_links = soup.find_all('a', href=re.compile(r'page=\d+'))
            max_page = 1

            for link in pagination_links:
                href = link.get('href', '')
                page_match = re.search(r'page=(\d+)', href)
                if page_match:
                    page_num = int(page_match.group(1))
                    if page_num > max_page:
                        max_page = page_num

            logger.info(f"Found {max_page} pages to scrape")
            return max_page
        except Exception as e:
            logger.error(f"Error getting total pages: {e}")
            return 1

    def extract_store_info(self, soup):
        """Extract store information from a page"""
        stores = []
        seen_urls = set()  # To avoid duplicates

        # Find store containers - look for the main store listing section
        store_section = soup.find('div', string=re.compile(r'WinMart cửa hàngs'))
        if not store_section:
            # Fallback: find any section containing store links
            store_section = soup

        # Find all store links with more specific pattern
        store_links = soup.find_all('a', href=re.compile(r'/(vietnam-.*-supermarket-|winmart-.*-supermarket-)'))

        for link in store_links:
            try:
                store_url = urljoin(self.base_url, link.get('href', ''))

                # Skip if we've already processed this URL
                if store_url in seen_urls:
                    continue
                seen_urls.add(store_url)

                store_name = link.get_text(strip=True)

                # Skip if not a valid store name
                if not store_name or 'WinMart' not in store_name:
                    continue

                # Find the store container - look for the parent that contains all store info
                store_container = link.find_parent()

                # Try to find a container that has both the link and phone number
                attempts = 0
                while store_container and attempts < 5:
                    phone_link = store_container.find('a', href=re.compile(r'tel:'))
                    if phone_link:
                        break
                    store_container = store_container.find_parent()
                    attempts += 1

                if not store_container:
                    continue

                # Extract phone number
                phone_link = store_container.find('a', href=re.compile(r'tel:'))
                phone = phone_link.get('href', '').replace('tel:', '') if phone_link else ''

                # Extract address information more precisely
                # Get all text nodes and clean them
                all_text = store_container.get_text(separator='|', strip=True)
                lines = [line.strip() for line in all_text.split('|') if line.strip()]

                # Filter out unwanted lines
                filtered_lines = []
                for line in lines:
                    if any(skip in line for skip in ['Gọi', 'Bản đồ', 'Trang web', 'Mở lúc', 'Mở cửa', phone.replace('+84', '0')]):
                        continue
                    if line == store_name:
                        continue
                    if len(line) > 3:
                        filtered_lines.append(line)

                # Parse address components
                address_parts = []
                district = ''
                city = ''

                for line in filtered_lines:
                    # Check if it's a district/ward
                    if any(keyword in line for keyword in ['Phường ', 'Quận ', 'Huyện ', 'Thị xã ', 'Thành phố ']):
                        if not district:
                            district = line
                    # Check if it's a city/province (usually the last meaningful line)
                    elif len(line) > 2 and not any(keyword in line for keyword in ['Số ', 'Đường ', 'Phố ', 'Tầng ', 'TTTM', 'Vincom', 'Lô ']):
                        city = line
                    # Address lines
                    else:
                        address_parts.append(line)

                # Combine address parts
                full_address = ', '.join(address_parts) if address_parts else ''

                # Clean up city and district
                if district and city and district in city:
                    city = city.replace(district, '').strip().strip(',').strip()

                store_data = {
                    'name': store_name,
                    'address': full_address,
                    'district': district,
                    'city': city,
                    'phone': phone,
                    'url': store_url
                }

                # Only add if we have meaningful data
                if store_data['name'] and (store_data['address'] or store_data['district'] or store_data['city']):
                    stores.append(store_data)

            except Exception as e:
                logger.warning(f"Error extracting store info: {e}")
                continue

        return stores

    def scrape_page(self, page_num=1):
        """Scrape a single page"""
        try:
            url = self.base_url if page_num == 1 else f"{self.base_url}/?page={page_num}"
            logger.info(f"Scraping page {page_num}: {url}")

            response = self.session.get(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            page_stores = self.extract_store_info(soup)
            logger.info(f"Found {len(page_stores)} stores on page {page_num}")

            return page_stores
        except Exception as e:
            logger.error(f"Error scraping page {page_num}: {e}")
            return []

    def get_store_details(self, store_url):
        """Get detailed store information"""
        try:
            time.sleep(self.delay)
            response = self.session.get(store_url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            details = {
                'hours': '',
                'coordinates': {'lat': None, 'lng': None}
            }

            # Try to find opening hours
            text_content = soup.get_text()
            hours_match = re.search(r'(\d{1,2}:\d{2}\s*(AM|PM).*?\d{1,2}:\d{2}\s*(AM|PM))', text_content, re.IGNORECASE)
            if hours_match:
                details['hours'] = hours_match.group(1)

            # Try to extract coordinates from scripts
            scripts = soup.find_all('script')
            for script in scripts:
                script_text = script.get_text()
                coord_match = re.search(r'lat["\s]*:[\s]*([0-9.-]+).*?lng["\s]*:[\s]*([0-9.-]+)', script_text, re.IGNORECASE)
                if coord_match:
                    details['coordinates']['lat'] = float(coord_match.group(1))
                    details['coordinates']['lng'] = float(coord_match.group(2))
                    break

            return details
        except Exception as e:
            logger.error(f"Error getting store details from {store_url}: {e}")
            return {'hours': '', 'coordinates': {'lat': None, 'lng': None}}

    def remove_duplicates(self):
        """Remove duplicate stores based on URL and name"""
        seen_urls = set()
        seen_names = set()
        unique_stores = []

        for store in self.stores:
            # Create a unique identifier
            store_id = f"{store.get('name', '')}-{store.get('address', '')}-{store.get('city', '')}"
            store_url = store.get('url', '')

            # Skip if we've seen this URL or very similar store
            if store_url in seen_urls or store_id in seen_names:
                continue

            seen_urls.add(store_url)
            seen_names.add(store_id)
            unique_stores.append(store)

        logger.info(f"Removed {len(self.stores) - len(unique_stores)} duplicate stores")
        self.stores = unique_stores

    def scrape_all_stores(self, include_details=False):
        """Main scraping function"""
        try:
            logger.info("Starting WinMart store scraping...")

            total_pages = self.get_total_pages()

            for page in range(1, total_pages + 1):
                page_stores = self.scrape_page(page)
                self.stores.extend(page_stores)

                # Add delay between pages
                if page < total_pages:
                    time.sleep(self.delay)

            logger.info(f"Total stores found before deduplication: {len(self.stores)}")

            # Remove duplicates
            self.remove_duplicates()

            logger.info(f"Total unique stores: {len(self.stores)}")

            # Get detailed information if requested
            if include_details:
                logger.info("Getting detailed store information...")
                for i, store in enumerate(self.stores):
                    if store.get('url'):
                        details = self.get_store_details(store['url'])
                        self.stores[i].update(details)
                        logger.info(f"Processed {i + 1}/{len(self.stores)} stores")

            return self.stores
        except Exception as e:
            logger.error(f"Error in scrape_all_stores: {e}")
            return self.stores

    def save_to_json(self, filename='winmart_stores.json'):
        """Save stores to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.stores, f, ensure_ascii=False, indent=2)
            logger.info(f"Stores saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving to JSON: {e}")

    def save_to_csv(self, filename='winmart_stores.csv'):
        """Save stores to CSV file"""
        try:
            if not self.stores:
                logger.info("No stores to save")
                return

            fieldnames = ['name', 'address', 'district', 'city', 'phone', 'url', 'hours', 'latitude', 'longitude']

            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for store in self.stores:
                    row = {
                        'name': store.get('name', ''),
                        'address': store.get('address', ''),
                        'district': store.get('district', ''),
                        'city': store.get('city', ''),
                        'phone': store.get('phone', ''),
                        'url': store.get('url', ''),
                        'hours': store.get('hours', ''),
                        'latitude': store.get('coordinates', {}).get('lat', ''),
                        'longitude': store.get('coordinates', {}).get('lng', '')
                    }
                    writer.writerow(row)

            logger.info(f"Stores saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")

def main():
    """Main function"""
    scraper = WinmartStoreScraper()

    # Scrape all stores (set to True to include detailed info like hours and coordinates)
    stores = scraper.scrape_all_stores(include_details=False)

    # Save results
    scraper.save_to_json('winmart_stores.json')
    scraper.save_to_csv('winmart_stores.csv')

    print(f"\nScraping completed! Found {len(stores)} stores.")
    print("Files saved:")
    print("- winmart_stores.json")
    print("- winmart_stores.csv")

if __name__ == "__main__":
    main()
