const axios = require('axios');
const fs = require('fs');

async function testWinmartAPI() {
    try {
        console.log('Testing WinMart API endpoints...\n');
        
        // Test 1: Get all provinces
        console.log('=== TESTING PROVINCES API ===');
        const provincesUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1/provinces/all-winmart';
        
        try {
            const provincesResponse = await axios.get(provincesUrl);
            console.log(`✓ Provinces API Status: ${provincesResponse.status}`);
            console.log(`✓ Response type: ${typeof provincesResponse.data}`);
            console.log(`✓ Data structure:`, Object.keys(provincesResponse.data));
            
            if (provincesResponse.data.data) {
                console.log(`✓ Number of provinces: ${provincesResponse.data.data.length}`);
                console.log('\n--- Sample provinces ---');
                provincesResponse.data.data.slice(0, 5).forEach((province, i) => {
                    console.log(`${i+1}. ${province.name} (${province.code})`);
                });
                
                // Save provinces data
                fs.writeFileSync('provinces_data.json', JSON.stringify(provincesResponse.data, null, 2), 'utf8');
                console.log('\n✓ Provinces data saved to provinces_data.json');
            }
        } catch (error) {
            console.error('✗ Provinces API Error:', error.message);
        }
        
        // Test 2: Get stores for Hanoi (HNI)
        console.log('\n=== TESTING STORES API (Hanoi) ===');
        const storesUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1/store-by-province?PageNumber=1&PageSize=10&ProvinceCode=HNI';
        
        try {
            const storesResponse = await axios.get(storesUrl);
            console.log(`✓ Stores API Status: ${storesResponse.status}`);
            console.log(`✓ Response type: ${typeof storesResponse.data}`);
            console.log(`✓ Data structure:`, Object.keys(storesResponse.data));
            
            if (storesResponse.data.data) {
                console.log(`✓ Number of stores in response: ${storesResponse.data.data.length}`);
                console.log(`✓ Total stores: ${storesResponse.data.totalRecord || 'N/A'}`);
                console.log(`✓ Total pages: ${storesResponse.data.totalPage || 'N/A'}`);
                
                console.log('\n--- Sample stores ---');
                storesResponse.data.data.slice(0, 3).forEach((store, i) => {
                    console.log(`\n${i+1}. Store:`, JSON.stringify(store, null, 2));
                });
                
                // Save stores data
                fs.writeFileSync('hanoi_stores_sample.json', JSON.stringify(storesResponse.data, null, 2), 'utf8');
                console.log('\n✓ Hanoi stores sample saved to hanoi_stores_sample.json');
            }
        } catch (error) {
            console.error('✗ Stores API Error:', error.message);
        }
        
        // Test 3: Get stores for Ho Chi Minh City
        console.log('\n=== TESTING STORES API (Ho Chi Minh) ===');
        const hcmStoresUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1/store-by-province?PageNumber=1&PageSize=5&ProvinceCode=HCM';
        
        try {
            const hcmStoresResponse = await axios.get(hcmStoresUrl);
            console.log(`✓ HCM Stores API Status: ${hcmStoresResponse.status}`);
            
            if (hcmStoresResponse.data.data) {
                console.log(`✓ Number of HCM stores: ${hcmStoresResponse.data.data.length}`);
                console.log(`✓ Total HCM stores: ${hcmStoresResponse.data.totalRecord || 'N/A'}`);
                
                console.log('\n--- Sample HCM stores ---');
                hcmStoresResponse.data.data.slice(0, 2).forEach((store, i) => {
                    console.log(`\n${i+1}. Store:`, JSON.stringify(store, null, 2));
                });
            }
        } catch (error) {
            console.error('✗ HCM Stores API Error:', error.message);
        }
        
        // Test 4: Test pagination - get more stores
        console.log('\n=== TESTING PAGINATION ===');
        const paginationUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1/store-by-province?PageNumber=1&PageSize=50&ProvinceCode=HNI';
        
        try {
            const paginationResponse = await axios.get(paginationUrl);
            console.log(`✓ Pagination API Status: ${paginationResponse.status}`);
            
            if (paginationResponse.data) {
                console.log(`✓ Page 1 with PageSize=50:`);
                console.log(`  - Stores returned: ${paginationResponse.data.data?.length || 0}`);
                console.log(`  - Total records: ${paginationResponse.data.totalRecord || 'N/A'}`);
                console.log(`  - Total pages: ${paginationResponse.data.totalPage || 'N/A'}`);
                console.log(`  - Current page: ${paginationResponse.data.pageNumber || 'N/A'}`);
                console.log(`  - Page size: ${paginationResponse.data.pageSize || 'N/A'}`);
            }
        } catch (error) {
            console.error('✗ Pagination API Error:', error.message);
        }
        
        console.log('\n=== API TESTING COMPLETED ===');
        
    } catch (error) {
        console.error('General error:', error.message);
    }
}

testWinmartAPI();
