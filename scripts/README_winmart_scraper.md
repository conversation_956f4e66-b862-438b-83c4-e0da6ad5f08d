# WinMart Store Scraper

Scripts để lấy danh sách cửa hàng từ trang https://stores.winmart.vn

## Tính năng

- Lấy danh sách tất cả cửa hàng WinMart từ website
- Trích xuất thông tin: tên c<PERSON><PERSON>, địa chỉ, quận/huy<PERSON>, thành phố, số điện thoại
- Tùy chọn lấy thông tin chi tiết: gi<PERSON> mở cửa, tọa độ GPS
- Xuất dữ liệu ra file JSON và CSV
- Có delay giữa các request để tránh spam server

## Yêu cầu

### Cho JavaScript (Node.js)
```bash
npm install axios cheerio
```

### Cho Python
```bash
pip install requests beautifulsoup4
```

## Cách sử dụng

### JavaScript
```bash
# Chạy script
node scripts/winmart_store_scraper.js

# Hoặc import như module
const WinmartStoreScraper = require('./scripts/winmart_store_scraper.js');

const scraper = new WinmartStoreScraper();
const stores = await scraper.scrapeAllStores();
```

### Python
```bash
# Chạy script
python scripts/winmart_store_scraper.py

# Hoặc import như module
from scripts.winmart_store_scraper import WinmartStoreScraper

scraper = WinmartStoreScraper()
stores = scraper.scrape_all_stores()
```

## Tùy chọn

### Lấy thông tin chi tiết
Để lấy thêm thông tin như giờ mở cửa và tọa độ GPS (sẽ chậm hơn):

**JavaScript:**
```javascript
const stores = await scraper.scrapeAllStores(true);
```

**Python:**
```python
stores = scraper.scrape_all_stores(include_details=True)
```

### Thay đổi delay
**JavaScript:**
```javascript
scraper.delay = 2000; // 2 seconds
```

**Python:**
```python
scraper.delay = 2  # 2 seconds
```

## Output

Script sẽ tạo ra 2 file:

1. **winmart_stores.json** - Dữ liệu JSON với cấu trúc:
```json
[
  {
    "name": "Siêu thị WinMart Trung Hòa",
    "address": "Số 5, Tầng B1, TTTM Ocean Mall Trung Hòa",
    "district": "Cầu Giấy",
    "city": "Hà Nội",
    "phone": "+84559701533",
    "url": "https://stores.winmart.vn/vietnam-sieu-thi-winmart-trung-hoa-supermarket-hoang-dao-thuy-cau-giay-390058/Home",
    "hours": "08:00 AM - 10:00 PM",
    "coordinates": {
      "lat": 21.0285,
      "lng": 105.8542
    }
  }
]
```

2. **winmart_stores.csv** - File CSV với các cột:
   - Name
   - Address
   - District
   - City
   - Phone
   - URL
   - Hours
   - Latitude
   - Longitude

## Lưu ý

- Script có delay 1 giây giữa các request để tránh spam server
- Nếu lấy thông tin chi tiết, thời gian chạy sẽ lâu hơn nhiều
- Dữ liệu được lưu với encoding UTF-8 để hỗ trợ tiếng Việt
- Script sẽ tự động phát hiện số trang và lấy hết tất cả cửa hàng

## Xử lý lỗi

- Script có logging để theo dõi quá trình
- Nếu một trang bị lỗi, script sẽ tiếp tục với trang tiếp theo
- Dữ liệu đã lấy được sẽ được giữ lại ngay cả khi có lỗi

## Ví dụ sử dụng nâng cao

### JavaScript
```javascript
const scraper = new WinmartStoreScraper();

// Tùy chỉnh delay
scraper.delay = 500;

// Lấy dữ liệu
const stores = await scraper.scrapeAllStores(false);

// Lọc theo thành phố
const hanoiStores = stores.filter(store => 
  store.city.toLowerCase().includes('hà nội')
);

// Lưu file tùy chỉnh
scraper.saveToJson('hanoi_stores.json');
scraper.saveToCsv('hanoi_stores.csv');
```

### Python
```python
scraper = WinmartStoreScraper()

# Lấy dữ liệu
stores = scraper.scrape_all_stores(include_details=False)

# Lọc theo thành phố
hanoi_stores = [store for store in stores 
                if 'hà nội' in store.get('city', '').lower()]

# In thống kê
print(f"Tổng số cửa hàng: {len(stores)}")
print(f"Cửa hàng ở Hà Nội: {len(hanoi_stores)}")

# Lưu file
scraper.save_to_json('winmart_stores.json')
scraper.save_to_csv('winmart_stores.csv')
```
