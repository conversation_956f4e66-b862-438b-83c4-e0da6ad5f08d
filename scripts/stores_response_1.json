{"provinceName": "T<PERSON><PERSON> <PERSON>", "provinceSlug": "thanh-pho-ha-noi", "data": [{"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101132", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101101", "wardName": "TT. T<PERSON> Đằng", "stores": [{"storeCode": "GH35", "storeName": "[Block] WM+ HNI Gian hàng hchợ 2 (U", "officeAddress": "T<PERSON><PERSON> <PERSON><PERSON> Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. T<PERSON> Đằng", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6050", "storeName": "WM+ HNI 188 Quảng Oai", "officeAddress": "<PERSON><PERSON> nhà 188 đườ<PERSON>, <PERSON><PERSON><PERSON> trấn Tâ<PERSON> Đằng, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> Việt Nam", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStore": "", "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. T<PERSON> Đằng", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101102", "wardName": "X. Ba Trại", "stores": [{"storeCode": "6849", "storeName": "WM+ HNI Ba Trại, Ba Vì", "officeAddress": "Thôn 5, <PERSON><PERSON>, Huyện Ba Vì TP. <PERSON><PERSON> Nội Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "X. Ba Trại", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101103", "wardName": "X. <PERSON>", "stores": [{"storeCode": "GH32", "storeName": "WM+ HNI Gian hàng hội chợ 1 (Urban)", "officeAddress": "T<PERSON><PERSON> <PERSON><PERSON> Việt Nam", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101103", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "X. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101104", "wardName": "<PERSON><PERSON> <PERSON><PERSON>", "stores": [{"storeCode": "6866", "storeName": "WM+ H<PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> TP. <PERSON><PERSON>ệ<PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101104", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON> <PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101105", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101106", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101107", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5741", "storeName": "WM+ HNI 329 Phố Mới, Ba Vì", "officeAddress": "Số 329 <PERSON><PERSON>, th<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.192472, "longitude": 105.43203, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101107", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101110", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101111", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101113", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101114", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101115", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101116", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101117", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101119", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5952", "storeName": "WM+ HNI <PERSON>ơn, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101119", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101120", "wardName": "X. <PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101121", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101122", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5662", "storeName": "WM+ HNI <PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 21.114, "longitude": 105.407, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101122", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6676", "storeName": "WM+ H<PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101122", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101123", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101124", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101125", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6076", "storeName": "WM+ <PERSON><PERSON>, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101125", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101126", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101127", "wardName": "<PERSON><PERSON> Bạt", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101128", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101129", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6072", "storeName": "WM+ HNI Chợ Mơ, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101129", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101130", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6293", "storeName": "WM+ HNI Tân Phú Mỹ, Ba Vì", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Ba Vì Việt Nam", "latitude": 21.214487, "longitude": 105.40997, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI101", "wardCode": "HNI101130", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI101", "districtName": "<PERSON><PERSON>", "wardCode": "HNI101131", "wardName": "<PERSON><PERSON>", "stores": []}]}], "paging": {"totalCount": 28, "pageNumber": 1, "pageSize": 1, "totalPages": 28}}