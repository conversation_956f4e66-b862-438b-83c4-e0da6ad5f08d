const axios = require('axios');
const cheerio = require('cheerio');

async function testStoreDetail() {
    try {
        // Test with a specific store URL
        const testUrl = 'https://stores.winmart.vn/vietnam-sieu-thi-winmart-trung-hoa-supermarket-hoang-dao-thuy-cau-giay-390058/Home';
        
        console.log(`Testing store detail page: ${testUrl}`);
        
        const response = await axios.get(testUrl);
        const $ = cheerio.load(response.data);
        
        console.log('\n=== PAGE TITLE ===');
        console.log($('title').text());
        
        console.log('\n=== ALL H1 TAGS ===');
        $('h1').each((i, el) => {
            console.log(`H1 ${i+1}: ${$(el).text().trim()}`);
        });
        
        console.log('\n=== ALL H2 TAGS ===');
        $('h2').each((i, el) => {
            console.log(`H2 ${i+1}: ${$(el).text().trim()}`);
        });
        
        console.log('\n=== ELEMENTS CONTAINING "Địa chỉ" ===');
        $('*:contains("Địa chỉ")').each((i, el) => {
            const text = $(el).text().trim();
            if (text.includes('Địa chỉ') && text.length < 200) {
                console.log(`Element ${i+1}: ${text}`);
                console.log(`Tag: ${el.tagName}, Class: ${$(el).attr('class') || 'none'}`);
                console.log('---');
            }
        });
        
        console.log('\n=== ELEMENTS CONTAINING "Address" ===');
        $('*:contains("Address")').each((i, el) => {
            const text = $(el).text().trim();
            if (text.includes('Address') && text.length < 200) {
                console.log(`Element ${i+1}: ${text}`);
                console.log(`Tag: ${el.tagName}, Class: ${$(el).attr('class') || 'none'}`);
                console.log('---');
            }
        });
        
        console.log('\n=== ELEMENTS WITH ADDRESS-RELATED CLASSES ===');
        const addressClasses = ['.address', '.location', '[class*="address"]', '[class*="location"]', '[class*="addr"]'];
        addressClasses.forEach(selector => {
            $(selector).each((i, el) => {
                const text = $(el).text().trim();
                if (text && text.length > 5) {
                    console.log(`${selector}: ${text}`);
                }
            });
        });
        
        console.log('\n=== ALL PARAGRAPH TAGS ===');
        $('p').each((i, el) => {
            const text = $(el).text().trim();
            if (text && text.length > 10 && text.length < 200) {
                console.log(`P ${i+1}: ${text}`);
            }
        });
        
        console.log('\n=== ALL DIV TAGS WITH TEXT ===');
        $('div').each((i, el) => {
            const text = $(el).text().trim();
            if (text && text.length > 10 && text.length < 200 && !text.includes('\n')) {
                console.log(`DIV ${i+1}: ${text}`);
                console.log(`Class: ${$(el).attr('class') || 'none'}`);
                console.log('---');
            }
        });
        
        console.log('\n=== PHONE NUMBERS ===');
        $('a[href^="tel:"]').each((i, el) => {
            console.log(`Phone ${i+1}: ${$(el).attr('href')} - ${$(el).text()}`);
        });
        
        console.log('\n=== ELEMENTS CONTAINING PHONE PATTERNS ===');
        const phonePattern = /(\+84\d{9,10}|\d{10,11})/;
        $('*').each((i, el) => {
            const text = $(el).text().trim();
            if (phonePattern.test(text) && text.length < 50) {
                console.log(`Phone element: ${text}`);
            }
        });
        
        console.log('\n=== RAW HTML SAMPLE (first 1000 chars) ===');
        console.log(response.data.substring(0, 1000));
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

testStoreDetail();
