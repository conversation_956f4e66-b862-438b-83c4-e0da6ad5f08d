const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

class WinmartStoreScraper {
    constructor() {
        this.baseUrl = 'https://stores.winmart.vn';
        this.stores = [];
        this.delay = 1000; // 1 second delay between requests
    }

    // Delay function to avoid overwhelming the server
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get total number of pages
    async getTotalPages() {
        try {
            const response = await axios.get(this.baseUrl);
            const $ = cheerio.load(response.data);
            
            // Find pagination links
            const paginationLinks = $('a[href*="page="]');
            let maxPage = 1;
            
            paginationLinks.each((i, element) => {
                const href = $(element).attr('href');
                const pageMatch = href.match(/page=(\d+)/);
                if (pageMatch) {
                    const pageNum = parseInt(pageMatch[1]);
                    if (pageNum > maxPage) {
                        maxPage = pageNum;
                    }
                }
            });
            
            console.log(`Found ${maxPage} pages to scrape`);
            return maxPage;
        } catch (error) {
            console.error('Error getting total pages:', error.message);
            return 1;
        }
    }

    // Extract store information from a page
    extractStoreInfo($) {
        const stores = [];
        
        // Find all store containers
        $('div').each((i, element) => {
            const $element = $(element);
            const storeLink = $element.find('a[href*="/vietnam-"]').first();
            
            if (storeLink.length > 0) {
                const storeName = storeLink.text().trim();
                const storeUrl = storeLink.attr('href');
                
                // Extract address information
                const addressLines = [];
                $element.find('br').replaceWith('\n');
                const textContent = $element.text();
                
                // Try to extract phone number
                const phoneLink = $element.find('a[href^="tel:"]');
                const phone = phoneLink.length > 0 ? phoneLink.attr('href').replace('tel:', '') : '';
                
                // Extract address from text content
                const lines = textContent.split('\n').map(line => line.trim()).filter(line => line);
                let address = '';
                let district = '';
                let city = '';
                
                // Find address components
                for (let j = 0; j < lines.length; j++) {
                    const line = lines[j];
                    if (line.includes('Số ') || line.includes('Đường ') || line.includes('Phố ')) {
                        address = line;
                    } else if (line.includes('Phường ') || line.includes('Quận ') || line.includes('Huyện ')) {
                        district = line;
                    } else if (line.length > 3 && !line.includes('Mở') && !line.includes('Gọi') && !phone.includes(line)) {
                        if (!address && !district) {
                            city = line;
                        }
                    }
                }
                
                if (storeName && storeName.includes('WinMart')) {
                    stores.push({
                        name: storeName,
                        address: address,
                        district: district,
                        city: city,
                        phone: phone,
                        url: storeUrl ? (storeUrl.startsWith('http') ? storeUrl : this.baseUrl + storeUrl) : ''
                    });
                }
            }
        });
        
        return stores;
    }

    // Scrape a single page
    async scrapePage(pageNum = 1) {
        try {
            const url = pageNum === 1 ? this.baseUrl : `${this.baseUrl}/?page=${pageNum}`;
            console.log(`Scraping page ${pageNum}: ${url}`);
            
            const response = await axios.get(url);
            const $ = cheerio.load(response.data);
            
            const pageStores = this.extractStoreInfo($);
            console.log(`Found ${pageStores.length} stores on page ${pageNum}`);
            
            return pageStores;
        } catch (error) {
            console.error(`Error scraping page ${pageNum}:`, error.message);
            return [];
        }
    }

    // Get detailed store information
    async getStoreDetails(storeUrl) {
        try {
            await this.sleep(this.delay);
            const response = await axios.get(storeUrl);
            const $ = cheerio.load(response.data);
            
            // Extract more detailed information
            const details = {
                hours: '',
                coordinates: { lat: null, lng: null }
            };
            
            // Try to find opening hours
            const hoursText = $('body').text();
            const hoursMatch = hoursText.match(/(\d{1,2}:\d{2}\s*(AM|PM).*?\d{1,2}:\d{2}\s*(AM|PM))/i);
            if (hoursMatch) {
                details.hours = hoursMatch[1];
            }
            
            // Try to extract coordinates from map links or scripts
            const mapScript = $('script').text();
            const coordMatch = mapScript.match(/lat["\s]*:[\s]*([0-9.-]+).*?lng["\s]*:[\s]*([0-9.-]+)/i);
            if (coordMatch) {
                details.coordinates.lat = parseFloat(coordMatch[1]);
                details.coordinates.lng = parseFloat(coordMatch[2]);
            }
            
            return details;
        } catch (error) {
            console.error(`Error getting store details from ${storeUrl}:`, error.message);
            return { hours: '', coordinates: { lat: null, lng: null } };
        }
    }

    // Main scraping function
    async scrapeAllStores(includeDetails = false) {
        try {
            console.log('Starting WinMart store scraping...');
            
            const totalPages = await this.getTotalPages();
            
            for (let page = 1; page <= totalPages; page++) {
                const pageStores = await this.scrapePage(page);
                this.stores.push(...pageStores);
                
                // Add delay between pages
                if (page < totalPages) {
                    await this.sleep(this.delay);
                }
            }
            
            console.log(`Total stores found: ${this.stores.length}`);
            
            // Get detailed information if requested
            if (includeDetails) {
                console.log('Getting detailed store information...');
                for (let i = 0; i < this.stores.length; i++) {
                    const store = this.stores[i];
                    if (store.url) {
                        const details = await this.getStoreDetails(store.url);
                        this.stores[i] = { ...store, ...details };
                        console.log(`Processed ${i + 1}/${this.stores.length} stores`);
                    }
                }
            }
            
            return this.stores;
        } catch (error) {
            console.error('Error in scrapeAllStores:', error.message);
            return this.stores;
        }
    }

    // Save stores to JSON file
    saveToJson(filename = 'winmart_stores.json') {
        try {
            fs.writeFileSync(filename, JSON.stringify(this.stores, null, 2), 'utf8');
            console.log(`Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving to JSON:', error.message);
        }
    }

    // Save stores to CSV file
    saveToCsv(filename = 'winmart_stores.csv') {
        try {
            if (this.stores.length === 0) {
                console.log('No stores to save');
                return;
            }
            
            const headers = ['Name', 'Address', 'District', 'City', 'Phone', 'URL', 'Hours', 'Latitude', 'Longitude'];
            const csvContent = [
                headers.join(','),
                ...this.stores.map(store => [
                    `"${store.name || ''}"`,
                    `"${store.address || ''}"`,
                    `"${store.district || ''}"`,
                    `"${store.city || ''}"`,
                    `"${store.phone || ''}"`,
                    `"${store.url || ''}"`,
                    `"${store.hours || ''}"`,
                    store.coordinates?.lat || '',
                    store.coordinates?.lng || ''
                ].join(','))
            ].join('\n');
            
            fs.writeFileSync(filename, csvContent, 'utf8');
            console.log(`Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving to CSV:', error.message);
        }
    }
}

// Usage example
async function main() {
    const scraper = new WinmartStoreScraper();
    
    // Scrape all stores (set to true to include detailed info like hours and coordinates)
    const stores = await scraper.scrapeAllStores(false);
    
    // Save results
    scraper.saveToJson('winmart_stores.json');
    scraper.saveToCsv('winmart_stores.csv');
    
    console.log(`\nScraping completed! Found ${stores.length} stores.`);
    console.log('Files saved:');
    console.log('- winmart_stores.json');
    console.log('- winmart_stores.csv');
}

// Export for use as module
module.exports = WinmartStoreScraper;

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}
