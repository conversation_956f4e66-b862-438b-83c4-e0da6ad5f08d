const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

class WinmartStoreScraper {
    constructor() {
        this.baseUrl = 'https://stores.winmart.vn';
        this.stores = [];
        this.delay = 1000; // 1 second delay between requests
    }

    // Delay function to avoid overwhelming the server
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get total number of pages
    async getTotalPages() {
        try {
            const response = await axios.get(this.baseUrl);
            const $ = cheerio.load(response.data);

            // Find pagination links
            const paginationLinks = $('a[href*="page="]');
            let maxPage = 1;

            paginationLinks.each((i, element) => {
                const href = $(element).attr('href');
                const pageMatch = href.match(/page=(\d+)/);
                if (pageMatch) {
                    const pageNum = parseInt(pageMatch[1]);
                    if (pageNum > maxPage) {
                        maxPage = pageNum;
                    }
                }
            });

            console.log(`Found ${maxPage} pages to scrape`);
            return maxPage;
        } catch (error) {
            console.error('Error getting total pages:', error.message);
            return 1;
        }
    }

    // Extract store information from a page
    extractStoreInfo($) {
        const stores = [];
        const seenUrls = new Set(); // To avoid duplicates

        // Find all store links with more specific pattern
        const storeLinks = $('a[href*="/vietnam-"], a[href*="/winmart-"]').filter((i, el) => {
            const href = $(el).attr('href') || '';
            return href.includes('supermarket-');
        });

        console.log(`Found ${storeLinks.length} potential store links`);

        storeLinks.each((i, element) => {
            try {
                const $link = $(element);
                const storeName = $link.text().trim();
                const storeUrl = $link.attr('href');

                // Skip if not a valid WinMart store
                if (!storeName || !storeName.includes('WinMart')) {
                    return;
                }

                const fullUrl = storeUrl ? (storeUrl.startsWith('http') ? storeUrl : this.baseUrl + storeUrl) : '';

                // Skip duplicates
                if (seenUrls.has(fullUrl)) {
                    console.log(`Skipping duplicate: ${storeName}`);
                    return;
                }
                seenUrls.add(fullUrl);

                // Find the store container - look for parent that contains phone link
                let $container = $link.parent();
                let attempts = 0;
                while ($container.length > 0 && attempts < 5) {
                    const phoneLink = $container.find('a[href^="tel:"]');
                    if (phoneLink.length > 0) {
                        break;
                    }
                    $container = $container.parent();
                    attempts++;
                }

                if ($container.length === 0) {
                    console.log(`Could not find container for: ${storeName}`);
                    return;
                }

                // Extract phone number
                const phoneLink = $container.find('a[href^="tel:"]');
                const phone = phoneLink.length > 0 ? phoneLink.attr('href').replace('tel:', '') : '';

                // Extract text content and split it properly
                const rawText = $container.text();

                // Try to split by phone number and other patterns
                let splitText = rawText;
                if (phone) {
                    splitText = splitText.replace(phone, `|${phone}|`);
                }
                splitText = splitText.replace(/Mở (lúc|cửa)/g, '|Mở $1');
                splitText = splitText.replace(/Gọi/g, '|Gọi');
                splitText = splitText.replace(/Bản đồ/g, '|Bản đồ');
                splitText = splitText.replace(/Trang web/g, '|Trang web');

                // Split by common address patterns - be more careful to preserve meaningful chunks
                splitText = splitText.replace(/(Số \d+[^,|]*)/g, '|$1');
                splitText = splitText.replace(/(Đường [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(Phố [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(Phường [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(Quận [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(Huyện [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(Tầng [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(TTTM [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(Vincom [^,|]+)/g, '|$1');
                splitText = splitText.replace(/(Lô [^,|]+)/g, '|$1');

                const lines = splitText.split('|')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);

                // Filter out unwanted lines
                const filteredLines = lines.filter(line => {
                    // Skip navigation links and status text
                    if (line.includes('Gọi') || line.includes('Bản đồ') || line.includes('Trang web')) return false;
                    if (line.includes('Mở lúc') || line.includes('Mở cửa')) return false;
                    if (phone && (line.includes(phone) || line.includes(phone.replace('+84', '0')))) return false;
                    if (line === storeName) return false;
                    return line.length > 3;
                });

                // Parse address components with improved logic
                const addressParts = [];
                let ward = '';
                let district = '';
                let city = '';

                // Define city/province keywords for better recognition
                const cityKeywords = [
                    'Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ',
                    'Ninh Bình', 'Hạ Long', 'Việt Trì', 'Thái Bình', 'Pleiku',
                    'Buôn Ma Thuột', 'Nha Trang', 'Cam Ranh', 'Bạc Liêu', 'Cà Mau',
                    'Sóc Trăng', 'Vĩnh Long', 'Trà Vinh', 'Long Xuyên', 'Rạch Giá',
                    'Vị Thanh', 'Kỳ Anh', 'Tuy Hòa', 'Lạng Sơn', 'Đồng Hới',
                    'Tân An', 'Tây Ninh', 'Bảo Lộc', 'Phủ Lý', 'Thanh Hóa',
                    'Quảng Ngãi', 'Hà Tĩnh', 'Bắc Ninh', 'Sa Đéc', 'Cao Lãnh',
                    'Móng Cái', 'Hòa Bình', 'Phú Thọ', 'Biên Hòa', 'Dĩ An',
                    'Long Thành', 'Mỹ Tho', 'Vũng Tàu', 'Đức Trọng', 'Yên Bái',
                    'Sơn La', 'Thái Nguyên', 'Huế', 'Nam Đàn', 'Uông Bí',
                    'Chí Linh', 'Tĩnh Gia', 'Tuyên Quang', 'Vinh', 'Kon Tum',
                    'Quy Nhơn', 'Lai Châu', 'Ninh Hòa', 'Bắc Kạn', 'Cẩm Phả',
                    'Thái Hòa'
                ];

                // First pass: Extract city/province
                filteredLines.forEach(line => {
                    cityKeywords.forEach(cityName => {
                        if (line.includes(cityName) && !city) {
                            city = cityName;
                        }
                    });
                });

                // Second pass: Extract ward, district, and address
                filteredLines.forEach(line => {
                    // Skip if this line contains the city we already found
                    if (city && line.includes(city)) {
                        // Try to extract ward/district from the same line
                        const lineWithoutCity = line.replace(city, '').trim();

                        // Check for ward patterns
                        const wardMatch = lineWithoutCity.match(/(Phường\s+[^,]+)/);
                        if (wardMatch && !ward) {
                            ward = wardMatch[1].trim();
                        }

                        // Check for district patterns
                        const districtMatch = lineWithoutCity.match(/(Quận\s+[^,]+|Huyện\s+[^,]+|Thị\s+xã\s+[^,]+)/);
                        if (districtMatch && !district) {
                            district = districtMatch[1].trim();
                        }

                        // Add remaining parts to address as a single piece
                        let remainingText = lineWithoutCity
                            .replace(ward, '')
                            .replace(district, '')
                            .trim();

                        // Clean up extra spaces and commas
                        remainingText = remainingText.replace(/\s+/g, ' ').replace(/,+/g, ',').replace(/^,|,$/, '').trim();

                        if (remainingText && remainingText.length > 2) {
                            addressParts.push(remainingText);
                        }

                        return; // Skip adding this line to address parts
                    }

                    // Check for standalone ward/district patterns
                    if (line.match(/^(Phường\s+[^,]+)/)) {
                        if (!ward) ward = line.trim();
                        return;
                    }

                    if (line.match(/^(Quận\s+[^,]+|Huyện\s+[^,]+|Thị\s+xã\s+[^,]+)/)) {
                        if (!district) district = line.trim();
                        return;
                    }

                    // Everything else goes to address
                    if (line.trim() && line.length > 2) {
                        // Clean up the line
                        let cleanLine = line.trim();

                        // Remove any ward/district info that might be embedded
                        cleanLine = cleanLine.replace(/(Phường\s+[^,]+|Quận\s+[^,]+|Huyện\s+[^,]+)/g, '');

                        // Remove city names
                        cityKeywords.forEach(cityName => {
                            cleanLine = cleanLine.replace(new RegExp(cityName, 'g'), '');
                        });

                        // Clean up extra commas and spaces
                        cleanLine = cleanLine.replace(/,+/g, ',').replace(/^,|,$/, '').trim();

                        if (cleanLine && cleanLine.length > 2) {
                            addressParts.push(cleanLine);
                        }
                    }
                });

                // Clean up address parts and join
                const cleanedAddressParts = addressParts
                    .map(part => part.replace(/,+$/, '').trim())
                    .filter(part => part.length > 0);

                const fullAddress = cleanedAddressParts.join(', ');

                // If no city found, try to infer from store name or URL
                if (!city) {
                    const nameMatch = storeName.match(/(Hà Nội|Hồ Chí Minh|Đà Nẵng|Hải Phòng|[A-Za-zÀ-ỹ\s]+)/);
                    if (nameMatch) {
                        const potentialCity = nameMatch[1];
                        if (cityKeywords.includes(potentialCity)) {
                            city = potentialCity;
                        }
                    }
                }

                const storeData = {
                    name: storeName,
                    address: fullAddress,
                    ward: ward,
                    district: district,
                    city: city,
                    phone: phone,
                    url: fullUrl
                };

                console.log(`Extracted:`, storeData);

                // Only add if we have meaningful data
                if (storeData.name && (storeData.address || storeData.district || storeData.city)) {
                    stores.push(storeData);
                } else {
                    console.log(`Skipping store with insufficient data: ${storeName}`);
                }

            } catch (error) {
                console.error(`Error extracting store info:`, error.message);
            }
        });

        return stores;
    }

    // Scrape a single page
    async scrapePage(pageNum = 1) {
        try {
            const url = pageNum === 1 ? this.baseUrl : `${this.baseUrl}/?page=${pageNum}`;
            console.log(`Scraping page ${pageNum}: ${url}`);

            const response = await axios.get(url);
            const $ = cheerio.load(response.data);

            const pageStores = this.extractStoreInfo($);
            console.log(`Found ${pageStores.length} stores on page ${pageNum}`);

            return pageStores;
        } catch (error) {
            console.error(`Error scraping page ${pageNum}:`, error.message);
            return [];
        }
    }

    // Get detailed store information
    async getStoreDetails(storeUrl) {
        try {
            await this.sleep(this.delay);
            const response = await axios.get(storeUrl);
            const $ = cheerio.load(response.data);

            // Extract more detailed information
            const details = {
                hours: '',
                coordinates: { lat: null, lng: null }
            };

            // Try to find opening hours
            const hoursText = $('body').text();
            const hoursMatch = hoursText.match(/(\d{1,2}:\d{2}\s*(AM|PM).*?\d{1,2}:\d{2}\s*(AM|PM))/i);
            if (hoursMatch) {
                details.hours = hoursMatch[1];
            }

            // Try to extract coordinates from map links or scripts
            const mapScript = $('script').text();
            const coordMatch = mapScript.match(/lat["\s]*:[\s]*([0-9.-]+).*?lng["\s]*:[\s]*([0-9.-]+)/i);
            if (coordMatch) {
                details.coordinates.lat = parseFloat(coordMatch[1]);
                details.coordinates.lng = parseFloat(coordMatch[2]);
            }

            return details;
        } catch (error) {
            console.error(`Error getting store details from ${storeUrl}:`, error.message);
            return { hours: '', coordinates: { lat: null, lng: null } };
        }
    }

    // Remove duplicates from the stores array
    removeDuplicates() {
        const seenUrls = new Set();
        const seenIds = new Set();
        const uniqueStores = [];

        for (const store of this.stores) {
            // Create a unique identifier
            const storeId = `${store.name}-${store.address}-${store.city}`;
            const storeUrl = store.url || '';

            // Skip if we've seen this URL or very similar store
            if (seenUrls.has(storeUrl) || seenIds.has(storeId)) {
                continue;
            }

            seenUrls.add(storeUrl);
            seenIds.add(storeId);
            uniqueStores.push(store);
        }

        console.log(`Removed ${this.stores.length - uniqueStores.length} duplicate stores`);
        this.stores = uniqueStores;
    }

    // Main scraping function
    async scrapeAllStores(includeDetails = false) {
        try {
            console.log('Starting WinMart store scraping...');

            const totalPages = await this.getTotalPages();

            for (let page = 1; page <= totalPages; page++) {
                const pageStores = await this.scrapePage(page);
                this.stores.push(...pageStores);

                // Add delay between pages
                if (page < totalPages) {
                    await this.sleep(this.delay);
                }
            }

            console.log(`Total stores found before deduplication: ${this.stores.length}`);

            // Remove duplicates
            this.removeDuplicates();

            console.log(`Total unique stores: ${this.stores.length}`);

            // Get detailed information if requested
            if (includeDetails) {
                console.log('Getting detailed store information...');
                for (let i = 0; i < this.stores.length; i++) {
                    const store = this.stores[i];
                    if (store.url) {
                        const details = await this.getStoreDetails(store.url);
                        this.stores[i] = { ...store, ...details };
                        console.log(`Processed ${i + 1}/${this.stores.length} stores`);
                    }
                }
            }

            return this.stores;
        } catch (error) {
            console.error('Error in scrapeAllStores:', error.message);
            return this.stores;
        }
    }

    // Save stores to JSON file
    saveToJson(filename = 'winmart_stores.json') {
        try {
            fs.writeFileSync(filename, JSON.stringify(this.stores, null, 2), 'utf8');
            console.log(`Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving to JSON:', error.message);
        }
    }

    // Save stores to CSV file
    saveToCsv(filename = 'winmart_stores.csv') {
        try {
            if (this.stores.length === 0) {
                console.log('No stores to save');
                return;
            }

            const headers = ['Name', 'Address', 'Ward', 'District', 'City', 'Phone', 'URL', 'Hours', 'Latitude', 'Longitude'];
            const csvContent = [
                headers.join(','),
                ...this.stores.map(store => [
                    `"${store.name || ''}"`,
                    `"${store.address || ''}"`,
                    `"${store.ward || ''}"`,
                    `"${store.district || ''}"`,
                    `"${store.city || ''}"`,
                    `"${store.phone || ''}"`,
                    `"${store.url || ''}"`,
                    `"${store.hours || ''}"`,
                    store.coordinates?.lat || '',
                    store.coordinates?.lng || ''
                ].join(','))
            ].join('\n');

            fs.writeFileSync(filename, csvContent, 'utf8');
            console.log(`Stores saved to ${filename}`);
        } catch (error) {
            console.error('Error saving to CSV:', error.message);
        }
    }
}

// Usage example
async function main() {
    const scraper = new WinmartStoreScraper();

    // Scrape all stores (set to true to include detailed info like hours and coordinates)
    const stores = await scraper.scrapeAllStores(false);

    // Save results
    scraper.saveToJson('winmart_stores.json');
    scraper.saveToCsv('winmart_stores.csv');

    console.log(`\nScraping completed! Found ${stores.length} stores.`);
    console.log('Files saved:');
    console.log('- winmart_stores.json');
    console.log('- winmart_stores.csv');
}

// Export for use as module
module.exports = WinmartStoreScraper;

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}
