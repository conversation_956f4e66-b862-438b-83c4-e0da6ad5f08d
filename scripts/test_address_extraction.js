const axios = require('axios');
const cheerio = require('cheerio');

async function testAddressExtraction() {
    try {
        const testUrl = 'https://stores.winmart.vn/vietnam-sieu-thi-winmart-trung-hoa-supermarket-hoang-dao-thuy-cau-giay-390058/Home';
        
        console.log(`Testing address extraction from: ${testUrl}`);
        
        const response = await axios.get(testUrl);
        const $ = cheerio.load(response.data);
        
        console.log('\n=== TESTING NEW LOGIC ===');
        
        // Extract full address from specific class
        let fullAddress = '';
        
        // First try the specific class we found in testing
        const addressEl = $('.store_information_02__text').first();
        console.log(`Found ${$('.store_information_02__text').length} elements with class store_information_02__text`);
        
        if (addressEl.length > 0) {
            let address = addressEl.text().trim();
            console.log(`First element text: "${address}"`);
            
            // Clean up the address
            address = address.replace(/\s+/g, ' ').trim();
            
            // Check if this looks like a real address (not a phone number or other info)
            if (address && address.length > 10 && !address.match(/^\+?\d+$/) && !address.includes('Mở cửa')) {
                fullAddress = address;
                console.log(`✓ Found address: "${fullAddress}"`);
            } else {
                console.log(`✗ Address doesn't meet criteria`);
            }
        }
        
        // Test all elements with this class
        console.log('\n=== ALL store_information_02__text ELEMENTS ===');
        $('.store_information_02__text').each((i, el) => {
            const text = $(el).text().trim();
            console.log(`Element ${i+1}: "${text}"`);
        });
        
        // Test store_information_02__item-list
        console.log('\n=== ALL store_information_02__item-list ELEMENTS ===');
        $('.store_information_02__item-list').each((i, el) => {
            const text = $(el).text().trim();
            console.log(`Element ${i+1}: "${text}"`);
        });
        
        // Extract store name
        let storeName = '';
        const nameSelectors = [
            'h1',
            '.store-name',
            '.title',
            '[class*="name"]',
            '[class*="title"]'
        ];
        
        for (const selector of nameSelectors) {
            const nameEl = $(selector).first();
            if (nameEl.length > 0) {
                const name = nameEl.text().trim();
                if (name && name.includes('WinMart')) {
                    storeName = name;
                    break;
                }
            }
        }
        
        // Extract phone
        let phone = '';
        const phoneLink = $('a[href^="tel:"]').first();
        if (phoneLink.length > 0) {
            phone = phoneLink.attr('href').replace('tel:', '');
        }
        
        console.log('\n=== FINAL RESULTS ===');
        console.log(`Store Name: "${storeName}"`);
        console.log(`Full Address: "${fullAddress}"`);
        console.log(`Phone: "${phone}"`);
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

testAddressExtraction();
