#!/usr/bin/env python3
"""
Test script for WinMart Store Scraper
Tests the scraper on a few pages to verify data quality
"""

import requests
from bs4 import BeautifulSoup
import json
import re
from urllib.parse import urljoin
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WinmartTestScraper:
    def __init__(self):
        self.base_url = 'https://stores.winmart.vn'
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def test_single_page(self, page_num=1):
        """Test extraction on a single page with detailed debugging"""
        try:
            url = self.base_url if page_num == 1 else f"{self.base_url}/?page={page_num}"
            logger.info(f"Testing page {page_num}: {url}")
            
            response = self.session.get(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find all store links
            store_links = soup.find_all('a', href=re.compile(r'/(vietnam-.*-supermarket-|winmart-.*-supermarket-)'))
            logger.info(f"Found {len(store_links)} store links")
            
            stores = []
            seen_urls = set()
            
            for i, link in enumerate(store_links):
                try:
                    store_url = urljoin(self.base_url, link.get('href', ''))
                    
                    if store_url in seen_urls:
                        logger.info(f"Skipping duplicate URL: {store_url}")
                        continue
                    seen_urls.add(store_url)
                    
                    store_name = link.get_text(strip=True)
                    logger.info(f"\n--- Processing store {i+1}: {store_name} ---")
                    
                    if not store_name or 'WinMart' not in store_name:
                        logger.info(f"Skipping non-WinMart store: {store_name}")
                        continue
                    
                    # Find store container
                    store_container = link.find_parent()
                    attempts = 0
                    while store_container and attempts < 5:
                        phone_link = store_container.find('a', href=re.compile(r'tel:'))
                        if phone_link:
                            break
                        store_container = store_container.find_parent()
                        attempts += 1
                    
                    if not store_container:
                        logger.warning(f"Could not find container for {store_name}")
                        continue
                    
                    # Extract phone
                    phone_link = store_container.find('a', href=re.compile(r'tel:'))
                    phone = phone_link.get('href', '').replace('tel:', '') if phone_link else ''
                    
                    # Debug: print raw text
                    raw_text = store_container.get_text(separator='|', strip=True)
                    logger.info(f"Raw text: {raw_text}")
                    
                    # Extract and clean lines
                    lines = [line.strip() for line in raw_text.split('|') if line.strip()]
                    logger.info(f"All lines: {lines}")
                    
                    # Filter lines
                    filtered_lines = []
                    for line in lines:
                        if any(skip in line for skip in ['Gọi', 'Bản đồ', 'Trang web', 'Mở lúc', 'Mở cửa']):
                            logger.info(f"Filtering out: {line}")
                            continue
                        if phone and phone.replace('+84', '0') in line:
                            logger.info(f"Filtering out phone line: {line}")
                            continue
                        if line == store_name:
                            logger.info(f"Filtering out store name: {line}")
                            continue
                        if len(line) > 3:
                            filtered_lines.append(line)
                    
                    logger.info(f"Filtered lines: {filtered_lines}")
                    
                    # Parse address components
                    address_parts = []
                    district = ''
                    city = ''
                    
                    for line in filtered_lines:
                        if any(keyword in line for keyword in ['Phường ', 'Quận ', 'Huyện ', 'Thị xã ', 'Thành phố ']):
                            if not district:
                                district = line
                                logger.info(f"Found district: {district}")
                        elif len(line) > 2 and not any(keyword in line for keyword in ['Số ', 'Đường ', 'Phố ', 'Tầng ', 'TTTM', 'Vincom', 'Lô ']):
                            city = line
                            logger.info(f"Found city: {city}")
                        else:
                            address_parts.append(line)
                            logger.info(f"Found address part: {line}")
                    
                    full_address = ', '.join(address_parts) if address_parts else ''
                    
                    # Clean up
                    if district and city and district in city:
                        city = city.replace(district, '').strip().strip(',').strip()
                    
                    store_data = {
                        'name': store_name,
                        'address': full_address,
                        'district': district,
                        'city': city,
                        'phone': phone,
                        'url': store_url
                    }
                    
                    logger.info(f"Final store data: {json.dumps(store_data, ensure_ascii=False, indent=2)}")
                    
                    if store_data['name'] and (store_data['address'] or store_data['district'] or store_data['city']):
                        stores.append(store_data)
                    else:
                        logger.warning(f"Skipping store with insufficient data: {store_name}")
                    
                except Exception as e:
                    logger.error(f"Error processing store {i+1}: {e}")
                    continue
            
            logger.info(f"\nPage {page_num} summary:")
            logger.info(f"Total links found: {len(store_links)}")
            logger.info(f"Valid stores extracted: {len(stores)}")
            
            return stores
            
        except Exception as e:
            logger.error(f"Error testing page {page_num}: {e}")
            return []

    def test_multiple_pages(self, num_pages=3):
        """Test extraction on multiple pages"""
        all_stores = []
        
        for page in range(1, num_pages + 1):
            logger.info(f"\n{'='*50}")
            logger.info(f"TESTING PAGE {page}")
            logger.info(f"{'='*50}")
            
            page_stores = self.test_single_page(page)
            all_stores.extend(page_stores)
            
            logger.info(f"Page {page} completed. Stores found: {len(page_stores)}")
        
        # Remove duplicates
        seen_urls = set()
        unique_stores = []
        
        for store in all_stores:
            if store['url'] not in seen_urls:
                seen_urls.add(store['url'])
                unique_stores.append(store)
        
        logger.info(f"\nFINAL SUMMARY:")
        logger.info(f"Total stores before deduplication: {len(all_stores)}")
        logger.info(f"Total unique stores: {len(unique_stores)}")
        logger.info(f"Duplicates removed: {len(all_stores) - len(unique_stores)}")
        
        # Save test results
        with open('test_winmart_stores.json', 'w', encoding='utf-8') as f:
            json.dump(unique_stores, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Test results saved to test_winmart_stores.json")
        
        # Print sample stores
        logger.info(f"\nSAMPLE STORES:")
        for i, store in enumerate(unique_stores[:5]):
            logger.info(f"{i+1}. {store['name']}")
            logger.info(f"   Address: {store['address']}")
            logger.info(f"   District: {store['district']}")
            logger.info(f"   City: {store['city']}")
            logger.info(f"   Phone: {store['phone']}")
            logger.info(f"   URL: {store['url']}")
            logger.info("")
        
        return unique_stores

def main():
    """Test the scraper"""
    tester = WinmartTestScraper()
    
    # Test first 3 pages
    stores = tester.test_multiple_pages(3)
    
    print(f"\nTest completed! Found {len(stores)} unique stores.")
    print("Check the logs above for detailed extraction process.")
    print("Results saved to test_winmart_stores.json")

if __name__ == "__main__":
    main()
