const axios = require('axios');
const fs = require('fs');

async function quickTest() {
    try {
        console.log('Quick test with a few provinces...\n');
        
        const baseApiUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1';
        
        // Test with 3 small provinces
        const testProvinces = ['AGG', 'BGG', 'BTG']; // <PERSON>, <PERSON><PERSON>, <PERSON>
        const allStores = [];
        
        for (const provinceCode of testProvinces) {
            console.log(`\nProcessing province: ${provinceCode}`);
            
            // Get first page to check pagination
            const firstResponse = await axios.get(`${baseApiUrl}/store-by-province`, {
                params: {
                    PageNumber: 1,
                    PageSize: 1,
                    ProvinceCode: provinceCode
                }
            });
            
            console.log(`Province: ${firstResponse.data.provinceName}`);
            console.log(`Paging: ${JSON.stringify(firstResponse.data.paging)}`);
            
            const totalPages = firstResponse.data.paging?.totalPages || 1;
            console.log(`Total pages: ${totalPages}`);
            
            // Get all pages for this province
            for (let page = 1; page <= totalPages; page++) {
                console.log(`  Fetching page ${page}/${totalPages}...`);
                
                const response = await axios.get(`${baseApiUrl}/store-by-province`, {
                    params: {
                        PageNumber: page,
                        PageSize: 1,
                        ProvinceCode: provinceCode
                    }
                });
                
                // Extract stores
                if (response.data && response.data.data) {
                    response.data.data.forEach(district => {
                        if (district.wardStores) {
                            district.wardStores.forEach(ward => {
                                if (ward.stores && ward.stores.length > 0) {
                                    ward.stores.forEach(store => {
                                        allStores.push({
                                            storeCode: store.storeCode,
                                            storeName: store.storeName,
                                            address: store.officeAddress,
                                            ward: store.wardName,
                                            district: store.districtName,
                                            province: store.provinceName,
                                            phone: store.contactMobile,
                                            latitude: store.latitude,
                                            longitude: store.longitude,
                                            isOpen: store.isOpen,
                                            activeStatus: store.activeStatus,
                                            chainId: store.chainId
                                        });
                                    });
                                }
                            });
                        }
                    });
                }
                
                // Small delay between pages
                if (page < totalPages) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
            
            console.log(`✓ Completed ${provinceCode}`);
            
            // Delay between provinces
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log(`\n=== RESULTS ===`);
        console.log(`Total stores found: ${allStores.length}`);
        
        // Group by province
        const byProvince = {};
        allStores.forEach(store => {
            const province = store.province || 'Unknown';
            byProvince[province] = (byProvince[province] || 0) + 1;
        });
        
        console.log('\nStores by province:');
        Object.entries(byProvince).forEach(([province, count]) => {
            console.log(`  ${province}: ${count} stores`);
        });
        
        // Show sample stores
        console.log('\nSample stores:');
        allStores.slice(0, 5).forEach((store, i) => {
            console.log(`\n${i+1}. ${store.storeName}`);
            console.log(`   Address: ${store.address}`);
            console.log(`   Province: ${store.province}`);
            console.log(`   Status: ${store.activeStatus}`);
        });
        
        // Save results
        fs.writeFileSync('quick_test_results.json', JSON.stringify(allStores, null, 2), 'utf8');
        console.log('\nResults saved to quick_test_results.json');
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

quickTest();
