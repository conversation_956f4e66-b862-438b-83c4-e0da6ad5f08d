const axios = require('axios');
const fs = require('fs');

async function testAPIPagination() {
    try {
        console.log('Testing WinMart API with PageSize=1...\n');

        const baseApiUrl = 'https://api-crownx.winmart.vn/mt/api/web/v1';

        // Test with <PERSON><PERSON> first
        const provinceCode = 'HNI';
        console.log(`Testing province: ${provinceCode}`);

        const allStores = [];
        let pageNumber = 1;
        let hasMorePages = true;

        while (hasMorePages && pageNumber <= 10) { // Limit to 10 pages for testing
            console.log(`\nFetching page ${pageNumber} for ${provinceCode}...`);

            const response = await axios.get(`${baseApiUrl}/store-by-province`, {
                params: {
                    PageNumber: pageNumber,
                    PageSize: 1,
                    ProvinceCode: provinceCode
                }
            });

            console.log(`Response status: ${response.status}`);
            console.log(`Response data keys:`, Object.keys(response.data));

            if (response.data.paging) {
                console.log(`Paging info:`, response.data.paging);
            }

            if (response.data && response.data.data && response.data.data.length > 0) {
                console.log(`Found ${response.data.data.length} districts on page ${pageNumber}`);

                // Count stores in this page
                let storesInPage = 0;
                response.data.data.forEach(district => {
                    if (district.wardStores) {
                        district.wardStores.forEach(ward => {
                            if (ward.stores && ward.stores.length > 0) {
                                storesInPage += ward.stores.length;
                                ward.stores.forEach(store => {
                                    const cleanStore = {
                                        storeCode: store.storeCode || '',
                                        storeName: store.storeName || '',
                                        address: store.officeAddress || '',
                                        ward: store.wardName || '',
                                        district: store.districtName || '',
                                        province: store.provinceName || '',
                                        phone: store.contactMobile || '',
                                        latitude: store.latitude || null,
                                        longitude: store.longitude || null,
                                        isOpen: store.isOpen || false,
                                        activeStatus: store.activeStatus || '',
                                        supportDelivering: store.supportDelivering || false,
                                        chainId: store.chainId || ''
                                    };

                                    allStores.push(cleanStore);
                                });
                            }
                        });
                    }
                });

                console.log(`Stores found on page ${pageNumber}: ${storesInPage}`);

                // Check pagination
                if (response.data.paging) {
                    const totalPages = response.data.paging.totalPages || response.data.paging.totalPage || 1;
                    console.log(`Page ${pageNumber}/${totalPages}`);

                    if (pageNumber >= totalPages) {
                        hasMorePages = false;
                        console.log('Reached last page');
                    } else {
                        pageNumber++;
                    }
                } else {
                    hasMorePages = false;
                    console.log('No pagination info, assuming single page');
                }
            } else {
                hasMorePages = false;
                console.log('No more data found');
            }

            // Add delay
            if (hasMorePages) {
                console.log('Waiting 1 second...');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        console.log(`\n=== SUMMARY ===`);
        console.log(`Total stores found: ${allStores.length}`);
        console.log(`Pages processed: ${pageNumber - 1}`);

        // Save test results
        fs.writeFileSync('test_api_pagination_results.json', JSON.stringify(allStores, null, 2), 'utf8');
        console.log('Results saved to test_api_pagination_results.json');

        // Show sample stores
        console.log('\nSample stores:');
        allStores.slice(0, 3).forEach((store, i) => {
            console.log(`\n${i+1}. ${store.storeName}`);
            console.log(`   Code: ${store.storeCode}`);
            console.log(`   Address: ${store.address}`);
            console.log(`   Ward: ${store.ward}`);
            console.log(`   District: ${store.district}`);
            console.log(`   Province: ${store.province}`);
            console.log(`   Phone: ${store.phone}`);
            console.log(`   Status: ${store.activeStatus}`);
            console.log(`   Coordinates: ${store.latitude}, ${store.longitude}`);
        });

    } catch (error) {
        console.error('Error:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

testAPIPagination();
