const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

class WinmartTestScraper {
    constructor() {
        this.baseUrl = 'https://stores.winmart.vn';
        this.stores = [];
        this.delay = 1000;
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Extract store information from a page with detailed logging
    extractStoreInfo($) {
        const stores = [];
        const seenUrls = new Set();

        // Find all store links
        const storeLinks = $('a[href*="/vietnam-"], a[href*="/winmart-"]').filter((i, el) => {
            const href = $(el).attr('href') || '';
            return href.includes('supermarket-');
        });

        console.log(`\nFound ${storeLinks.length} potential store links`);

        storeLinks.each((i, element) => {
            try {
                const $link = $(element);
                const storeName = $link.text().trim();
                const storeUrl = $link.attr('href');

                console.log(`\n--- Processing store ${i+1}: ${storeName} ---`);

                if (!storeName || !storeName.includes('WinMart')) {
                    console.log(`Skipping non-WinMart: ${storeName}`);
                    return;
                }

                const fullUrl = storeUrl ? (storeUrl.startsWith('http') ? storeUrl : this.baseUrl + storeUrl) : '';

                if (seenUrls.has(fullUrl)) {
                    console.log(`Skipping duplicate URL: ${fullUrl}`);
                    return;
                }
                seenUrls.add(fullUrl);

                // Find container with phone
                let $container = $link.parent();
                let attempts = 0;
                while ($container.length > 0 && attempts < 5) {
                    const phoneLink = $container.find('a[href^="tel:"]');
                    if (phoneLink.length > 0) {
                        break;
                    }
                    $container = $container.parent();
                    attempts++;
                }

                if ($container.length === 0) {
                    console.log(`Could not find container for: ${storeName}`);
                    return;
                }

                // Extract phone
                const phoneLink = $container.find('a[href^="tel:"]');
                const phone = phoneLink.length > 0 ? phoneLink.attr('href').replace('tel:', '') : '';

                // Get text from individual elements instead of combined text
                const textElements = [];
                $container.find('*').contents().each((i, node) => {
                    if (node.nodeType === 3) { // Text node
                        const text = $(node).text().trim();
                        if (text && text.length > 0) {
                            textElements.push(text);
                        }
                    }
                });

                // Also try to get text from direct children
                $container.children().each((i, child) => {
                    const $child = $(child);
                    if ($child.is('br')) return;
                    const childText = $child.text().trim();
                    if (childText && !textElements.includes(childText)) {
                        textElements.push(childText);
                    }
                });

                // Fallback: split the combined text by common patterns
                const rawText = $container.text();
                console.log(`Raw text: ${rawText.substring(0, 200)}...`);

                // Try to split by phone number and other patterns
                let splitText = rawText;
                if (phone) {
                    splitText = splitText.replace(phone, `|${phone}|`);
                }
                splitText = splitText.replace(/Mở (lúc|cửa)/g, '|Mở $1');
                splitText = splitText.replace(/Gọi/g, '|Gọi');
                splitText = splitText.replace(/Bản đồ/g, '|Bản đồ');
                splitText = splitText.replace(/Trang web/g, '|Trang web');

                // Split by common address patterns
                splitText = splitText.replace(/(Số \d+)/g, '|$1');
                splitText = splitText.replace(/(Đường [^|]+)/g, '|$1');
                splitText = splitText.replace(/(Phố [^|]+)/g, '|$1');
                splitText = splitText.replace(/(Phường [^|]+)/g, '|$1');
                splitText = splitText.replace(/(Quận [^|]+)/g, '|$1');
                splitText = splitText.replace(/(Huyện [^|]+)/g, '|$1');
                splitText = splitText.replace(/(Tầng [^|]+)/g, '|$1');
                splitText = splitText.replace(/(TTTM [^|]+)/g, '|$1');
                splitText = splitText.replace(/(Vincom [^|]+)/g, '|$1');

                const lines = splitText.split('|')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);

                console.log(`Split lines (${lines.length}):`, lines);

                // Filter lines
                const filteredLines = lines.filter(line => {
                    if (line.includes('Gọi') || line.includes('Bản đồ') || line.includes('Trang web')) return false;
                    if (line.includes('Mở lúc') || line.includes('Mở cửa')) return false;
                    if (phone && (line.includes(phone) || line.includes(phone.replace('+84', '0')))) return false;
                    if (line === storeName) return false;
                    return line.length > 3;
                });

                console.log(`Filtered lines (${filteredLines.length}):`, filteredLines);

                // Parse address components with improved logic
                const addressParts = [];
                let ward = '';
                let district = '';
                let city = '';

                // Define city/province keywords for better recognition
                const cityKeywords = [
                    'Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ',
                    'Ninh Bình', 'Hạ Long', 'Việt Trì', 'Thái Bình', 'Pleiku',
                    'Buôn Ma Thuột', 'Nha Trang', 'Cam Ranh', 'Bạc Liêu', 'Cà Mau',
                    'Sóc Trăng', 'Vĩnh Long', 'Trà Vinh', 'Long Xuyên', 'Rạch Giá',
                    'Vị Thanh', 'Kỳ Anh', 'Tuy Hòa', 'Lạng Sơn', 'Đồng Hới',
                    'Tân An', 'Tây Ninh', 'Bảo Lộc', 'Phủ Lý', 'Thanh Hóa',
                    'Quảng Ngãi', 'Hà Tĩnh', 'Bắc Ninh', 'Sa Đéc', 'Cao Lãnh',
                    'Móng Cái', 'Hòa Bình', 'Phú Thọ', 'Biên Hòa', 'Dĩ An',
                    'Long Thành', 'Mỹ Tho', 'Vũng Tàu', 'Đức Trọng', 'Yên Bái',
                    'Sơn La', 'Thái Nguyên', 'Huế', 'Nam Đàn', 'Uông Bí',
                    'Chí Linh', 'Tĩnh Gia', 'Tuyên Quang', 'Vinh', 'Kon Tum',
                    'Quy Nhơn', 'Lai Châu', 'Ninh Hòa', 'Bắc Kạn', 'Cẩm Phả',
                    'Thái Hòa'
                ];

                // First pass: Extract city/province
                filteredLines.forEach(line => {
                    cityKeywords.forEach(cityName => {
                        if (line.includes(cityName) && !city) {
                            city = cityName;
                            console.log(`Found city: ${city}`);
                        }
                    });
                });

                // Second pass: Extract ward, district, and address
                filteredLines.forEach(line => {
                    // Skip if this line contains the city we already found
                    if (city && line.includes(city)) {
                        // Try to extract ward/district from the same line
                        const lineWithoutCity = line.replace(city, '').trim();

                        // Check for ward patterns
                        const wardMatch = lineWithoutCity.match(/(Phường\s+[^,]+)/);
                        if (wardMatch && !ward) {
                            ward = wardMatch[1].trim();
                            console.log(`Found ward: ${ward}`);
                        }

                        // Check for district patterns
                        const districtMatch = lineWithoutCity.match(/(Quận\s+[^,]+|Huyện\s+[^,]+|Thị\s+xã\s+[^,]+)/);
                        if (districtMatch && !district) {
                            district = districtMatch[1].trim();
                            console.log(`Found district: ${district}`);
                        }

                        // Add remaining parts to address if they're meaningful
                        const remainingParts = lineWithoutCity
                            .replace(ward, '')
                            .replace(district, '')
                            .split(/[,\s]+/)
                            .filter(part => part.length > 2 && !part.match(/^(Phường|Quận|Huyện|Thị|xã)$/));

                        remainingParts.forEach(part => {
                            if (part.trim() && !addressParts.includes(part.trim())) {
                                addressParts.push(part.trim());
                                console.log(`Found address part: ${part.trim()}`);
                            }
                        });

                        return; // Skip adding this line to address parts
                    }

                    // Check for standalone ward/district patterns
                    if (line.match(/^(Phường\s+[^,]+)/)) {
                        if (!ward) {
                            ward = line.trim();
                            console.log(`Found standalone ward: ${ward}`);
                        }
                        return;
                    }

                    if (line.match(/^(Quận\s+[^,]+|Huyện\s+[^,]+|Thị\s+xã\s+[^,]+)/)) {
                        if (!district) {
                            district = line.trim();
                            console.log(`Found standalone district: ${district}`);
                        }
                        return;
                    }

                    // Everything else goes to address
                    if (line.trim() && line.length > 2) {
                        // Clean up the line
                        let cleanLine = line.trim();

                        // Remove any ward/district info that might be embedded
                        cleanLine = cleanLine.replace(/(Phường\s+[^,]+|Quận\s+[^,]+|Huyện\s+[^,]+)/g, '');

                        // Remove city names
                        cityKeywords.forEach(cityName => {
                            cleanLine = cleanLine.replace(new RegExp(cityName, 'g'), '');
                        });

                        // Clean up extra commas and spaces
                        cleanLine = cleanLine.replace(/,+/g, ',').replace(/^,|,$/, '').trim();

                        if (cleanLine && cleanLine.length > 2) {
                            addressParts.push(cleanLine);
                            console.log(`Found address part: ${cleanLine}`);
                        }
                    }
                });

                // Clean up address parts and join
                const cleanedAddressParts = addressParts
                    .map(part => part.replace(/,+$/, '').trim())
                    .filter(part => part.length > 0);

                const fullAddress = cleanedAddressParts.join(', ');

                const storeData = {
                    name: storeName,
                    address: fullAddress,
                    ward: ward,
                    district: district,
                    city: city,
                    phone: phone,
                    url: fullUrl
                };

                console.log(`Final data:`, JSON.stringify(storeData, null, 2));

                if (storeData.name && (storeData.address || storeData.district || storeData.city)) {
                    stores.push(storeData);
                    console.log(`✓ Added store`);
                } else {
                    console.log(`✗ Skipped - insufficient data`);
                }

            } catch (error) {
                console.error(`Error processing store ${i+1}:`, error.message);
            }
        });

        return stores;
    }

    async testPage(pageNum = 1) {
        try {
            const url = pageNum === 1 ? this.baseUrl : `${this.baseUrl}/?page=${pageNum}`;
            console.log(`\n${'='.repeat(60)}`);
            console.log(`TESTING PAGE ${pageNum}: ${url}`);
            console.log(`${'='.repeat(60)}`);

            const response = await axios.get(url);
            const $ = cheerio.load(response.data);

            const stores = this.extractStoreInfo($);

            console.log(`\nPage ${pageNum} Summary:`);
            console.log(`- Stores extracted: ${stores.length}`);

            return stores;
        } catch (error) {
            console.error(`Error testing page ${pageNum}:`, error.message);
            return [];
        }
    }

    async testMultiplePages(numPages = 2) {
        const allStores = [];

        for (let page = 1; page <= numPages; page++) {
            const pageStores = await this.testPage(page);
            allStores.push(...pageStores);

            if (page < numPages) {
                console.log(`\nWaiting ${this.delay}ms before next page...`);
                await this.sleep(this.delay);
            }
        }

        // Remove duplicates
        const seenUrls = new Set();
        const uniqueStores = [];

        for (const store of allStores) {
            if (!seenUrls.has(store.url)) {
                seenUrls.add(store.url);
                uniqueStores.push(store);
            }
        }

        console.log(`\n${'='.repeat(60)}`);
        console.log(`FINAL RESULTS`);
        console.log(`${'='.repeat(60)}`);
        console.log(`Total stores before dedup: ${allStores.length}`);
        console.log(`Total unique stores: ${uniqueStores.length}`);
        console.log(`Duplicates removed: ${allStores.length - uniqueStores.length}`);

        // Save test results
        fs.writeFileSync('test_results.json', JSON.stringify(uniqueStores, null, 2), 'utf8');
        console.log(`\nTest results saved to test_results.json`);

        // Show sample stores
        console.log(`\nSample stores:`);
        uniqueStores.slice(0, 3).forEach((store, i) => {
            console.log(`\n${i+1}. ${store.name}`);
            console.log(`   Address: ${store.address}`);
            console.log(`   Ward: ${store.ward}`);
            console.log(`   District: ${store.district}`);
            console.log(`   City: ${store.city}`);
            console.log(`   Phone: ${store.phone}`);
        });

        return uniqueStores;
    }
}

async function main() {
    const tester = new WinmartTestScraper();

    try {
        // Test first 2 pages
        const stores = await tester.testMultiplePages(2);
        console.log(`\nTest completed! Found ${stores.length} unique stores.`);
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

if (require.main === module) {
    main();
}
