{"provinceName": "T<PERSON><PERSON> <PERSON>", "provinceSlug": "thanh-pho-ha-noi", "data": [{"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardStores": [{"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102101", "wardName": "TT. <PERSON>", "stores": [{"storeCode": "6321", "storeName": "WM+ HNI 118 Hòa Sơn", "officeAddress": "Số <PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.919994, "longitude": 105.699234, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "6157", "storeName": "WM+ HNI 15 <PERSON><PERSON><PERSON>, Chương Mỹ", "officeAddress": "Số 15 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}, {"storeCode": "5063", "storeName": "WM+ HNI Số 16 Hòa Sơn", "officeAddress": "Số 16 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.92208, "longitude": 105.7022, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102101", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "TT. <PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102102", "wardName": "<PERSON><PERSON><PERSON>", "stores": [{"storeCode": "6173", "storeName": "WM+ HNI 13 Tổ 3 Tân Xuân", "officeAddress": "<PERSON><PERSON> 13, <PERSON><PERSON> 3 <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102102", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102103", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102104", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102105", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102106", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102107", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102108", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102109", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102110", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6664", "storeName": "WM+ HNI <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": true, "allowCod": true, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102110", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102111", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102112", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102113", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6403", "storeName": "WM+ H<PERSON>, Chương <PERSON>", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.865273, "longitude": 105.659935, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102113", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102114", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6683", "storeName": "WM+ HNI Ứng <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON> Ứng <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102114", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102115", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102116", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102117", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102118", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102119", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102120", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6131", "storeName": "WM+ HNI <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, huy<PERSON><PERSON>, T<PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102120", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102121", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6247", "storeName": "WM+ HNI 68-70 Quảng Bị", "officeAddress": "<PERSON><PERSON> 68-70, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102121", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102122", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102123", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102124", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102125", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102126", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5817", "storeName": "WM+ H<PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102126", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102127", "wardName": "<PERSON><PERSON> <PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102128", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "6184", "storeName": "WM+ HNI <PERSON>, Chương Mỹ", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 0, "longitude": 0, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102128", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102129", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102130", "wardName": "<PERSON><PERSON>", "stores": []}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102131", "wardName": "<PERSON><PERSON>", "stores": [{"storeCode": "5803", "storeName": "WM+ H<PERSON> 2, <PERSON><PERSON><PERSON><PERSON> M", "officeAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>", "latitude": 20.913239, "longitude": 105.646576, "contactMobile": "", "activeStatus": "<PERSON><PERSON><PERSON>", "isOpen": false, "deliveryStatus": "", "supportDelivering": false, "kindOfStoreName": "", "isDeliveryTime": false, "allowCod": false, "provinceCode": "HNI", "districtCode": "HNI102", "wardCode": "HNI102131", "provinceName": "T<PERSON><PERSON> <PERSON>", "districtName": "<PERSON><PERSON>", "wardName": "<PERSON><PERSON>", "chainId": "VMP", "deliveryInd": false, "takeAwayInd": false}]}, {"districtCode": "HNI102", "districtName": "<PERSON><PERSON>", "wardCode": "HNI102132", "wardName": "<PERSON><PERSON>", "stores": []}]}], "paging": {"totalCount": 28, "pageNumber": 2, "pageSize": 1, "totalPages": 28}}