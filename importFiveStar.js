// get store
// get address five star
// create branch

const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const UserProductTypeModel = require('./lib/models/userProductType');
const UserToppingModel = require('./lib/models/userTopping');
const UserToppingGroupModel = require('./lib/models/userToppingGroup');
const UserProductModel = require('./lib/models/userProduct');
const StoreLogModel = require('./lib/models/storeLog');
const StoreTypeModel = require('./lib/models/storeTypes');
const MemberModel = require('./lib/models/member');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const moment = require('moment')
const ms = require('ms')
const tool = require('./lib/utils/tool');

var workbook = new Excel.Workbook();
let index = 0;
let region = '';

workbook.xlsx.readFile(`${ABSPATH}/DSHeyU.xlsx`)
  .then(function () {
    var worksheet = workbook.getWorksheet(4);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber >= 2 && rowNumber < 75 && row.values && row.values.length) {
        // console.log('haha:rowNumber', rowNumber, row.values);
        if (row.values[2].trim() === 'CẦN THƠ') {
          region = 'vietnam:cantho';
        } else if (row.values[2].trim() === 'Đà Nẵng') {
          region = 'vietnam:danang';
        } else if (row.values[2].trim() === 'Hải Phòng') {
          region = 'vietnam:haiphong';
        } else if (row.values[2].trim() === 'Hải Dương') {
          if (row.values[9].trim() === 'Hải Dương') {
            region = 'vietnam:haiduong';
          } else if (row.values[9].trim() === 'Chí Linh') {
            region = 'vietnam:haiduong-chilinh';
          } else if (row.values[9].trim() === 'Nam Sách') {
            region = 'vietnam:haiduong-namsach';
          }
        } else if (row.values[2].trim() === 'Nghệ An') {
          region = 'vietnam:nghean';
        } else if (row.values[2].trim() === 'Thanh Hóa') {
          region = 'vietnam:thanhhoa';
        } else if (row.values[2].trim() === 'Hà Tĩnh') {
          region = 'vietnam:hatinh';
        } else if (row.values[2].trim() === 'HN') {
          region = 'hn';
        } else if (row.values[2].trim() === 'HCM') {
          region = 'hcm';
        } else if (row.values[2].trim() === 'Thái Nguyên') {
          region = 'vietnam:thainguyen';
        } else if (row.values[2].trim() === 'Khánh Hòa') {
          region = 'vietnam:khanhhoa';
        } else if (row.values[2].trim() === 'Bắc Ninh') {
          region = 'vietnam:bacninh';
        } else if (row.values[2].trim() === 'Hưng Yên') {
          if (row.values[9].trim() === 'TP. Hưng Yên') {
            region = 'vietnam:hungyen';
          } else if (row.values[9].trim() === 'Khoái Châu') {
            region = 'vietnam:hungyen-khoaichau';
          } else if (row.values[9].trim() === 'Phù Cừ') {
            region = 'vietnam:hungyen-phucu';
          }
        }

        // rowArr.push(row);
        const from = row.values[8].trim().split('-')[0].trim();
        const to = row.values[8].trim().split('-')[1].trim();

        // console.log('haha', moment(from, 'hh:mm').valueOf() - new Date().setHours(0, 0, 0, 0))
        // console.log('haha:row', row.values[3], region, rowNumber, from, to);

        let storeInf = {};
        let storeType;
        let storeId;
        let objCreate = {}
        let memberId;
        let productTypes = [];
        let toppings = [];
        let toppingGroups = [];
        const phone = row.values[4].trim().replace(/ /g, '');
        const name = row.values[3].trim();

        const lat = Number(row.values[6].trim().split(',')[0].trim());
        const lng = Number(row.values[6].trim().split(',')[1].trim());

        const getMemberInf = (next) => {
          MemberModel
            .findOne({ phone })
            .lean()
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              if (!result) {
                MemberModel
                  .create({
                    phone,
                    status: 1,
                    region,
                    name,
                    'facebook.name': name
                  }, (error, data) => {
                    if (error) {
                      return next(err);
                    }

                    if (!data) {
                      return next({
                        code: CONSTANTS.CODE.FAIL,
                        message: MESSAGES.SYSTEM.ERROR
                      })
                    }

                    memberId = data._id;

                    next();
                  })

                return;
              }

              memberId = result._id;

              next();
            })
        }

        const getStoreInf = (next) => {
          const date = new Date().setHours(0, 0, 0, 0);

          UserStoreModel
            .findOne({
              phone: '0777818411'
            })
            .lean()
            .exec((err, result) => {
              if (err || !result) {
                return next(err || new Error('Store not found'));
              }

              storeInf = result;
              objCreate = {
                name: row.values[7].trim(),
                nameAlias: tool.change_alias(row.values[7].trim()),
                phone: [phone],
                member: memberId,
                image: result.image,
                background: result.background,
                address: row.values[5].trim(),
                subAddress: '',
                description: result.description,
                location: {
                  type: 'Point',
                  coordinates: [lng, lat]
                },
                workingTime: {
                  "0": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "1": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "2": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "3": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "4": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "5": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "6": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ]
                },
                businessType: result.businessType,
                subType: result.subType,
                service: result.service,
                productSearch: [],
                region,
                hasProduct: 1,
                levelStatusProduct: 0,
                messageGoLive: 'HeyU sẽ duyệt toàn bộ thông tin sản phẩm, thông tin cửa hàng bạn cung cấp. Vì thế hãy cập nhật hết sản phẩm của Gian Hàng để đạt yêu cầu đưa lên các mục mua sắm của HeyU.'
              }

              // if (result.type) {
              //   objCreate.type = result.type;
              // }

              next();
            })
        }

        checkWorkingTime = (next) => {

        }

        const getLocation = (next) => {
          const options = {
            method: 'POST',
            uri: `${config.proxyRequestServer.google}/api/v1.0/google/name-to-location`,
            body: {
              text: row.values[4].trim()
            },
            json: true // Automatically stringifies the body to JSON
          };

          rp(options)
            .then((result) => {
              if (result.code === 200) {
                objCreate[location.type] = 'Point';
                objCreate[location.coordinates] = [result.data.lng, result.data.lat];
              } else {
                objCreate.location = storeInf.location;
              }

              next();
            })
            .catch((err) => {
              objCreate.location = storeInf.location;

              next();
            });
        }

        const determineRegion = (next) => {
          const location = {
            lat: req.body.location.coordinates[1],
            lng: req.body.location.coordinates[0]
          }

          locationHelper
            .getRegionByLatLng(location, 2, (err, regionName) => {
              if (err) {
                return next(err);
              }

              objCreate.region = regionName;

              next();

            })
        }

        const createStoreType = (next) => {
          if (storeInf.type) {
            return next();
          }

          StoreTypeModel
            .create({
              name: 'Five Star',
              nameAlias: tool.change_alias('Five Star'),
              status: 1
            }, (err, result) => {
              objCreate.type = result._id;

              next();
            })
        }

        const updateStore = (next) => {
          if (storeInf.type) {
            return next();
          }

          UserStoreModel
            .update({
              member: memberId,
              type: { $exists: false }
            }, {
              type: objCreate.type
            }, { multi: true })
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next();
            })
        }

        const createStore = (next) => {
          // console.log('haha:objCreate', objCreate)
          // return next();
          UserStoreModel
            .create(objCreate, (err, result) => {
              if (err) {
                return next(err)
              }

              storeId = result._id;

              StoreLogModel
                .create({
                  action: 'Tạo mới cửa hàng',
                  level: 0,
                  reason: '',
                  member: memberId,
                  store: result._id,
                  region
                })

              next();
            })
        }

        const createProductType = (next) => {
          UserProductTypeModel
            .find({
              store: storeInf._id
            })
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err);
              }

              results.map(store => {
                store.store = [storeId];
                store.member = memberId;
                store.level = 0;
                delete store._id;
                delete store.storeType;

                UserProductTypeModel
                  .create(store, (err, result) => {
                    if (result) {
                      productTypes.push(result._id);
                    }
                  })
              })

              next();
            })
        }

        const createTopping = (next) => {
          UserToppingModel
            .find({
              store: storeInf._id
            })
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err);
              }

              results.map(store => {
                store.store = [storeId];
                store.member = memberId;
                store.level = 0;
                delete store._id;
                delete store.storeType;

                UserToppingModel
                  .create(store, (err, result) => {
                    if (result) {
                      toppings.push(result._id);
                    }
                  })
              })

              next();
            })
        }

        const createToppingGroup = (next) => {
          UserToppingGroupModel
            .find({
              store: storeInf._id
            })
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err);
              }

              results.map(store => {
                store.store = [storeId];
                store.member = memberId;
                store.level = 0;
                store.topping = toppings;
                delete store._id;
                delete store.storeType;

                UserToppingGroupModel
                  .create(store, (err, result) => {
                    if (result) {
                      toppingGroups.push(result._id);
                    }
                  })
              })

              next();
            })
        }

        const createProduct = (next) => {
          UserProductModel
            .find({
              store: storeInf._id
            })
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err);
              }

              results.map(store => {
                store.store = [storeId];
                store.member = memberId;
                store.level = 0;
                store.productType = productTypes;
                store.topping = toppingGroups;
                delete store._id;
                delete store.storeType;

                UserProductModel
                  .create(store, (err, result) => {
                    UserStoreModel
                      .update({
                        _id: storeId,
                      }, {
                        $addToSet: {
                          productSearch: {
                            _id: store._id,
                            nameAlias: store.nameAlias,
                          }
                        }
                      }, { multi: true })
                      .exec((err, result) => { })
                  })
              })

              next();
            })
        }

        async.waterfall([
          getMemberInf,
          getStoreInf,
          // getLocation,
          // determineRegion,
          // createStoreType,
          // updateStore,
          createStore,
          createProductType,
          createTopping,
          createToppingGroup,
          createProduct
        ], (err, data) => {
          // err && _.isError(err) && (data = {
          //   code: CONSTANTS.CODE.SYSTEM_ERROR,
          //   message: MESSAGES.SYSTEM.ERROR
          // });
          index++;
          console.log('haha:result', rowNumber, index);
          console.log('haha:err', err, data);
        })
      }
    });
  });
