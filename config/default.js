module.exports = {
  "redis": {
    "connections": {
      "master": {
        "sentinels": process.env.REDIS_MODE === 'sentinel' ? JSON.parse(process.env.SENTINEL_INSTANCES || '[]') : undefined,
      	"sentinelPassword": process.env.SENTINEL_PASSWORD || "",
      	"host": process.env.REDIS_HOST || "localhost",
        "port": process.env.REDIS_PORT || 6379,
      	"name": process.env.REDIS_MASTER_NAME || "",
        "database": process.env.REDIS_DATABASE || 0,
        "password": process.env.REDIS_PASSWORD || "",
        "options": JSON.parse(process.env.REDIS_OPTIONS || '{}'),
      	"mode": process.env.REDIS_MODE || 'standalone'
      }
    }
  },
  "mongo": {
    "connections": {
      "master": {
        "instances" : JSON.parse(process.env.MONGODB_MASTER_INSTANCES || '[]'),
  			"host": process.env.MONGODB_MASTER_HOST || 'localhost',
  			"port": process.env.MONGODB_MASTER_PORT || 27017,
  			"database": process.env.MONGODB_MASTER_DATABASE || "shipping-management",
        "options": JSON.parse(process.env.MONGODB_MASTER_OPTIONS || '{}'),
  			"mode": process.env.MONGODB_MASTER_MODE || "standalone",
        "replicaSet": process.env.MONGODB_MASTER_REPLICASET_NAME || ""
      },
      "cms": {
        "instances" : JSON.parse(process.env.MONGODB_CMS_INSTANCES || '[]'),
  			"host": process.env.MONGODB_CMS_HOST || 'localhost',
  			"port": process.env.MONGODB_CMS_PORT || 27017,
  			"database": process.env.MONGODB_CMS_DATABASE || "shipping-management",
        "options": JSON.parse(process.env.MONGODB_CMS_OPTIONS || '{}'),
  			"mode": process.env.MONGODB_CMS_MODE || "standalone"
      }
    }
  },
  "environment": process.env.ENVIRONMENT || 'dev',
  "proxyRequestServer": JSON.parse(process.env.PROXY_REQUEST_SERVER || '{}'),
  "emailInfo": JSON.parse(process.env.EMAIL_INFO || '{}'),
  "listEmailAlert": JSON.parse(process.env.LIST_EMAIL_ALERT || '[]'),
  "logLevel": process.env.LOG_LEVEL || "error", // info , error
  "dirLog": "logs",
  "serviceName": "SS-PRODUCT",
  "port": process.env.PORT_PRODUCT || 2026,
  "listRegionOpened": ["vietnam:nghean", "vietnam:thanhhoa","vietnam:thainguyen","vietnam:cantho","vietnam:danang","vietnam:hungyen","vietnam:hatinh","vietnam:haiduong","vietnam:khanhhoa","hn","vietnam:haiphong","vietnam:hungyen-vanlam", "vietnam:hungyen-khoaichau", "vietnam:hungyen-vangiang","vietnam:khanhhoa","vietnam:hungyen-yenmy", "vietnam:hungyen-myhao","vietnam:hungyen-phucu", "vietnam:hungyen-tienlu","vietnam:haiduong-chilinh","vietnam:hungyen:anthi","vietnam:bacninh"],
  "listUserBarCode": ["61687985471ff1316ebcdee8", "5b3e10fb152aaf7c4efcf122", "616a793d91113b31602aebc3","5af6ab02e04c50d4fa383c6f"],
  "listAdmin": ["5af6ab02e04c50d4fa383c6f",  "5e378645292fcc438c22f92f", "5e1bb01e292fcc438c2ae445", "5eaf85350b1f6521d6e643a2", "5f8d059a0b1f6521d67992d7", "5f5991830b1f6521d696ed03", "5f771d7c0b1f6521d6a172b8", "5f1e40150b1f6521d64026e3","5d9697fc292fcc438c42e8bf"],
  "specialDistrict":["vietnam:hungyen:vanlam", "vietnam:hungyen:khoaichau", "vietnam:hungyen:vangiang","vietnam:hungyen:yenmy","vietnam:hungyen:myhao","vietnam:hungyen:phucu","vietnam:hungyen:tienlu","vietnam:hungyen:kimdong","vietnam:haiduong:chilinh","vietnam:hungyen:anthi"]
}
