const config = require('config');
const rp = require('request-promise');

module.exports = {
  getRegionByLatLng: (location, level, cb) => {
    const options = {
      method: 'POST',
      uri: `${config.proxyRequestServer.locationAddr}/api/v2.0/region/lat-lng`,
      body: {
        location,
        level
      },
      json: true
    }

    rp(options)
      .then((result) => {
        if(result.code !== 200) {
          return cb(result);
        }

        cb(null, result.data);
      })
      .catch((err) => {
        logger.logError(['getRegionByLatLng', err], __dirname);

        cb(err);
      })
  },
  getDistanceFromLatLonInKm: (lat1,lon1,lat2,lon2) => {
    var R = 6371.008; // Radius of the earth in km
    var dLat = deg2rad(lat2-lat1);  // deg2rad below
    var dLon = deg2rad(lon2-lon1);
    var a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    var d = R * c; // Distance in km
    return Math.round(d*10)/10
  }
}

function deg2rad(deg) {
  return deg * (Math.PI/180)
}
