const _ = require('lodash');
const moment = require('moment');
moment.locale('vi');

const self = {
  getDescriptionPromote: (promoteInf) => {
    let message = ''
    const isPointPromote = promoteInf.strategy.cashBackPointRate;
    if(!isPointPromote) {
      message = `${self.getStrategyDiscount(promoteInf.strategy)}`;
    } else {
      message = `Hoàn thành đơn nhân ${promoteInf.strategy.cashBackPointRate} lần điểm tích luỹ`
    }

    let messageCondition = '';
    let messagePart = '';
    if(messagePart) {
      messageCondition += `${messagePart}`
    }

    messagePart = self.getMessageFromUsed(promoteInf.condition.used);
    if(messagePart) {
      if(messageCondition) {
        messageCondition += `\n${messagePart}`
      } else {
        messageCondition += `${messagePart}`
      }
    }

    if(messageCondition) {
      message += `\nĐiều kiện áp dụng:\n${messageCondition}`
    }

    if(_.get(promoteInf, 'condition.limited', 0)) {
      message += `\n- Số lượng mã có giới hạn`;
    }

    return message;
  },
  getMessageCondition: (promoteInf) => {
    let message = ''
    // const isPointPromote = promoteInf.strategy.cashBackPointRate;
    // if (!isPointPromote) {
    //   message = `${self.getStrategyDiscount(promoteInf.strategy)}`;
    // } else {
    //   message = `Hoàn thành đơn nhân ${promoteInf.strategy.cashBackPointRate} lần điểm tích luỹ`
    // }

    let messageCondition = '';
    let messagePart = self.getMessageDistance(promoteInf.condition.order.condition);
    if (messagePart) {
      messageCondition += `${messagePart}`
    }

    messagePart = self.getMessageDeposit(promoteInf.condition.order.condition);
    if (messagePart) {
      if (messageCondition) {
        messageCondition += `\n${messagePart}`
      } else {
        messageCondition += `${messagePart}`
      }
    }

    messagePart = self.getMessageFromUsed(promoteInf.condition.used);
    if (messagePart) {
      if (messageCondition) {
        messageCondition += `\n${messagePart}`
      } else {
        messageCondition += `${messagePart}`
      }
    }

    if (messageCondition) {
      message += `\nĐiều kiện sử dụng mã:\n${messageCondition}`
    }

    if (_.get(promoteInf, 'condition.limited', 0)) {
      message += `\n- Số lượng mã có giới hạn`;
    }

    return message;
  },
  getStrategyDiscount: (strategy) => {
    let description = '';

    if(strategy.type === 'same') {
      description = `Khuyến mãi đồng giá ${strategy.value.toLocaleString().replace(/,/g, ".")}₫`
    } else if(strategy.type === 'percent') {
      description = `Khuyến mãi giảm ${strategy.value}% tiền mua hàng`
      if(strategy.maximum) {
        description += ` tối đa ${strategy.maximum.toLocaleString().replace(/,/g, ".")}₫`
      }
    } else if(strategy.type === 'direct') {
      description = `Khuyến mãi giảm ${strategy.value.toLocaleString().replace(/,/g, ".")}₫ tiền mua hàng`
    }

    return description;
  },
  getMessageFromOrder: (condition) => {
    let message = '';
    condition.forEach((conditionField) => {
      let messageCondition;

      if(conditionField.fieldName === 'region') {
        messageCondition = self.getMessageRegion([conditionField]);
      } else if(conditionField.fieldName === 'distance') {
        messageCondition = self.getMessageDistance([conditionField]);
      } else if(conditionField.fieldName === 'orderType') {
        messageCondition = self.getMessageOrderType([conditionField]);
      } else if(conditionField.fieldName === 'regionDistrictOrigin') {
        messageCondition = self.getMessageRegionDistrictOrigin([conditionField])
      } else if(conditionField.fieldName === 'regionDistrictDestination') {
        messageCondition = self.getMessageRegionDistrictDestination([conditionField])
      } if(conditionField.fieldName === 'regionDistrict') {
        messageCondition = self.getMessageRegionDistrict([conditionField])
      } else if(conditionField.fieldName === 'numDes') {
        messageCondition = self.getMessageNumDes([conditionField]);
      }

      if(!message) {
        message = `${messageCondition}`
      } else {
        message += `\n${messageCondition}`
      }
    })

    return message;
  },
  getMessageRegionDistrict: (condition) => {
    let message = '';

    for(let i=0; i<condition.length; i++) {
      const conditionField = condition[i];

      if(conditionField.fieldName === 'regionDistrict') {
        message = conditionField.message || '- Mã Khuyến Mãi không áp dụng cho đơn hàng có điểm nhận hoặc điểm giao từ quận/huyện của đơn hàng này.'
        break;
      }
    }

    return message;
  },
  getMessageRegionDistrictOrigin: (condition) => {
    let message = '';

    for(let i=0; i<condition.length; i++) {
      const conditionField = condition[i];

      if(conditionField.fieldName === 'regionDistrictOrigin') {
        message = conditionField.message || '- Mã Khuyến Mãi không áp dụng cho đơn hàng có điểm nhận từ quận/huyện của đơn hàng này.'
        break;
      }
    }

    return message;
  },
  getMessageRegionDistrictDestination: (condition) => {
    let message = '';

    for(let i=0; i<condition.length; i++) {
      const conditionField = condition[i];

      if(conditionField.fieldName === 'regionDistrictDestination') {
        message = conditionField.message || '- Mã Khuyến Mãi không áp dụng cho đơn hàng có điểm giao đến quận/huyện của đơn hàng này.'
        break;
      }
    }

    return message;
  },
  getMessageNumDes: (condition) => {
    let message = '';

    for(let i=0; i<condition.length; i++) {
      const conditionField = condition[i];

      if(conditionField.fieldName === 'numDes') {
        message = `- Mã KM chỉ áp dụng cho đơn hàng `;
        if(conditionField.strategy.type === "exact") {
          message += `có ${conditionField.strategy.value} điểm giao`
        }
        break;
      }
    }

    return message;
  },
  getMessageRegion: (condition) => {
    let message = '';

    for(let i=0; i<condition.length; i++) {
      const conditionField = condition[i];

      if(conditionField.fieldName === 'region') {
        message = `- Đơn hàng được áp dụng cho khu vực: `;
        if(conditionField.strategy.type === "exact") {
          message += ` ${conditionField.strategy.value === 'hn' ? `Hà Nội` : `Hồ Chí Minh`}`
        } else if(conditionField.strategy.type === "oneOf") {
          for(let i=0; i<conditionField.strategy.value.length; i++) {
            if(i === 0) {
              message += ` ${conditionField.strategy.value[i] === 'hn' ? `Hà Nội` : `Hồ Chí Minh`}`
            } else {
              message += `, ${conditionField.strategy.value[i] === 'hn' ? `Hà Nội` : `Hồ Chí Minh`}`
            }
          }
        }

        break;
      }
    }

    return message;
  },
  getMessageDistance: (condition) => {
    let message = '';

    for(let i=0; i<condition.length; i++) {
      const conditionField = condition[i];

      if(conditionField.fieldName === 'distance') {
        message = `- Khoảng cách đơn hàng trong phạm vi`;
        if(conditionField.strategy.type === "exact") {
          message += `: ${conditionField.strategy.value}km`
        } else if(conditionField.strategy.type === "range") {
          if(_.has(conditionField, 'strategy.value.from')) {
            message += ` từ ${conditionField.strategy.value.from} km`
          }

          if(_.has(conditionField, 'strategy.value.to')) {
            message += ` tới ${conditionField.strategy.value.to} km`
          }
        }

        break;
      }
    }

    return message;
  },
  getMessageDeposit: (condition) => {
    let message = '';

    for (let i = 0; i < condition.length; i++) {
      const conditionField = condition[i];

      if (conditionField.fieldName === 'deposit') {
        message = `- Giá trị đơn hàng`;
        if (conditionField.strategy.type === "exact") {
          message += `: ${conditionField.strategy.value.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1.")} ₫`
        } else if (conditionField.strategy.type === "range") {
          if (_.has(conditionField, 'strategy.value.from')) {
            message += ` từ ${conditionField.strategy.value.from.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1.")} ₫`
          }

          if (_.has(conditionField, 'strategy.value.to')) {
            message += ` tới ${conditionField.strategy.value.to.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1.")} ₫`
          }
        }

        break;
      }
    }

    return message;
  },
  getMessageOrderType: (condition) => {
    return 'Mã Khuyến Mãi không áp dụng cho loại đơn hàng này!';
  },
  getMessageFromUsed: (use) => {
    console.log(use);
    let messagePart = [];
    if(_.get(use, 'perDay', 0)) {
      messagePart.push(`- Sử dụng ${use.perDay} lần trong ngày`);
    }

    if(_.get(use, 'maximum', 0)) {
      messagePart.push(`- Mã sử dụng tối đa ${use.maximum} lần`);
    }

    return messagePart.join('\n')
  },
  getMessageFromTimeRange: (timeRange) => {
    let message;
    if(timeRange.from) {
      message = `Mã khuyến mãi được sử dụng từ ${moment(timeRange.from).format('H:mm DD/MM/YYYY')}`
    }

    if(timeRange.to) {
      if(!message) {
        message = `Mã khuyến mãi được sử dụng tới ${moment(timeRange.to).format('H:mm DD/MM/YYYY')}`;
      } else {
        message += ` tới ${moment(timeRange.to).format('H:mm DD/MM/YYYY')}`
      }
    }

    message = message || "Mã khuyến mãi hết hiệu lực";

    return message;
  },
  checkOneOf: (value, valueCheck) => {
    let valid = false;

    if(valueCheck.indexOf(value) !== -1) {
      valid = true;
    }

    return valid;
  },
  checkRange: (value, valueCheck) => {
    let valid = true;

    if(!_.has(valueCheck, 'from') && !_.has(valueCheck, 'to')) {
      valid = false;
    } else {
      if(valueCheck.from && value < valueCheck.from) {
        valid = false;
      }

      if(valueCheck.to && value > valueCheck.to) {
        valid = false;
      }
    }

    return valid;
  },
  checkExact: (value, valueCheck) => {
    return value === valueCheck;
  },
  checkTimeUntilNow: (value, valueCheck) => {
    return Date.now() - value <= valueCheck;
  },
  checkHasSameValue: (value, valueCheck) => {
    for (let i = 0; i < value.length; i++) {
      if(valueCheck.includes(value[i])) {
        return true
      }
    }
    return false
  },
  checkNoStartsWith: (value, valueCheck, region) => {
    if(region && region !== 'hn') {
      return true
    }
    for (var i = 0; i < valueCheck.length; i++) {
      if (value && valueCheck[i] && value.toLowerCase().startsWith(valueCheck[i].toLowerCase())) {
        return false
      }
    }
    return true
  },
  checkFieldValid: (obj, field, strategy) => {
    let valid = false;
    const typeCheck = strategy.type;
    const valueCheck = strategy.value;

    if(_.has(obj, field)) {
      let fnc;
      if(typeCheck === "oneOf") {
        fnc = self.checkOneOf;
      } else if(typeCheck === "range") {
        fnc = self.checkRange;
      } else if(typeCheck === "exact") {
        fnc = self.checkExact;
      } else if(typeCheck === "timeUntilNow") {
        fnc = self.checkTimeUntilNow;
      } else if(typeCheck === "hasSameValue") {
        fnc = self.checkHasSameValue;
      } else if(typeCheck === "noStartsWith") {
        fnc = self.checkNoStartsWith;
      }

      if(fnc) {
        if(typeCheck === "noStartsWith") {
          valid = fnc(_.get(obj, field), valueCheck, obj.region || '');
        } else {
          valid = fnc(_.get(obj, field), valueCheck);
        }
      }
    }

    return valid;
  },
  calculateDistance: (lat1, lng1, lat2, lng2) => {
    const R = 6371.008; // Radius of the earth in km
    const dLat = deg2rad(lat2-lat1);  // deg2rad below
    const dLon = deg2rad(lng2-lng1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const d = R * c; // Distance in km
    return parseFloat(d.toFixed(1));
  },
  replacePhone: (text) => {
    return removePhone(text);
  },
  removeSignAndLowerCase: ( alias ) =>
  {
      var str = alias;
      str= str.toLowerCase();
      str= str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g,"a");
      str= str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g,"e");
      str= str.replace(/ì|í|ị|ỉ|ĩ/g,"i");
      str= str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g,"o");
      str= str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g,"u");
      str= str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g,"y");
      str= str.replace(/đ/g,"d");
      str= str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|,|\.|\:|\;|\'| |\"|\&|\#|\[|\]|~|$|_/g,"");
      str= str.replace(/ /g, '');
      /* tìm và thay thế các kí tự đặc biệt trong chuỗi sang kí tự - */
      // str= str.replace(/-+-/g,"-"); //thay thế 2- thành 1-
      // str= str.replace(/^\-+|\-+$/g,"");
      //cắt bỏ ký tự - ở đầu và cuối chuỗi
      return str;
  }
}

function getOrderTypeName(id){

  if(id === '594b351ed177170872f2b257' || id === '5d3a9fa233a5080b994bac23') {
    return 'Truyền thống'
  }

  if(id === '597eb6a08dd7c30dc429ab2d' || id === '5d3a9fb933a5080b994bac2a') {
    return 'Hoả tốc'
  }

  if(id === '5dce26cdbc5b9d29bb3767ef') {
    return 'Di chuyển'
  }

  if(id === '5bb5bbfbbbfcc61f8bfc46ed') {
    return 'Mua hộ đồ ăn'
  }

  if(id === '5e4174bc503e8e0a1c38f8c5') {
    return 'Linh hoạt'
  }

  return 'Chưa xác định'
}

function removePhone (str) {
  const arrStartedPhoneNumber = ["09", "01", "08", "02", "84", "05"];

  let phoneFromStr = '';

  for (var i = 0; i < arrStartedPhoneNumber.length; i++) {
    var phone = getPhoneNumberSub(str, arrStartedPhoneNumber[i]);
    if(phone != "-1" && phone != "-2" && phone.length >= 10) {
      phoneFromStr =  phone;
      break;
    }
  }

  if(phoneFromStr) {
    str = str.replace(/\+84/g, "0");
    str = str.replace(/0 /g, "0");
    var i=-1;
    while((i=str.indexOf("0",i+1)) >= 0){
        var temp = str.substring(i);
        temp = temp.replace(/[^a-zA-Z0-9]/g, "");
        if(temp.startsWith(phoneFromStr)){
            // Found phone number
            str = str.substring(0,i+phoneFromStr.length-2) + "**" +str.substring(i+phoneFromStr.length);
            return str;
        }
    }
  }

  return str;
}

function getPhoneNumberSub(str, startedpn) {
    str = str.replace(/\+84/g, "0");
    str = str.replace(/[^a-zA-Z0-9]/g, "");
    let phone = "-1";
    //alert(str);
    if(str.length < 10)
        return "-2"
    if(str.indexOf(startedpn) >= 0)
    {
        let idx = str.indexOf(startedpn);
        let iPhoneNumber = 0;
        try
        {
            phone = str.substring(idx, idx+11);
            if(isNaN(phone)) // Khong phai la so
            {
                phone = str.substring(idx, idx+10);
                iPhoneNumber = parseInt(phone);
                if(isNaN(phone))
                {
                    return getPhoneNumberSub(str.substring(idx+2), startedpn);
                }
            }
        }
        catch (e) {
            return getPhoneNumberSub(str.substring(idx+2), startedpn);
        }
    }
    return phone;
}

function isInt(value) {
    return !isNaN(value) && (function(x) { return (x | 0) === x; })(parseFloat(value))
}


function deg2rad(deg) {
  return deg * (Math.PI/180)
}

module.exports = self;
