const OrderInteractionManager = require('../job/orderInteractionManager');
const OrderSystemModel = require('../models/orderSystem')

module.exports = {
  calculateMoney: (distance, moneyStrategy, priceIncrease, numDes) => {
    const configMoney = moneyStrategy.step || [];
    const min = moneyStrategy.minMoney;
    const moreDesPrice = moneyStrategy.moreDesPrice || 0;

    priceIncrease = priceIncrease || 1;
    numDes = numDes || 1;

    let money = 0;
    for(let i=0; i<configMoney.length; i++) {
      if (configMoney[i].distance === 0) {
        money += configMoney[i].money*distance;
        break;
      } else if (distance >= configMoney[i].distance) {
        distance -= configMoney[i].distance
        money += configMoney[i].money*configMoney[i].distance
      } else {
        money += configMoney[i].money*distance;
        break;
      }
    }

    if(money < min) {
      money = min;
    }

    if(numDes > 1) {
      money += moreDesPrice*(numDes - 1);
    }

    money = money*priceIncrease;

    money = Math.round(money/1000)*1000;

    return money;
  },
  calculateServiceCharge: (orderInf, region) => {

    region = region || orderInf.region;
    let serviceCharge = 0;
    const money = orderInf.money || 0
    if(OrderInteractionManager.getConfig(region)) {
      const percent = OrderInteractionManager.getConfig(region).serviceCharge;
      serviceCharge = money*percent/100;
    }
    return serviceCharge

  },
  calculateMinMoney: (orderInf, region) => {

    region = region || orderInf.region;
    let minMoney = 0;
    if(OrderInteractionManager.getConfig(region)) {
      minMoney = OrderInteractionManager.getConfig(region).minMoney;
    }
    return minMoney

  },
  getTotalDeposit: (userId, from, cb) => {
    OrderSystemModel
      .find({
        shop: userId,
        updatedAt: {
          $gte: from
        },
        status: {
          $in: [-1, 0, 1, 2]
        }
      }, "deposit")
      .lean()
      .exec((err, results) => {
        if (err) {
          return cb(err);
        }

        let totalDeposit = 0;
        results.forEach((result) => {
          totalDeposit += result.deposit;
        })

        cb(null, totalDeposit)
      })
  },
}
