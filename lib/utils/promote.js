const _ = require('lodash');
const async = require('async');
const utils = require('./utils');
const OrderStoreModel = require('../models/orderStore')
const OrderTypeModel = require('../models/orderType')
// const memberUtil = require('./member')
const OrderHelper = require('./order')
const MESSAGES = require('../message')

module.exports = {
  calculateDiscount: (orderInf, promoteInf, cb) => {
    let money = orderInf.money;
    const distance = orderInf.distance;
    const orderType = orderInf.orderType;
    const destinationPlaces = orderInf.destinationPlaces;

    const calculateDiscountMoney = (done) => {
      let discount = 0;

      if (promoteInf.strategy.type === "same") {
        // if(money > realMoney) {
        //   discount = realMoney - promoteInf.strategy.value;
        // } else {
        discount = money - promoteInf.strategy.value;
        // }
        if (discount > promoteInf.strategy.maximum) {
          discount = promoteInf.strategy.maximum;
        }
      } else if (promoteInf.strategy.type === "percent") {
        // if(money > realMoney) {
        //   discount = realMoney*promoteInf.strategy.value/100;
        // } else {
        discount = money * promoteInf.strategy.value / 100;
        // }

        discount = Math.round(discount / 1000) * 1000;

        if (promoteInf.strategy.maximum && discount > promoteInf.strategy.maximum) {
          discount = promoteInf.strategy.maximum;
        }

        // if (discount > 50000) {
        //   discount = 50000
        // }

      } else if (promoteInf.strategy.type === "direct") {
        discount = promoteInf.strategy.value;
        if (money - promoteInf.strategy.value < 0) {
          discount = money;
        }
      }

      if (discount < 0) {
        discount = 0;
      }

      done(null, discount)
    }

    async.waterfall([
      calculateDiscountMoney
    ], cb)
  },
  checkTotalOrderUse: (userId, promoteId, condition, cb) => {
    let valid = true;
    let message = '';
    let numberOfUses = 0;

    const checkUseByDevices = (done) => {
      if (!_.get(condition, 'used.checkUseByDevices', 0)) {
        return done();
      }

      done();
    }

    const checkTotalOrderUsePerDay = (done) => {
      if (!_.get(condition, 'used.perDay', 0)) {
        return done();
      }

      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0)
      const startTime = currentDate.getTime();
      currentDate.setHours(24, 0, 0, 0)
      const endTime = currentDate.getTime();

      OrderStoreModel
        .count({
          customer: userId,
          promoteStore: promoteId,
          status: {
            $in: [0, 1, 2, 3, 4]
          },
          updatedAt: {
            $gte: startTime,
            $lt: endTime
          }
        })
        .exec((err, count) => {
          if (err) {
            return done(err);
          }

          if (count >= condition.used.perDay) {
            valid = false;
            message = MESSAGES.PROMOTE.MAXIMUM_USE_PER_DAY
          } else {
            numberOfUses = condition.used.perDay - count;
          }

          done();
        })
    }

    const checkTotalOrderUse = (done) => {
      if (!_.get(condition, 'used.maximum', 0) || !valid) {
        return done();
      }

      OrderStoreModel
        .count({
          customer: userId,
          promoteStore: promoteId,
          status: {
            $in: [0, 1, 2, 3, 4]
          }
        })
        .exec((err, count) => {
          if (err) {
            return done(err);
          }

          if (count >= condition.used.maximum) {
            valid = false;
            message = MESSAGES.PROMOTE.MAXIMUM_USE
          } else {
            numberOfUses = condition.used.maximum - count;
          }

          done();
        })
    }

    const checkLimited = (done) => {
      if (!_.get(condition, 'limited', 0) || !valid) {
        return done();
      }

      OrderStoreModel
        .count({
          promoteStore: promoteId,
          status: {
            $in: [0, 1, 2, 3, 4]
          }
        })
        .exec((err, count) => {
          if (err) {
            return done(err);
          }

          if (count >= condition.limited) {
            valid = false;
            message = MESSAGES.PROMOTE.MAXIMUM_USE
          }

          done();
        })
    }

    async.waterfall([
      checkUseByDevices,
      checkTotalOrderUsePerDay,
      checkTotalOrderUse,
      checkLimited
    ], (err) => {
      if (err) {
        return cb(err);
      }

      cb(null, {
        valid: valid,
        message: message,
        numberOfUses
      })
    })
  },
  checkOrderCondition: (orderInf, conditionOrder, cb) => {
    let isValid = true;
    let message;
    for (let i = 0; i < conditionOrder.condition.length; i++) {
      if (!utils.checkFieldValid(orderInf, conditionOrder.condition[i].fieldName, conditionOrder.condition[i].strategy)) {
        isValid = false;
        if (!message) {
          message = utils.getMessageFromOrder([conditionOrder.condition[i]])
        } else {
          message += `\n${utils.getMessageFromOrder([conditionOrder.condition[i]])}`
        }
      }
    }

    cb(null, {
      valid: isValid,
      message: message
    });
  },
  checkRegion: (region, conditionOrder, cb) => {
    let valid = true;

    for (let i = 0; i < conditionOrder.condition.length; i++) {
      if (conditionOrder.condition[i].fieldName === "region" && !utils.checkFieldValid({ region }, conditionOrder.condition[i].fieldName, conditionOrder.condition[i].strategy)) {
        valid = false;
        break;
      }
    }

    return valid;
  },
  checkMemberCondition: (userInf, conditionMember, cb) => {
    const userId = userInf._id.toHexString();
    let isWhiteList = false;
    const checkWhiteList = (done) => {
      if (conditionMember.whiteList && conditionMember.whiteList.indexOf(userId) !== -1) {
        isWhiteList = true;
      }

      done();
    }

    const checkCondition = (done) => {
      if (isWhiteList) {
        return done(null, {
          valid: true
        });
      }

      let message = '';
      let isValid = true;
      for (let i = 0; i < conditionMember.condition.length; i++) {
        if (!utils.checkFieldValid(userInf, conditionMember.condition[i].fieldName, conditionMember.condition[i].strategy)) {
          isValid = false;
          message += `\n${JSON.stringify(conditionMember.condition[i])}`;
        }
      }

      done(null, {
        valid: isValid,
        message
      });
    }

    async.waterfall([
      checkWhiteList,
      checkCondition
    ], cb)
  }
}
