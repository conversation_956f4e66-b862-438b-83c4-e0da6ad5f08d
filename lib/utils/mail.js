const nodemailer = require('nodemailer');
const _ = require('lodash');
const config = require('config');

transporter = nodemailer.createTransport(config.emailInfo)

let listRecents = [];

setInterval(() => {
  listRecents = [];
}, 2*60*1000);

module.exports = {
  sendMail: (body, listEmailAlert) => {
    if(listRecents.indexOf(body) !== -1) {
      return;
    }

    listRecents.push(body);

    if(!listEmailAlert) {
      listEmailAlert = _.get(config, 'listEmailAlert', []).join(',');
    }

    const mailOptions = {
      from:'"Săn Ship System" <<EMAIL>>', // sender address
      to: listEmailAlert, // list of receivers
      subject: `HEYU - ${config.serviceName} - ${config.environment}`, // Subject line
      text: `Our system has encountered an error:\n\n${body}\n\nYou need to do something right now!\nBest regards,\nSăn Ship Team`, // plain text body
    }

    transporter.sendMail(mailOptions, (error, info) => {
      logger.logInfo("Send mail:", error, info);
    });
  }
}
