const CONSTANTS = require('../const')

module.exports = {
  change_alias:function( alias )
  {
      var str = alias;
      str= str.toLowerCase();
      str= str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g,"a");
      str= str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g,"e");
      str= str.replace(/ì|í|ị|ỉ|ĩ/g,"i");
      str= str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g,"o");
      str= str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g,"u");
      str= str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g,"y");
      str= str.replace(/đ/g,"d");
      str= str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'| |\"|\&|\#|\[|\]|~|$|_|-/g,""); /* tìm và thay thế các kí tự đặc biệt trong chuỗi sang kí tự "" */
      // str= str.replace(/-+-/g,"-"); //thay thế 2- thành 1-
      // str= str.replace(/^\-+|\-+$/g,""); //cắt bỏ ký tự - ở đầu và cuối chuỗi
      return str;
  },
  short_address: (addr, region) => {
    if (region === 'vietnam:nghean') {
      return addr.split(',').slice(1).join(',').trim();
    }

    let addrEn = addr;
    addrEn = addrEn.toLowerCase();
    addrEn = addrEn.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    addrEn = addrEn.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    addrEn = addrEn.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    addrEn = addrEn.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    addrEn = addrEn.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    addrEn = addrEn.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    addrEn = addrEn.replace(/đ/g, "d");

    let addrEnArr = addrEn.split(' ');
    let i = 0;

    let regNum = /\d/i;
    let regSpecial = /\W/i;

    do {
      i++;
      addrEnArr.shift();
    } while (regNum.test(addrEnArr[0]) || regSpecial.test(addrEnArr[0]));

    return addr.split(' ').slice(i).join(' ').trim();
  },

  store_level_trans: (level) => {
    const {
      REJECT_UPDATE,
      REJECT,
      WAIT_FOR_APPROVED,
      APPROVED,
      WAIT_FOR_UPDATE,
      DELETED
    } = CONSTANTS.STORE_LEVEL

    switch (level) {
      case DELETED:
        return 'Không còn nhu cầu';
      break;
      case REJECT_UPDATE:
        return 'Từ chối cập nhật';
      break;
      case REJECT:
        return 'Từ chối duyệt';
      break;
      case WAIT_FOR_APPROVED:
        return 'Chờ duyệt';
      break;
      case APPROVED:
        return 'Đã duyệt';
      break;
      case WAIT_FOR_UPDATE:
        return 'Chờ cập nhật';
      break;
      default:
        return level;
    }
  },
  formatMoney: (n, format = 'đ') => {
    if (typeof n == 'number') {
      n = n.toString()
    }
    return n.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",")+format;
  },
  getTextTotalOrders: (totalOrders = 0) => {
    let textTotalOrders = '';
    if (totalOrders >= 9999) {
      textTotalOrders = '(9999+)';
    } else if (totalOrders >= 1000) {
      textTotalOrders = '(1000+)';
    } else if (totalOrders >= 100) {
      textTotalOrders = '(100+)';
    } else if (totalOrders >= 50) {
      textTotalOrders = '(50+)';
    // } else if (totalOrders !== 0) {
    //   textTotalOrders = `(${totalOrders})`;
    }

    return textTotalOrders;
  },

  getTextTotalSold: (totalSold = 0, region) => {
    let textTotalSold = ['vietnam:hungyen', 'vietnam:hungyen-phucu', 'vietnam:hungyen-tienlu'].includes(region) ? 'Đã bán' : '';
    if (totalSold >= 9999) {
      textTotalSold = 'Đã bán: 9999+';
    } else if (totalSold >= 1000) {
      textTotalSold = 'Đã bán: 1000+';
    } else if (totalSold >= 100) {
      textTotalSold = 'Đã bán: 100+';
    } else if (totalSold >= 50) {
      textTotalSold = 'Đã bán: 50+';
    // } else if (totalSold !== 0) {
    //   textTotalSold = `Đã bán: ${totalSold}`;
    }

    return textTotalSold;
  }
}
