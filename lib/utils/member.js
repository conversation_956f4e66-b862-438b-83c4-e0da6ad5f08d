const _ = require('lodash');
const async = require('async');
const MemberModel = require('../models/member')
const TransactionLogModel = require('../models/transactionLog')
const CONSTANTS = require('../../lib/const')
const MESSAGES = require('../../lib/message')
const config = require('config');
const rp = require('request-promise');

module.exports = {
  isShipper: (userInf) => {
    let value = false;

    if(userInf.coints > 0 || userInf.realMoney > 0 || userInf.ship.isAuthen
    || userInf.ship.totalRides > 0 || userInf.ship.totalRejects > 0 || userInf.blockOrderUtil > 0) {
      value = true;
    }

    return value;
  },
  phoneIsNotStartWith: (listPhoneCheck, listStartPhone) => {
    let value = false;

    for(let i=0; i<listPhoneCheck.length && !value; i++) {
      for(let j=0; j<listStartPhone.length; j++) {
        if(listPhoneCheck[i].startsWith(listStartPhone[j])) {
          value = true;
          break;
        }
      }
    }

    return value;
  },
  handleBackDeposit: (obj, cb) => {

    const orderId = obj.orderId;
    const region = obj.region;
    const orderInf = obj.orderInf
    const userId = obj.userId;
    const inapp = obj.inapp;
    const inappDeposit = obj.inappDeposit;

    const totalMoneyInapp = inapp + inappDeposit


    let memberInfo;

    if(!cb) {
      cb = (err) => {
        if(err) {
          logger.logError(['handleBackDeposit', err], __dirname)
        }
      }
    }

    if(!totalMoneyInapp) {
      return cb();
    }

    const increaseDeposit = (done) => {
      MemberModel
        .findOneAndUpdate({
          _id: userId
        }, {
          $inc: {
            deposit: totalMoneyInapp
          }
        })
        .exec((err, result) => {
          if(err || !result) {
            return done(err || {
              code: CONSTANTS.CODE.FAIL
            });
          }

          memberInfo = result;

          done();
        })
    }

    const writeLogTransaction = (done) => {
      if(!inapp) {
        return done();
      }
      TransactionLogModel
        .create({
          member: userId,
          region: region,
          message: "Hoàn lại phí ship thanh toán đơn hàng",
          data: {
            amount: inapp,
            idOrder: orderId,
            type: 11,
            back: 1,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney,
            initialRealMoney: memberInfo.realMoney,
            initialDeposit: memberInfo.deposit,
            finalDeposit: memberInfo.deposit + inapp,
          }
        }, (err) => {
          done();
        })
    }

    const writeLogTransactionDeposit = (done) => {
      if(!inappDeposit) {
        return done();
      }
      TransactionLogModel
        .create({
          member: userId,
          region: region,
          message: "Hoàn lại phí COD thanh toán đơn hàng",
          data: {
            amount: inappDeposit,
            idOrder: orderId,
            type: 33,
            back: 1,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney,
            initialRealMoney: memberInfo.realMoney,
            initialDeposit: memberInfo.deposit + inapp,
            finalDeposit: memberInfo.deposit + totalMoneyInapp,
          }
        }, (err) => {
          done();
        })
    }

    const handlePayment = (done) => {
      if(orderInf.paymentMethod !== 'momo' && orderInf.paymentMethod !== 'zalo' && orderInf.paymentMethod !== 'shopee' && orderInf.paymentMethod !== 'tokenization') {
        return done();
      }


      if(orderInf.paymentMethod === 'momo') {
        if(orderInf.momoRef) {
          const options = {
            method: 'POST',
            uri: `${config.proxyRequestServer.payment}/api/v1.0/momo/payment/refund`,
            body: {
                id: orderInf.momoRef
            },
            json: true
          };
          rp(options)
            .then((result) => {
              done()
            })
            .catch((err) => {
              done()
            });
        } else {
          return done();
        }
      }
      if(orderInf.paymentMethod === 'zalo') {
        if(orderInf.zaloRef) {
          const options = {
            method: 'POST',
            uri: `${config.proxyRequestServer.payment}/api/v1.0/zalo/payment/refund`,
            body: {
                id: orderInf.zaloRef
            },
            json: true
          };
          rp(options)
            .then((result) => {
              done()
            })
            .catch((err) => {
              done()
            });
        } else {
          return done();
        }
      }

      if (orderInf.paymentMethod === 'shopee') {
        if (orderInf.shopeeRef) {
          const options = {
            method: 'POST',
            uri: `${config.proxyRequestServer.payment}/api/v1.0/shopee/payment/refund`,
            body: {
              id: orderInf.shopeeRef
            },
            json: true
          };
          rp(options)
            .then((result) => {
              done()
            })
            .catch((err) => {
              done()
            });
        } else {
          return done();
        }
      }

      if (orderInf.paymentMethod === 'tokenization') {
        if (orderInf.tokenizationZaloRef) {
          const options = {
            method: 'POST',
            uri: `${config.proxyRequestServer.payment}/api/v1.0/zalo/tokenization/refund`,
            body: {
              id: orderInf.tokenizationZaloRef
            },
            json: true
          };
          rp(options)
            .then((result) => {
              done()
            })
            .catch((err) => {
              done()
            });
        } else {
          return done();
        }
      }
    }

    async.waterfall([
      increaseDeposit,
      writeLogTransaction,
      writeLogTransactionDeposit,
      handlePayment
    ], cb)
  },
  handleIncreaseRealMoneyViaInapp: (obj, cb) => {
    const userId = obj.userId;
    const orderId = obj.orderId;
    const amount = obj.amount;
    const region = obj.region;

    if(!cb) {
      cb = (err) => {
        if(err) {
          logger.logError(['handleIncreaseRealMoneyViaInapp', err], __dirname)
        }
      }
    }

    if(!amount) {
      return cb();
    }

    let memberInfo;
    const increaseDeposit = (done) => {
      MemberModel
        .findOneAndUpdate({
          _id: userId
        }, {
          $inc: {
            realMoney: amount,
            realMoneyShop: amount
          }
        })
        .exec((err, result) => {
          if(err || !result) {
            return done(err || {
              code: CONSTANTS.CODE.FAIL
            });
          }

          memberInfo = result;

          done();
        })
    }

    const writeLogTransaction = (done) => {
      TransactionLogModel
        .create({
          member: userId,
          region: region,
          message: "Thanh toán qua ứng dụng",
          data: {
            amount: amount,
            idOrder: orderId,
            type: 12,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop + amount,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney + amount,
            initialRealMoney: memberInfo.realMoney
          }
        }, (err) => {
          done();
        })
    }

    async.waterfall([
      increaseDeposit,
      writeLogTransaction
    ], cb)
  },
  handleIncreaseRealMoneyViaPoint: (obj, cb) => {
    const userId = obj.userId;
    const orderId = obj.orderId;
    const amount = obj.amount;
    const region = obj.region;

    if(!cb) {
      cb = (err) => {
        if(err) {
          logger.logError(['handleIncreaseRealMoneyViaPoint', err], __dirname)
        }
      }
    }

    if(!amount) {
      return cb('Not found amount');
    }

    let memberInfo;
    const increaseDeposit = (done) => {
      MemberModel
        .findOneAndUpdate({
          _id: userId
        }, {
          $inc: {
            realMoney: amount
          }
        })
        .exec((err, result) => {
          if(err || !result) {
            return done(err || {
              code: CONSTANTS.CODE.FAIL
            });
          }

          memberInfo = result;

          done();
        })
    }

    const writeLogTransaction = (done) => {
      TransactionLogModel
        .create({
          member: userId,
          region: region,
          message: "Thanh toán qua điểm tích luỹ",
          data: {
            amount: amount,
            idOrder: orderId,
            type: 22,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney + amount,
            initialRealMoney: memberInfo.realMoney
          }
        }, (err, result) => {
          if (err) {
            logger.logError([err]);
          }

          done(err);
        })
    }

    async.waterfall([
      increaseDeposit,
      writeLogTransaction
    ], cb)
  }
}
