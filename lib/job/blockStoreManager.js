const _ = require('lodash');
const ms = require('ms');
const async = require('async');
const PushNotifyManager = require('./pushNotify');
const StoreModel = require('../models/store')
const UserStoreModel = require('../models/userStore')
const MemberModel = require('../models/member')
const OrderStoreLogModel = require('../models/orderStoreLog')
const StoreLog = require('../models/storeLog');
const redisConnections = require('../connections/redis')
const CONSTANTS = require('../const');
const OrderInteractionManager = require('../job/orderInteractionManager');
const rp = require('request-promise')
const config = require('config')
var schedule = require('node-schedule');
const MIN_MONEY = -50000;

class DailyJob {

  constructor() {
    this.init();
    this.handleDailyTask = this.handleDailyTask.bind(this);
  }

  init() {
    schedule.scheduleJob('11 1 0 * * *', () => {
      const date = new Date();
      console.log(moment(date).format("DD/MM hh:mm:ss"))
      this.handleDailyTask();
    });
  }

  handleDailyTask() {

    let memberBlock = []

    const findMember = (next) => {
      MemberModel
        .find({
          money: {
            $lte: MIN_MONEY
          },
          $or:[{
            isBlockStore: {
              $exists:false
            }
          },{
            isBlockStore: 0
          }]
        })
        .lean()
        .exec((err, results) => {
          if(err) {
            return next(err);
          }

          results.forEach((item, i) => {
            memberBlock.push(item._id.toString());
          });

          next();

        })
    }

    const handleBlockStore = (next) => {

      if(!memberBlock.length) {
        return next();
      }

      StoreModel
        .update({
          member: {$in: memberBlock}
        },{
          golive: 0,
          golink: 0,
          messageGoLive: 'Cửa hàng của bạn đã tạm thời bị vô hiệu hoá trên HeyU. Vui lòng nạp thêm tiền để có thể mở lại cửa hàng. Liên hệ hotline 1900.633.689 để được hỗ trợ.'
        },{
          multi:true
        },(err, result) => {
          if(err) {
            return next(err)
          }

          next();
        })

    }

    const handleBlockUserStore = (next) => {

      if(!memberBlock.length) {
        return next();
      }

      UserStoreModel
        .update({
          member: {$in: memberBlock}
        },{
          golive: 0,
          golink: 0,
          messageGoLive: 'Cửa hàng của bạn đã tạm thời bị vô hiệu hoá trên HeyU. Vui lòng nạp thêm tiền để có thể mở lại cửa hàng. Liên hệ hotline 1900.633.689 để được hỗ trợ.'
        },{
          multi:true
        },(err, result) => {
          if(err) {
            return next(err)
          }

          next();
        })

    }

    const updateMember = (next) => {

      next();

      MemberModel
        .update({
          _id: {$in: memberBlock}
        },{
          isBlockStore: 1
        },{
          multi:true
        },(err, result) => {})

      memberBlock.map(member => {
        StoreModel
          .findOne({ member })
          .lean()
          .exec((err, result) => {
            const log = {
              author: '594e082d885ac733cdb9fa26',
              phone: result.phone,
              action: 'Tự động vô hiệu hoá gian hàng',
              reason: 'Vô hiệu hoá gian hàng do nợ chiết khấu',
              userId: '594e082d885ac733cdb9fa26',
              member,
              region: result.region,
              store: result._id
            };

            StoreLog.create(log);
          })
      })
    }

    const pushNotify = (next) => {
        const options = {
          method: 'POST',
          uri: `${config.proxyRequestServer.pushNotifyTickbox}/api/v1.0/push-notification/all`,
          body: {
              query: {
                _id: {$in:memberBlock}
              },
              title: "Thông báo",
              message: "Cửa hàng của bạn đã tạm thời bị vô hiệu hoá trên HeyU. Vui lòng nạp thêm tiền để có thể mở lại cửa hàng. Liên hệ hotline 1900.633.689 để được hỗ trợ."
          },
          json: true // Automatically stringifies the body to JSON
        };

        rp(options)
          .then((result) => {
            next()
          })
          .catch((err) => {
            next()
          });

    }

    async.waterfall([
      findMember,
      handleBlockStore,
      handleBlockUserStore,
      updateMember,
      pushNotify
    ], (err, data) => {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });
      console.log(`Sucessfully block ${memberBlock.length} stores`);
    })
  }

}

module.exports = new DailyJob;
