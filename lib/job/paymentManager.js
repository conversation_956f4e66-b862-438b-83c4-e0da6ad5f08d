const _ = require('lodash');
const ms = require('ms');
const async = require('async');
const PushNotifyManager = require('./pushNotify');
const OrderStore = require('../models/orderStore')
const ConfigModel = require('../models/config');
const OrderStoreLogModel = require('../models/orderStoreLog');

const CONSTANTS = require('../const');
const rp = require('request-promise')
const config = require('config')

class PaymentManager {
  constructor() {

    this.listJob = {};

    this.init();
  }

  add(orderId) {
    orderId = orderId.toString();
    this.remove(orderId);
    this.listJob[orderId] = setTimeout(() => {
      this.rejectOrder(orderId)
    },ms('5m'))

  }

  isWaiting(orderId) {
    orderId = orderId.toString();

    if(this.listJob[orderId]) {
      return true
    }

    return false
  }

  rejectOrder(orderId) {
    orderId = orderId.toString()

    let orderInf;

    const getOrderInf = (next) => {
      OrderStore
        .findById(orderId)
        .populate('store', 'name member customer')
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err)
          }

          orderInf = result

          if(result.status !== CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT) {
            return next('Status is not valid')
          }
          next();
        })
    }

    const rejectOrder = (next) => {
      OrderStore
        .update({
          _id: orderId,
          status: CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT
        },{
          status: CONSTANTS.ORDER_STATUS.REJECT
        },(err, result) => {
          if(err) {
            return next(err)
          }
          OrderStoreLogModel.create({
            member: '594e082d885ac733cdb9fa26',
            order: orderInf._id,
            store: orderInf.store._id,
            customer: orderInf.customer,
            merchant: orderInf.store.member,
            type: CONSTANTS.ORDER_LOG.REJECT
          })
          PushNotifyManager
            .sendToMember(orderInf.customer, "Thông báo", `Hệ thống đã huỷ đơn mua hàng từ ${orderInf.store.name} do quá thời gian chờ thanh toán. Bạn vui lòng đặt đơn mới nếu còn nhu cầu sử dụng. Xin cảm ơn!`, {link: 'SBHDetailOrderForShopScreen', extras:{id: orderInf._id}}, 'order_update_customer')
          next();
        })
    }

    async.waterfall([
      getOrderInf,
      rejectOrder
    ], (err, result) => {

      this.remove(orderId);
    });
  }

  remove(orderId) {
    orderId = orderId.toString()
    if(this.listJob[orderId]) {
      clearTimeout(this.listJob[orderId]);
      delete this.listJob[orderId];
    }
  }

  init() {
    let currentDate = new Date();
    currentDate.setHours(0,0,0,0);
    OrderStore
      .find({status: CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT, updatedAt: {$gt: currentDate.getTime()}})
      .exec((err, jobs) => {
        if(err) {
          return setTimeout(() => {
            this.init();
          }, 5000)
        }

        logger.logInfo('Init jobs payment', err, jobs.length);
        jobs.forEach((job) => {
          this.add(job._id);
        });
      });
  }

}

module.exports = new PaymentManager;
