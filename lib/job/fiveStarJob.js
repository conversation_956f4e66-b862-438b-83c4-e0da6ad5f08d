const _ = require('lodash');
const async = require('async');
const config = require('config');
const ms = require('ms');
const moment = require('moment');
const CONSTANTS = require('../const');
const schedule = require('node-schedule');
const nodemailer = require("nodemailer");
const exportStoresData = require('../routes/admin/store/exportStoresData');

class FiveStarJob {
  constructor() {
    this.init();
  }

  init() {
    schedule.scheduleJob('0 30 8 1 * *', () => {
      this.count = 0;
      this.handleJob();
    });
  }

  handleJob = () => {
    if (this.count > 5) {
      return;
    }

    const date = new Date();
    const endTime = moment(date).startOf('month').valueOf();
    date.setMonth(date.getMonth() - 1);
    const startTime = moment(date).startOf('month').valueOf();
    const phones = ['0368375888', '0358448449', '0983895105', '0948887315', '0962375299', '0916961418', '0985136914', '0962241397', '0979983100', '0984778988', '0979960596', '0983110082', '0981948678', '0938927912', '0905825725', '0988195369', '0963968344', '0774400343', '0989310299', '0903432335', '0986212868', '0357299576', '0906678658', '0912212688', '0392124800', '0973420890', '0964492915', '0832238668', '0348145308', '0765170694','0971982984','0859831806','0985889847','0907650380','0984101288','0772359090','0376428815','0977266379','0964941114','0979770128','0333150061','0981800444','0969866133','0355917638','0917888611','0984318389','0986319990','0938089590','0965681834','0849012345','0707181309','0396886816','0585858283','0968379389','0398868468','0972986226','0704188790','0372998170','0949226006','0944607456','0968622814','0945262648','0904070200','0966664346','0986085225','0961486058','0977336729','0373821651','0964764002','0904921909','0852881299','0966914256','0777703290'];
    const email = '<EMAIL>';

    exportStoresData({
      body: {
        startTime,
        endTime,
        phones,
        email
      }
    }, {
      json: (data) => {
        this.sendMail(data);

        if (data.code !== CONSTANTS.CODE.SUCCESS) {
          this.count++;
          setTimeout(() => {
            this.handleJob();
          }, ms('5m'));
        }
      }
    })
  }

  sendMail = (data) => {
    let transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: config.mail.sender,
        pass: config.mail.password,
      },
    });

    let mailOptions = {
      from: config.mail.sender,
      to: '<EMAIL>',
      subject: `Công ty Cổ phần Công nghệ HeyU Việt Nam`,
      text: `Thống Kê Doanh Thu Five Star ${JSON.stringify(data)}`
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.log(error);
      }
    });
  };
}

module.exports = new FiveStarJob;
