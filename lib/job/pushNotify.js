const async = require('async');
const MembersModel = require('../models/member')
const config = require('config')
const rp = require('request-promise')
const CONSTANTS = require('../const')

class NotifyManager {
  sendToMember(id, title, description, data, eventName, appName) {
    return new Promise((resolve, reject) => {
      const options = {
        method: 'POST',
        uri: `${config.proxyRequestServer.pushNotify}/api/v1.0/push-notification/member`,
        body: {
            userId: id,
            title: title,
            message: description,
            data: data,
            eventName: eventName,
            appName
        },
        json: true // Automatically stringifies the body to JSON
      };

      rp(options)
        .then((result) => {
          if(result.code === 501) {
            return reject(result)
          }

          if((result.code === CONSTANTS.CODE.FAIL) || (result.code === CONSTANTS.CODE.SYSTEM_ERROR)) {
            return reject(result)
          }

          resolve(result)
        })
        .catch((err) => {
          reject(err)
        });
    });
  }

  sendAllToHCM(title, description, data) {
    return new Promise((resolve, reject) => {
      const options = {
        method: 'POST',
        uri: `${config.proxyRequestServer.pushNotify}/api/v1.0/push-notification/all`,
        body: {
            query: {
              type: 0,
              region: 'hcm'
            },
            title: title,
            message: description,
            data: data
        },
        json: true // Automatically stringifies the body to JSON
      };

      rp(options)
        .then((result) => {
          if(result.code === 500) {
            return reject(result)
          }

          resolve(result)
        })
        .catch((err) => {
          reject(err)
        });
    });
  }

}
module.exports = new NotifyManager
