const _ = require('lodash');
const ms = require('ms');
const async = require('async');
const PushNotifyManager = require('./pushNotify');
const OrderStoreModel = require('../models/orderStore')
const ProductModel = require('../models/product')
const OrderStoreLogModel = require('../models/orderStoreLog')
const MemberModel = require('../models/member');
const TransactionLogModel = require('../models/transactionLog');
const StaffModel = require('../models/staff');
const redisConnections = require('../connections/redis')
const CONSTANTS = require('../const');
const orderHelper = require('../utils/order');
const OrderInteractionManager = require('../job/orderInteractionManager');
const rp = require('request-promise')
const config = require('config')

const firstTimePush = ms('1m')
const secondTimePush = ms('2m')

class PushConfirmManager {
  constructor() {
    this.listJob = {};
    // this.init();
  }

  init() {

  }

  add(orderId, sound) {
    orderId = orderId.toString();

    this.remove(orderId);

    this.listJob[orderId] = setTimeout(() => {
      this.checkPushConfirm(orderId, false, sound);
    }, firstTimePush)

  }

  checkPushConfirm(orderId, isFinal, sound) {

    let orderInf;
    let staffs = [];

    const checkOrderStore = (next) => {
      OrderStoreModel
        .findOne({
          _id: orderId
        })
        .populate('store','member')
        .lean()
        .exec((err, result) => {

          if(err || !result) {
            return next('NOT FOUND ORDER')
          }

          if(result.status !== CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION) {
            return next('ORDER DONT NEED CONFIRMING')
          }

          if(result.timeWait) {
            return next('ORDER DONT NEED CONFIRMING')
          }

          orderInf = result

          next();
        })
    }

    const getStaff = (next) => {
      if (!orderInf || !orderInf.store || !orderInf.store._id) {
        return next();
      }

      StaffModel
        .find({
          store: orderInf.store._id,
          status: 1,
          online: 1
        }, 'member')
        .lean()
        .exec((err, results) => {
          if (err) {
            return next(err);
          }

          if (results && results.length) {
            staffs = results.map(result => result.member.toString());
          }

          next();
        })
    }

    const handlePush = (next) => {
      if(orderInf && orderInf.store && orderInf.store.member) {
        next();
        PushNotifyManager
          .sendToMember(orderInf.store.member.toString(), 'Xác nhận đơn hàng', `Đã khá lâu bạn chưa xác nhận đơn hàng. Bạn vui lòng kiểm tra lại tình trạng đơn và xác nhận cho khách biết bạn nhé!`, { link: 'SBHDetailOrderScreen', extras: { id: orderId }, sound }, 'new_order_merchant', 'tickbox')

        if (staffs && staffs.length) {
          staffs.map(staff => {
            PushNotifyManager
              .sendToMember(staff.toString(), 'Xác nhận đơn hàng', `Đã khá lâu bạn chưa xác nhận đơn hàng. Bạn vui lòng kiểm tra lại tình trạng đơn và xác nhận cho khách biết bạn nhé!`, { link: 'SBHDetailOrderScreen', extras: { id: orderId }, sound }, 'new_order_merchant', 'tickbox')
          })
        }
      } else {
        next('NOT FOUND MEMBER STORE')
      }
    }

    async.waterfall([
      checkOrderStore,
      getStaff,
      handlePush
    ], (err, data) => {

      if(err) {
        console.log(`${orderId} Job finished cause: `, err);
        this.remove(orderId);
        return;
      }

      if(!isFinal) {

        console.log('First Job Confirm push success',orderId);
        this.remove(orderId);

        this.listJob[orderId] = setTimeout(() => {
          this.checkPushConfirm(orderId, true, sound);
        }, secondTimePush)

      } else {

        console.log('Second Job Confirm push success',orderId);

      }
    });


  }

  remove(orderId) {
    orderId = orderId.toString()

    if (this.listJob && this.listJob[orderId]) {
      clearTimeout(this.listJob[orderId]);
      delete this.listJob[orderId];
    }
  }

}

module.exports = new PushConfirmManager;
