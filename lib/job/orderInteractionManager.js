const _ = require('lodash');
const ms = require('ms');
const async = require('async');
const ConfigModel = require('../models/config');
const CONSTANTS = require('../const');

class OrderInteractionManager {
  constructor() {
    this.init();
  }

  init() {
    // Set default config
    this.config = {
      hn: {
        serviceCharge: 0
      },
      "vietnam:nghean": {
        serviceCharge: 0
      },
      "vietnam:thanhhoa": {
        serviceCharge: 0
      },
      "vietnam:thainguyen": {
        serviceCharge: 0
      },
      "vietnam:cantho": {
        serviceCharge: 0
      },
      "vietnam:danang": {
        serviceCharge: 0
      },
      "vietnam:hatinh": {
        serviceCharge: 0
      }
    }

    this.syncConfig();
    setInterval(() => {
      this.syncConfig();
    }, ms("5m"))
  }

  syncConfig() {
    ConfigModel
      .findOne({
        type: CONSTANTS.CONFIG_TYPE.SERVICE_CHARGE_MERCHANT,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(!err && result) {
          this.config = result.config;
        }
      })
  }

  getConfig(region) {
    return this.config[region];
  }
}

module.exports = new OrderInteractionManager
