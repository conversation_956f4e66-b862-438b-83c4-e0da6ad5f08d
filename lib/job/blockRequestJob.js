const _ = require('lodash');
const async = require('async');
const config = require('config');
const ms = require('ms');
const CONSTANTS = require('../const');
var schedule = require('node-schedule');

class BlockRequestJob {
  constructor() {
    this.currentOrder = {}
    this.init();
  }

  init() {
    schedule.scheduleJob('*/5 * * * *', () => {
      Object.keys(this.currentOrder).forEach((key, i) => {
        if(this.currentOrder[key] && Date.now() - this.currentOrder[key] > ms('4m')) {
          this.remove(key)
        }
      });
    });
  }

  setRequest(id) {
    this.currentOrder[id] = Date.now()
  }

  checkRequest(id) {
    return this.currentOrder[id];
  }
  remove(id) {
    if(this.currentOrder[id]) {
      _.unset(this.currentOrder, id);
    }
  }
}

module.exports =  new BlockRequestJob;
