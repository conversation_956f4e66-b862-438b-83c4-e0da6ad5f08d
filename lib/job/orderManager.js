const _ = require('lodash');
const ms = require('ms');
const async = require('async');
const PushNotifyManager = require('./pushNotify');
const OrderStoreModel = require('../models/orderStore')
const ProductModel = require('../models/product')
const OrderStoreLogModel = require('../models/orderStoreLog')
const MemberModel = require('../models/member');
const ConfigModel = require('../models/config');
const redisConnections = require('../connections/redis')
const CONSTANTS = require('../const');
const orderHelper = require('../utils/order');
const OrderInteractionManager = require('../job/orderInteractionManager');
const rejectForAdmin = require('../routes/admin/order/reject');
const confirmForAdmin = require('../routes/admin/order/confirmOrder');
const rp = require('request-promise')
const config = require('config')

class OrderManager {
  constructor() {
    this.listJob = {};
    this.listJobReject = {};
    this.listJobPushAdmin = {};
    this.listJobRePush = {};

    this.init();
  }

  init() {
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    OrderStoreModel
      .find({ status: CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION, updatedAt: { $gt: currentDate.getTime() } }, '_id region')
      .populate('store', 'phone')
      .exec((err, jobs) => {
        if (err) {
          return setTimeout(() => {
            this.init();
          }, 5000)
        }

        logger.logInfo('Init jobs order', err, jobs.length);
        jobs.forEach((job) => {
          this.add(job._id, job.region, job.store.phone);
        });
      });
  }

  add(orderId, region, phones = []) {
    orderId = orderId.toString();

    const listBusinessType = ['61551942d5f7a54fb77b4acc', '61551941d5f7a54fb77b4aca', '6155193ed5f7a54fb77b4ac5'];
    const now = new Date();
    this.remove(orderId);
    this.removeJobReject(orderId);

    if (region !== 'vietnam:hungyen' && !phones.includes('0941522717')) {
      this.listJob[orderId] = setTimeout(() => {
        this.confirmOrder(orderId)
      }, ms('3m'))
    }

    this.listJobReject[orderId] = setTimeout(() => {
      this.rejectOrder(orderId, true);
    }, ms('5h'))

    this.listJobPushAdmin[orderId] = setTimeout(() => {
      this.pushToAdmin(orderId);
    }, ms('1m'))
  }

  addJobRePushed(orderId, members = [], time, sound, region, phones) {
    orderId = orderId.toString();

    this.listJobRePush[orderId] = setTimeout(() => {
      this.add(orderId, region, phones);
      if (members && members.length) {
        members.map(member => {
          PushNotifyManager.sendToMember(member.toString(), 'Xác nhận đơn hàng', `Bạn có đơn hàng báo khách chờ. Bạn vui lòng vào xác nhận đơn hàng. Xin cảm ơn`, { link: 'SBHDetailOrderScreen', extras: { id: orderId }, sound }, 'new_order_merchant', 'tickbox');
        })
      }
    }, time);
  }

  confirmOrder(orderId) {
    orderId = orderId.toString();
    const now = new Date();

    if (now.getHours() > 21 || now.getHours() < 8 || (now.getHours() === 8 && now.getMinutes() < 30) || now.getHours() === 12 || ((now.getHours() === 13 && now.getMinutes() < 15))) {
      confirmForAdmin({
        body: {
          id: orderId
        }
      }, {
        json: (data) => {
          this.remove(orderId);
          this.removeJobRePush(orderId);
        }
      })
    }

    return;

    OrderStoreModel
      .findOne({
        _id: orderId,
        status: CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION
      }, 'region code')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return;
        }

        if (result.region === 'vietnam:haiduong') {
          if (now.getHours() > 17 || (now.getHours() === 17 && now.getMinutes() >= 30) || now.getHours() < 8 || (now.getHours() === 8 && now.getMinutes() < 30) || now.getHours() === 12 || ((now.getHours() === 13 && now.getMinutes() < 15))) {
            confirmForAdmin({
              body: {
                id: orderId
              }
            }, {
              json: (data) => {
                this.remove(orderId);
                this.removeJobRePush(orderId);
              }
            })
          }
        } else {
          confirmForAdmin({
            body: {
              id: orderId
            }
          }, {
            json: (data) => {
              this.remove(orderId);
              this.removeJobRePush(orderId);
            }
          })
        }
      })
  }

  rejectOrder(orderId, autoReject) {
    this.removeJobReject(orderId);

    rejectForAdmin({
      body: {
        id: orderId,
        backSSM: 1,
        autoReject
      }
    }, {
      json: (data) => {
        this.remove(orderId);
        this.removeJobRePush(orderId);
      }
    })
  }

  pushToAdmin(orderId) {
    let adminPhone = [];
    let adminId = [];

    const getConfig = (next) => {
      ConfigModel
        .findOne({
          type: CONSTANTS.CONFIG_TYPE.PUSH_ADMIN
        }, 'config')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.FAIL
            })
          }

          adminPhone = result.config;

          next();
        })
    }

    const getAdminId = (next) => {
      MemberModel
        .find({
          phone: { $in: adminPhone }
        }, '_id')
        .lean()
        .exec((err, results) => {
          if (err) {
            return next(err)
          }

          adminId = results.map(result => result._id);

          next();
        })
    }

    async.waterfall([
      getConfig,
      getAdminId
    ], (err, data) => {
      if (err) {
        return;
      }

      OrderStoreModel
        .findOne({
          _id: orderId,
          status: CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION
        }, 'region code')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return;
          }

          this.removeJobPushAdmin(orderId);
          adminId.map(admin => {
            PushNotifyManager.sendToMember(admin.toString(), 'TickBox', `Đơn hàng chưa được xác nhận khu vực ${result.region} ${result.code}`, { link: 'WebviewScreen', extras: { source: 'https://db.heyu.asia' } }, 'order_update_customer', 'tickbox');
          })
        })
    });
  }

  remove(orderId) {
    orderId = orderId.toString()

    if (this.listJob[orderId]) {
      clearTimeout(this.listJob[orderId]);
      delete this.listJob[orderId];
    }

    // if (this.listJobReject[orderId]) {
    //   clearTimeout(this.listJobReject[orderId]);
    //   delete this.listJobReject[orderId];
    // }

    if (this.listJobPushAdmin[orderId]) {
      clearTimeout(this.listJobPushAdmin[orderId]);
      delete this.listJobPushAdmin[orderId];
    }
  }

  removeJobRePush(orderId) {
    orderId = orderId.toString()

    if (this.listJobRePush[orderId]) {
      clearTimeout(this.listJobRePush[orderId]);
      delete this.listJobRePush[orderId];
    }
  }

  removeJobReject(orderId) {
    orderId = orderId.toString()

    if (this.listJobReject[orderId]) {
      clearTimeout(this.listJobReject[orderId]);
      delete this.listJobReject[orderId];
    }
  }

  removeJobPushAdmin(orderId) {
    orderId = orderId.toString()

    if (this.listJobPushAdmin[orderId]) {
      clearTimeout(this.listJobPushAdmin[orderId]);
      delete this.listJobPushAdmin[orderId];
    }
  }
}

module.exports = new OrderManager;
