module.exports = {
	USER: {
		TOKEN_EXPIRE: {
			head: 'Thông báo',
			body: '<PERSON>ên làm việc đã hết hoặc do người nào đó đã đăng nhập tài khoản của bạn ở thiết bị khác. Vui lòng đăng nhập lại. Xin cảm ơn.'
		},
		NOT_ALLOW_PUSH: {
			head: 'Thông báo',
			body: 'Không tìm thấy token để push cho User này'
		},
		PERMISSION_DENY: {
			head: 'Thông báo',
			body: 'Bạn không có quyền thực hiện thao tác này!'
		}
	},
	SYSTEM: {
		ERROR: {
			head: 'Thông báo',
			body: 'Hệ thống đang bận vui lòng thử lại'
		},
		WRONG_PARAMS: {
			head: 'Thông báo',
			body: 'Bạn vui lòng kiểm tra lại dữ liệu vừa nhập. Xin cảm ơn.'
		}
	},
	PRODUCT: {
		NOT_FOUND_STORE:{
			head: 'Thông báo',
			body: 'Kh<PERSON>ng tìm thấy cửa hàng đã nhập'
		},
		PRODUCT_TYPE_EXISTS:{
			head: 'Thông báo',
			body: 'Danh mục đã tồn tại'
		},
		PRODUCT_TYPE_NOT_EXISTS:{
			head: 'Thông báo',
			body: 'Danh mục không tồn tại'
		},
		PRODUCT_NOT_EXISTS:{
			head: 'Thông báo',
			body: 'Sản phẩm không tồn tại'
		},
		PRODUCT_EXISTS:{
			head: 'Thông báo',
			body: 'Sản phẩm đã tồn tại'
		},
		PRICE_PROMOTE:{
			head: 'Thông báo',
			body: 'Giá khuyến mãi phải nhỏ hơn giá gốc'
		},
		PHONE_NOT_VALID: {
			head: 'Thông báo',
			body: 'Số điện thoại không đúng định dạng. Vui lòng kiểm tra lại'
		},
		WORKING_TIME: {
			head: 'Thông báo',
			body: 'Giờ đóng cửa phải muộn hơn giờ mở cửa'
		},
		STORE_NOT_CREATE: {
			head: 'Thông báo',
			body: 'Bạn chưa tạo cửa hàng'
		},
		ADD_IMAGE: {
			head: 'Thông báo',
			body: 'Bạn cần thêm ảnh đại diện và ảnh bìa để tạo gian hàng'
		}
	},
	ORDER: {
		REJECT_FAIL_MERCHANT: {
			head: 'Thông báo',
			body: 'Hủy đơn hàng không thành công. Vui lòng liên hệ 1900.633.689 để được trợ giúp. Xin cảm ơn.'
		},
		REJECT_FAIL: {
			head: 'Thông báo',
			body: 'Hủy đơn hàng không thành công.'
		},
		REJECT_SUCCESS: {
			head: 'Thông báo',
			body: 'Huỷ đơn hàng thành công.'
		},
		CONFIRM_FAIL: {
			head: 'Thông báo',
			body: 'Xác nhận đơn hàng không thành công. Vui lòng liên hệ 1900.633.689 để được trợ giúp. Xin cảm ơn.'
		},
		CONFIRM_SUCCESS: {
			head: 'Thông báo',
			body: 'Xác nhận đơn hàng thành công.'
		},
		STORE_CLOSED: {
			head: 'Thông báo',
			body: 'Xin lỗi, hiện tại cửa hàng đang dừng hoạt động. Bạn vui lòng chọn cửa hàng khác để mua hàng. Xin cảm ơn.'
		},
		DISTANCE_TOO_FAR: {
			head: 'Thông báo',
			body: 'Rất xin lỗi bạn hiện tại chúng tôi chưa phục vụ đơn hàng có khoảng cách quá xa. Xin cảm ơn.'
		},
		MONEY_IS_NOMORE_VALID: {
			head: 'Thông báo',
			body: 'Đã có lỗi xảy ra trong quá trình tính phí Ship của đơn hàng này. Bạn vui lòng thoát app ra vào lại sau đó tạo đơn hàng. Xin cảm ơn.'
		},
		PRODUCT_IS_AVAILABLE: {
			head: 'Thông báo',
			body: 'Giỏ hàng bạn chọn có sản phẩm đã hết hàng, bạn vui lòng xoá giỏ hàng và chọn lại sản phẩm khác. Xin cảm ơn.'
		},
		STORE_DEMO: {
			head: 'Thông báo',
			body: 'HeyU đang trong quá trình đưa dần các Gian hàng lên App. Nếu bạn có Cửa hàng hãy liên hệ ngay 1900.633.689 để hợp tác với HeyU\nHàng ngàn quán ăn chuẩn bị có mặt trên HeyU\nCùng với đó là rất nhiều chương trình ưu đãi khuyến mãi\nTrận trọng cảm ơn'
		},
		WAIT_NOTICE_SUCCESS: 'Báo chờ đơn hàng thành công.',
		BLOCK_CREATE_ORDER: {
			head: 'Thông báo',
			body: 'Lỗi nội bộ hệ thống. Chúng tôi đang cố gắng khắc phục tình trạng kết nối của ứng dụng. Bạn vui lòng quay lại sau giây lát. Xin cảm ơn'
		},
	},
	PROMOTE: {
		PROMOTE_EXISTS: {
			head: 'Thông báo',
			body: 'Đã tồn tại mã khuyến mãi trùng với chiến lược này. Bạn vui lòng tạo mã với chiến lược khác. Xin cảm ơn.'
		},
		PROMOTE_NOT_EXISTS: {
			head: 'Thông báo',
			body: 'Mã khuyến mãi không tồn tại.'
		},
		CHECK_PROMOTE: {
			head: 'Thông báo',
			body: 'Bạn vui lòng kiểm tra lại mã khuyến mãi vừa nhập. Xin cảm ơn.'
		},
		NOT_EXISTS: "Mã khuyến mãi không tồn tại. Vui lòng kiểm tra lại. Xin cảm ơn",
		MAXIMUM_USE: "Quá số lần sử dụng mã. Vui lòng kiểm tra lại. Xin cảm ơn",
		NOT_ACTIVE: "Mã khuyến mãi hết hiệu lực",
		MAXIMUM_USE_PER_DAY: "Quá số lần sử dụng mã trong ngày. Vui lòng kiểm tra lại. Xin cảm ơn",
		ERROR: "Đã có lỗi xảy ra vui lòng thử lại",
		MEMBER_INFO_NOT_VALID: "Mã khuyến mãi hết hiệu lực",
		ORDER_INFO_NOT_VALID: "ORDER_INFO_NOT_VALID"
	},
	STAFF: {
		STAFF_EXISTS: {
			head: 'Thông báo',
			body: 'Nhân viên đã tồn tại hoặc đang là nhân viên của gian hàng khác. Bạn vui lòng thêm nhân viên khác. Xin cảm ơn.'
		},
		STAFF_OWNED_STORE: {
			head: 'Thông báo',
			body: 'Số điện thoại này đang quản lý gian hàng khác. Bạn vui lòng thêm số điện thoại khác làm nhân viên. Xin cảm ơn.'
		}
	},
	TOPPING: {
		TOPPING_NOT_EXISTS:{
			head: 'Thông báo',
			body: 'Tùy chọn món không tồn tại'
		},
	}
}
