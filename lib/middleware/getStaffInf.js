const async = require('async');
const _ = require('lodash');
const StaffModel = require('../models/staff');
const UserStoreModel = require('../models/userStore');
const CONSTANTS = require('../const')
const MESSAGES = require('../message')

module.exports = (req, res, next) => {
  const userId = _.get(req, 'user.id', '');
  let storeId;

  let objStore = {
    member: userId.toString()
  };
  req.store = objStore;

  const checkParams = (done) => {
    if (!userId) {
      return done({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    done();
  }

  const getStoreInf = (done) => {
    UserStoreModel
      .findOne({
        member: userId
      }, '_id')
      .lean()
      .exec((err, store) => {
        if (err) {
          return done(err);
        }

        if (store) {
          storeId = store._id.toString();
          objStore.member = userId.toString();
        }

        done();
      })
  }

  const getStoreFromStaff = (done) => {
    if (storeId) {
      return done();
    }

    StaffModel
      .findOne({
        member: userId,
        status: 1
      }, 'store')
      .populate('store', 'member')
      .lean()
      .exec((err, result) => {
        if (err) {
          return done(err);
        }

        if (result && result.store) {
          storeId = result.store._id.toString();
          objStore.member = result.store.member.toString();
        }

        done();
      })
  }

  const getStaffInf = (done) => {
    if (!storeId) {
      return done();
    }

    StaffModel
      .find({
        store: storeId,
        status: 1,
        online: 1
      }, 'member level')
      .lean()
      .exec((err, results) => {
        if (err) {
          return done(err);
        }

        if (!results || !results.length === 0) {
          return done();
        }

        objStore.id = storeId;
        objStore.staffs = [];
        results.map(result => {
          objStore.staffs.push({
            id: result.member.toString(),
            level: result.level
          });
        });
        req.store = objStore;

        done();
      })
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    getStoreFromStaff,
    getStaffInf
  ], (err, data) => {
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    if (err || data) {
      return res.json(err || data);
    }

    next();
  })
}
