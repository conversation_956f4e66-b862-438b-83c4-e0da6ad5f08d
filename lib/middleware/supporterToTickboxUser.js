const _ = require('lodash')
const redisConnections = require('../connections/redis')
const CONSTANTS = require('../const');
const MESSAGES = require('../message');
const User = require('../models/tickboxUser');

module.exports = (req, res, next) => {
  const {userId} = _.get(req, 'body', {}) || _.get(req, 'query', {});
  User.findOne({
    _id: userId,
    active: 1
  })
  .select("-ip")
  .lean()
  .exec((err, result)=>{
    if (err || !result) {
      return res.json({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.USER.PERMISSION_DENY
      })
    }
    req.user = result
    next()
  })
}
