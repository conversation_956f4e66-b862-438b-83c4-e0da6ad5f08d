const _ = require('lodash')
const redisConnections = require('../connections/redis')
const CONSTANTS = require('../const');
const MESSAGES = require('../message');
const TickboxUser = require('../models/tickboxUser');

module.exports = (req, res, next) => {
  const token = _.get(req, 'body.token', '') || _.get(req, 'query.token', '');

    if(!token) {
        return res.json({
            code: CONSTANTS.CODE.TOKEN_EXPIRE,
            message: MESSAGES.USER.TOKEN_EXPIRE
        });
    }

    redisConnections('master').getConnection().get(`user:${token}`, (err, result) => {
        if(err) {
            return res.json({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGES.SYSTEM.ERROR
            });
        }

        if(!result) {
            return res.json({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: MESSAGES.USER.TOKEN_EXPIRE
            });
        }

        try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id')) {
                return res.json({
                    code: CONSTANTS.CODE.TOKEN_EXPIRE,
                    message: MESSAGES.USER.TOKEN_EXPIRE
                });
            }

            TickboxUser
              .findOne({
                _id: objSign.id
              })
              .select('-ip')
              .lean()
              .exec((err, result)=>{
                req.user = result;
                next();
              })

        } catch(e) {
            return res.json({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: MESSAGE.USER.TOKEN_EXPIRE
            });
        }
    });
}
