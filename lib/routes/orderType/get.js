const _ = require('lodash');
const async = require('async');
const OrderTypeModel = require('../../models/orderType');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message');
const locationHelper = require('../../utils/location');
const config = require('config')
const rp = require('request-promise');
const ms = require('ms')
const orderHelper = require('../../utils/order');
const MemberModel = require('../../models/member');
const ConfigDepositModel = require('../../models/configDeposit');

module.exports = (req, res) => {
  const service = _.get(req.body, 'service', '');
  let region = _.get(req.body, 'regionName', 'hn');
  let orderType;
  let maxDeposit;
  let maxDepositShipperAuthened;
  let typeUser;
  let configDeposit;
  let maxDepositStandard = 0;
  let totalDepositStandard = 0;

  const getLocationFromOrigin = (next) => {
    if (!req.body.originPlace || !req.body.originPlace.lat || !req.body.originPlace.lng) {
      return next();
    }

    locationHelper
      .getRegionByLatLng(req.body.originPlace, 2, (err, result) => {
        if (err) {
          return next(err);
        }

        region = result;
        next();
      })
  }

  const findMemberType = (next) => {
    MemberModel
      .findById(req.user.id)
      .lean()
      .select('shop ship')
      .exec((err, result) => {
        if (err) {
          return next(err)
        }
        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }

        if (result.shop.isAuthen) {
          typeUser = 'shopAuthen'
        } else if (result.ship.isAuthen) {
          typeUser = 'shipAuthen'
        } else {
          typeUser = 'shopNotAuthen'
        }

        next();
      })
  }

  const findConfigDeposit = (next) => {
    ConfigDepositModel
      .findOne({
        $or: [{
          'region.allow': region
        }, {
          'region.deny': {
            $ne: region
          }
        }]
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }

        configDeposit = result;

        next();
      })
  }

  const checkConverDeposit = (next) => {

    let depositType = 0
    let totalDepositType = 0
    if (typeUser === 'shopAuthen') {
      depositType = configDeposit.maxDeposit
      totalDepositType = configDeposit.totalDeposit

      if (maxDepositStandard > depositType) {
        depositType = maxDepositStandard
      }
      if (totalDepositStandard > totalDepositType) {
        totalDepositType = totalDepositStandard
      }
    } else if (typeUser === 'shipAuthen') {
      depositType = configDeposit.maxDepositShipperAuthened
      totalDepositType = configDeposit.totalDepositShipperAuthened

    } else {
      depositType = configDeposit.maxDepositShopNotAuthened
      totalDepositType = configDeposit.totalDepositShopNotAuthened
    }
    maxDepositStandard = depositType
    totalDepositStandard = totalDepositType

    maxDeposit = maxDepositStandard;
    maxDepositShipperAuthened = configDeposit.maxDepositShipperAuthened;
    next();
  }

  const preventCreateBigDeposit = (next) => {
    const from = Date.now() - ms('24h');

    orderHelper.getTotalDeposit(req.user.id, from, (err, totalDeposit) => {
      if (err) {
        return next(err);
      }

      if (totalDepositStandard - totalDeposit < maxDeposit) {
        maxDeposit = totalDepositStandard - totalDeposit;
      }

      if (totalDepositStandard - totalDeposit < maxDepositShipperAuthened) {
        maxDepositShipperAuthened = totalDepositStandard - totalDeposit;
      }

      next();
    })
  }

  const getOrderType = (next) => {
    OrderTypeModel
      .findOne({
        status: 1,
        $or: [
          {
            'region.allow': region
          },
          {
            'region.allow': 'all',
            'region.deny': {
              $ne: region
            }
          }
        ],
        service
      }, 'showTip priceIncrease forceMoney messageChangeSalary service resourceFee depositNotAuthen messageNotAuthen maxDeposit maxDepositShipperAuthened')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }

        if (maxDeposit !== undefined) {
          result.maxDeposit = maxDeposit;
        }

        if (maxDepositShipperAuthened !== undefined) {
          result.maxDepositShipperAuthened = maxDepositShipperAuthened;
        }

        orderType = result;
        next();
      })
  }

  const calculateSurgeRate = (next) => {
    const options = {
      method: 'POST',
      uri: `${config.proxyRequestServer.surgePricing}/api/v1.0/auto-surge-pricing/get-surge-rate`,
      body: {
        location: req.body.originPlace
      },
      timeout: 3000,
      json: true // Automatically stringifies the body to JSON
    };

    rp(options)
      .then((result) => {
        if (result.code === 200 && result.data) {
          orderType.priceIncrease = result.data[orderType._id.toString()];
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: orderType
        });
      })
      .catch((err) => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: orderType
        });
      });
  }

  async.waterfall([
    getLocationFromOrigin,
    findMemberType,
    findConfigDeposit,
    checkConverDeposit,
    preventCreateBigDeposit,
    getOrderType,
    calculateSurgeRate
  ], (err, data) => {
    console.log('haha:err', err, data)
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
