const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductModel = require('../../models/product');
const StoreModel = require('../../models/store');
const tool = require('../../utils/tool');

module.exports = (req, res) => {
  const productIds = _.get(req, 'body.ids', []);
  const storeId = _.get(req, 'body.storeId', []);
  const userId = _.get(req, 'user.id', '');
  let region = '';

  const checkParams = (next) => {
    if (!productIds || !productIds.length || !userId || !storeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getRegion = (next) => {
    StoreModel
      .findOne({_id: storeId}, 'region')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        region = result.region;

        next();
      })
  }

  const getProduct = (next) => {
    ProductModel
      .find({
        _id: {$in: productIds},
        status: 1
      })
      .populate({
        path: 'topping',
        match: { status: 1 },
        populate: [{
          path: 'topping',
          match: { status: 1 }
        }]
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        results.map(result => {
          result.totalSold = tool.getTextTotalSold(result.totalSold, region);

          result.store = storeId
        })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    checkParams,
    getRegion,
    getProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
