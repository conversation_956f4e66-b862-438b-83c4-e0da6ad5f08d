const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductModel = require('../../models/product');
const tool = require('../../utils/tool');

module.exports = (req, res) => {
  const storeId = req.body.storeId;
  const type = req.body.type || ''
  const productTypeId = req.body.productType;
  const idProduct = req.body.id;
  const limit = req.body.limit || ''
  let createdAt

  const checkParams = (next) => {
    if(!storeId || !productTypeId || !idProduct || !limit) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const findProduct = (next) => {
    ProductModel
      .findOne({
        _id: idProduct,
        status: 1
      })
      .lean()
      .exec((err,result) => {
        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL
          })
        }
        createdAt = result.createdAt
        next()
      })
  }

  const findPage = (next) => {

    const query = {
      status: 1
    };


    if(type) {
      query.storeType = type
    } else {
      query.store = storeId
    }

    if(productTypeId) {
      query.productType = productTypeId;
    }
    query.createdAt = {
      $lt: createdAt
    }
    ProductModel
      .count(query)
      .sort({
        createdAt: 1
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: Math.floor(count/limit)
        })
      })
  }

  async.waterfall([
    checkParams,
    findProduct,
    findPage
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
