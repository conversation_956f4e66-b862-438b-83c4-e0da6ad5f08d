const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductModel = require('../../models/product');
const tool = require('../../utils/tool');

module.exports = (req, res) => {
  const name = req.body.name || '';
  const type = req.body.type || ''

  const checkParams = (next) => {
    if(!name) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const listProduct = (next) => {

    const query = {
      status: 1
    };

    let options = {
    }

    if(type) {
      query.storeType = type
    } else {
      query.store = storeId
    }

    if(productTypeId) {
      query.productType = productTypeId;
    }

    if(ids.length) {
      query._id = {
        $in: ids
      }
    }

    if(name && name.trim()) {
      query.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
      options.limit = 10
    }

    ProductModel
      .find(query,'',options)
      .populate('store', 'region')
      .lean()
      .exec((err, products) => {
        if(err) {
          return next(err)
        }

        products.forEach((item, i) => {
          if (item.store && item.store.region) {
            item.totalSold = tool.getTextTotalSold(item.totalSold, item.store.region);
          }

          item.store = storeId

          if (item.unit && item.quantitative) {
            item.name = `${item.name} (${item.quantitative} ${item.unit})`;
          }
        });

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: products
        })
      })
  }

  async.waterfall([
    checkParams,
    listProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
