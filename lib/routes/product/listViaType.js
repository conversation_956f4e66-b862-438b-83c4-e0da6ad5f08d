const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductModel = require('../../models/product');
const StoreModel = require('../../models/store');
const tool = require('../../utils/tool');

module.exports = (req, res) => {
  const storeId = req.body.storeId;
  const name = req.body.name || '';
  const type = req.body.type || ''
  const productTypeId = req.body.productType;
  const ids = req.body.ids || [];

  const limit = _.get(req, 'body.limit', 12);
  const page = _.get(req, 'body.page', 0);

  const checkParams = (next) => {
    // if(!storeId) {
    //   return next({
    //     code: CONSTANTS.CODE.WRONG_PARAMS
    //   })
    // }
    next();
  }

  const listProduct = (next) => {

    const query = {
      status: 1
    };

    let options = {
    }

    if (type) {
      query.storeType = type
    } else if (storeId) {
      query.store = storeId
    }

    if (productTypeId) {
      query.productType = productTypeId;
    }

    if (ids.length) {
      query._id = {
        $in: ids
      }
    }

    if (name && name.trim()) {
      query.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
    }

    options.limit = limit
    options.skip = limit * page
    ProductModel
      .find(query, '', options)
      .populate('productType')
      .sort({
        createdAt: 1
      })
      .lean()
      .exec((err, products) => {
        if (err) {
          return next(err)
        }
        products.forEach((item, i) => {
          // item.store = storeId
          if (!item.store) {
            item.store = '6152ede065aa0b7aa3445cce';
          }

          if (item.unit && item.quantitative) {
            if (item.unit === 'Khác') {
              item.name = `${item.name} (${item.quantitative})`;
            } else {
              item.name = `${item.name} (${item.quantitative} ${item.unit})`;
            }
          }

          // if (!item.store && item.storeType) {
          //   StoreModel
          //     .findOne({
          //       type: item.storeType
          //     }, '_id')
          //     .near('location', {
          //       center: {
          //         coordinates: [location.lng, location.lat],
          //         type: 'Point'
          //       },
          //       maxDistance: distance
          //     })
          //     .populate('businessType')
          //     .populate('subType', 'name')
          //     .lean()
          //     .exec(cb)
          // }
        });

        console.log('haha', products)
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: products
        })
      })
  }

  async.waterfall([
    checkParams,
    listProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
