const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductModel = require('../../models/product');
const StoreModel = require('../../models/store');
const tool = require('../../utils/tool');

module.exports = (req, res) => {
  const storeId = req.body.storeId;
  const name = req.body.name || '';
  const type = req.body.type || ''
  const productTypeId = req.body.productType;
  const ids = req.body.ids || [];

  const limit = _.get(req, 'body.limit', 12);
  const page = _.get(req, 'body.page', 0);
  let storeInf;

  const checkParams = (next) => {
    if(!storeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const checkStore = (next) => {
    StoreModel
      .findOne({
        _id: storeId
      })
      .lean()
      .exec((err, store) => {
        if (err || !store) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        storeInf = store;

        next();
      })
  }

  const listProduct = (next) => {

    const query = {
      status: 1
    };

    let options = {
    }

    if(type && !storeInf.member) {
      query.storeType = type
    } else {
      query.store = storeId
    }

    if(productTypeId) {
      query.productType = productTypeId;
    }

    if(ids.length) {
      query._id = {
        $in: ids
      }
    }

    if(name && name.trim()) {
      query.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
    }

    options.limit = limit
    options.skip = limit*page
    ProductModel
      .find(query,'',options)
      .populate('productType')
      .populate('store', 'region')
      .populate({
        path: 'topping',
        match: { status: 1, store: storeId },
        populate: [{
          path: 'topping',
          match: { status: 1, store: storeId }
        }]
      })
      .sort('order')
      .lean()
      .exec((err, products) => {
        if(err) {
          return next(err)
        }
        products.forEach((item, i) => {
          if (item.store && item.store.region) {
            item.totalSold = tool.getTextTotalSold(item.totalSold, item.store.region);
          }

          item.store = storeId

          if (item.isAvailable && typeof item.isAvailable !== 'number') {
            const isAvailable = item.isAvailable.map(value => value.toString());

            item.isAvailable = 0;
            if (isAvailable.includes(storeId)) {
              item.isAvailable = 1;
            }
          }

          if (item.unit && item.quantitative) {
            if (item.unit === 'Khác') {
              item.name = `${item.name} (${item.quantitative})`;
            } else {
              item.name = `${item.name} (${item.quantitative} ${item.unit})`;
            }
          }
        });

        products.sort((a, b) => {
          if (a.isAvailable > b.isAvailable) return -1
        });

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: products
        })
      })
  }

  async.waterfall([
    checkParams,
    checkStore,
    listProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
