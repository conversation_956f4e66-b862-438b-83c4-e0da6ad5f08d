const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const id = req.body.id || '';
  const status = req.body.status || 0
  const checkParams = (next) => {
    if(!id){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }


  const deactive = (next) => {

    ProductTypeModel
      .findOneAndUpdate({
        _id: id,
        status:!status
      },{
        status: status,
        updatedAt: Date.now()
      },(err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }
        if (status) {
          StoreModel
            .update({
              _id: store
            },{
              $addToSet:{
                productTypes: result._id
              }
            },() => {
              next(null,{
                code: CONSTANTS.CODE.SUCCESS
              })
            })
        } else {
          StoreModel
            .update({
              _id: store
            },{
              $pull:{
                productTypes: result._id
              }
            },() => {
              next(null,{
                code: CONSTANTS.CODE.SUCCESS
              })
            })
        }
      })
  }

  async.waterfall([
    checkParams,
    deactive
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
