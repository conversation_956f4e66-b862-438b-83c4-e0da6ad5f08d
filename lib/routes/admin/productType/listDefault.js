const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')

const ProductTypeModel = require('../../../models/productType');

module.exports = (req, res) => {
  const service = req.body.service;
  const limit = req.body.limit || 0;
  const region = req.body.regionName;
  const checkParams = (next) => {
    if(!service) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }
  const listProductType = (next) => {
    let query = {
      status: 1,
      default: 1,
      service,
      $or:[{
        'region.allow':'all',
        'region.deny':{
          $ne:region
        }
      },{
        'region.allow': region
      }]
    }

    ProductTypeModel
      .find(query)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    checkParams,
    listProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
