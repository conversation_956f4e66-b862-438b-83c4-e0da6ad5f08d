const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const limit = _.get(req, 'body.limit', 15);
  const page = _.get(req, 'body.page', 0);
  const sort = _.get(req, 'body.sort', 1);
  const name = _.get(req, 'body.name', '');
  const store = _.get(req, 'body.store', '');

  let storeType

  const getStoreType = (next) => {
    StoreModel
      .findOne({
        _id: store,
        status: 1
      })
      .select('type')
      .lean()
      .exec((err, result)=>{
        if (_.get(result, 'type')) {
          storeType = _.get(result, 'type')
        }
        next()
      })
  }

  const listProductType = (next) => {

    if(!store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.NOT_FOUND_STORE
      })
    }

    let objSeach = storeType ? {
      storeType
    } : {
      store
    }

    if(name && name.trim()) {
      objSeach.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
    }

    const skip = page*limit;
    const options = {
      limit,
      skip,
      sort: sort == 1 ? 'createdAt' : '-createdAt'
    }
    ProductTypeModel
      .find(objSeach,"",options)
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    getStoreType,
    listProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
