const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {name = '', store = '', icon = ''} = req.body

  let storeType
  let service

  const checkParams = (next) => {
    if(!name || !store || !icon) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const checkStore = (next) => {

    StoreModel
      .findOne({
        _id: store,
        status: 1
      })
      .select('type service')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }
        if (_.get(result, 'type')) {
          storeType = _.get(result, 'type')
        }

        if (_.get(result, 'service')) {
          service = _.get(result, 'service')
        } else {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }

        next();
      })
  }

  const checkName = (next) => {
    ProductTypeModel
      .count({
        store: store,
        name: name.trim()
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_EXISTS
          })
        }
        next();
      })
  }

  const createProductType = (next) => {
    const prodTypeObj = {
      name,
      status:1,
      nameAlias: tool.change_alias(name.trim()),
      service,
      icon,
      default: icon ? 1 : 0
    }

    if (storeType) {
      prodTypeObj.storeType = storeType
    } else {
      prodTypeObj.store = store
    }

    ProductTypeModel
      .create(prodTypeObj,(err,result) => {
        if(err) {
          return next(err)
        }
        StoreModel
          .update({
            _id: store
          },{
            $addToSet:{
              productTypes: result._id
            }
          },() => {
            next(null,{
              code: CONSTANTS.CODE.SUCCESS
            })
          })
      })
  }

  async.waterfall([
    checkParams,
    checkStore,
    checkName,
    createProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
