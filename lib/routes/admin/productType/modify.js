const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {name = '', store = '', icon, id} = req.body

  const checkParams = (next) => {
    if(!id || !name || !name.trim() || !store){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  let storeType

  const getStoreType = (next) => {
    StoreModel
      .findOne({
        _id: store,
        status: 1,
      })
      .select('type')
      .lean()
      .exec((err, result)=>{
        if (_.get(result, 'type')) {
          storeType = _.get(result, 'type')
        }
        next()
      })
  }

  const findOld = (next) => {
    ProductTypeModel
      .findOne({
        _id: id,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }
        oldName = result.name
        if(oldName.trim() === name.trim()) {
          return next({
            code: CONSTANTS.CODE.SUCCESS
          })
        }
        next();
      })
  }

  const checkNameExists = (next) => {

    ProductTypeModel
      .findOne({
        name: name.trim(),
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_EXISTS
          })
        }
        next();
      })
  }

  const update = (next) => {

    const objUpdate = {
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()),
      updatedAt: Date.now()
    }
    if (icon || icon === '') {
      objUpdate.icon = icon
      objUpdate.default = icon ? 1 : 0
    }
    ProductTypeModel
      .update({
        _id: id
      },objUpdate,(err, result) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    getStoreType,
    findOld,
    checkNameExists,
    update
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
