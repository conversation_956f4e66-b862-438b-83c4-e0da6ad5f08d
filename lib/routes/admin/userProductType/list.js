const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const UserProductModel = require('../../../models/userProduct');
const UserProductTypeModel = require('../../../models/userProductType');
const UserStoreModel = require('../../../models/userStore');

const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {
    body: {
      limit = 10,
      page = 0,
      sort = 1,
      name = '',
      store = '',
      level
    },
    user
  } = req

  const checkParams = (next) => {
    if (!store){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next()
  }

  const listProductType = (next) => {
    let query = {
      store,
      status: 1
    }

    if (name && name.trim()) {
      query.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
    }

    if (_.isNumber(level)) {
      query.level = level
    }

    UserProductTypeModel
      .find(query)
      .skip(limit*page)
      .limit(limit)
      .sort(sort == 1 ? 'createdAt' : '-createdAt')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    checkParams,
    listProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
