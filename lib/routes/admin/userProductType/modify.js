const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const UserProductModel = require('../../../models/userProduct');
const UserProductTypeModel = require('../../../models/userProductType');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {name = '', store = '', icon, id} = req.body
  let newPType
  const checkParams = (next) => {
    if(!id || !name || !name.trim() || !store){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const findOld = (next) => {
    UserProductTypeModel
      .findOne({
        _id: id,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }
        let oldName = result.name
        if(oldName.trim() === name.trim() && (!icon || icon === result.icon)) {
          return next({
            code: CONSTANTS.CODE.SUCCESS
          })
        }
        next();
      })
  }

  const checkNameExists = (next) => {

    UserProductTypeModel
      .findOne({
        _id: {$ne: id},
        name: name.trim(),
        store,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_EXISTS
          })
        }
        next();
      })
  }

  const updateUserProductType = (next) => {

    const objUpdate = {
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()),
      updatedAt: Date.now()
    }

    if (icon) {
      objUpdate.icon = icon
    }

    UserProductTypeModel
      .update({
        _id: id
      },objUpdate,(err, result) => {
        if(err) {
          return next(err);
        }
        newPType = result
        next()
      })
  }

  const updateProductType = next => {
    if (!newPType || (newPType && newPType.level !== 1)) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật thành công!'
        }
      })
    }

    ProductTypeModel
      .findOneAndUpdate({
        _id: newPType._id
      },
      newPType,
      {
        new: true,
        upsert: true,
        setDefaultsOnInsert: true
      })
      .lean()
      .exec((err, result)=>{
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật thành công!'
          }
        })
      })
  }

  async.waterfall([
    checkParams,
    findOld,
    checkNameExists,
    updateUserProductType,
    updateProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
