const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductTypeModel = require('../../../models/userProductType');
const ProductTypeModel = require('../../../models/productType');
const UserProductModel = require('../../../models/userProduct');
const ProductModel = require('../../../models/product');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {
    body: {id, store},
    user
  } = req

  let productType;
  let service;

  const checkParams = (next) => {
    if(!id || !store){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const getProductType = (next) => {
    UserStoreModel
      .findOne({
        _id: store
      }, 'service')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PRODUCT.STORE_NOT_CREATE
          })
        }

        service = result.service;

        next();
      })
  }

  const getProductTypeOther = (next) => {
    UserProductTypeModel
      .findOne({
        status: 1,
        service,
        nameAlias: 'khac'
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          productType = result._id;
        }

        next();
      })
  }

  const updateProduct = (next) => {
    UserProductModel
      .find({
        productType: id
      })
      .lean()
      .exec((err, products) => {
        if (err) {
          return next(err);
        }

        if (products && products.length) {
          products.forEach((product) => {
            if (product.productType && product.productType.length === 1) {
              let objUpdate = {
                updatedAt: Date.now()
              }

              if (!productType) {
                objUpdate.status = 0;
              } else {
                objUpdate.productType = [productType]
              }

              UserProductModel
                .update({
                  _id: product._id
                }, objUpdate)
                .lean()
                .exec((err, product) => {
                  ProductModel
                    .update({
                      _id: product._id
                    }, objUpdate)
                    .lean()
                    .exec((err, product) => { })
                })
            } else if (product.productType && product.productType.length > 1) {
              let listProductTypes = [];
              product.productType.map(productType => {
                if (productType._id.toHexString() !== id) {
                  listProductTypes.push(productType._id);
                }
              })

              let objUpdate = {
                productType: listProductTypes
              }

              UserProductModel
                .update({
                  _id: product._id
                }, objUpdate)
                .lean()
                .exec((err, product) => {
                  ProductModel
                    .update({
                      _id: product._id
                    }, objUpdate)
                    .lean()
                    .exec((err, product) => { })
                })
            }
          })
        }

        next();
      })
  }

  const deactive = (next) => {
    UserProductTypeModel
      .findOneAndUpdate({
        _id: id,
        store
      },{
        status: 0,
        updatedAt: Date.now()
      }, {new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }

        ProductTypeModel
          .findOneAndUpdate({
            _id: id
          }, {
            result
          }, (err, result) => {
            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật thành công!'
              }
            })
          })
      })
  }

  async.waterfall([
    checkParams,
    getProductType,
    getProductTypeOther,
    updateProduct,
    deactive
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
