const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const UserProductType = require('../../../models/userProductType');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const ProductLogs = require('../../../models/productLog');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
      body: {id, store, level, approveReason},
      user
    } = req

    let newProduct

    const checkParams = (next) => {
      if (!id || !store || !_.isNumber(level)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        });
      }
      next()
    }

    const updatedProductType = (next) => {
      UserProductType
        .findOneAndUpdate({
          _id: id
        },{level, approveReason},{
          new: true
        })
        .lean()
        .exec((err, newPtype)=>{
          if (level !== 1) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật thành công!'
              }
            })
          }
          ProductType.findOneAndUpdate({
            _id: id
          }, newPtype, {
            new: true,
            upsert: true,
            setDefaultsOnInsert: true
          })
          .lean()
          .exec((err, result)=>{
            next(err || {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật thành công!'
              }
            })
          })
        })
    }

    const updateStore = (next) => {
      if (level !== 1) {
        return next({
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật thành công!'
          }
        })
      }
      Stores
        .findOneAndUpdate({
          _id: store,
        }, {
          $addToSet: {
            productTypes: {
              $each: [id]
            }
          }
        }, {new: true})
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật thành công!'
              }
            })
          }
          next({
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật thành công!'
            }
          })
        })
    }

     async.waterfall([
       checkParams,
       updatedProductType,
       // updateStore
     ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

       err && _.isError(err) && (data = {
         code: CONSTANTS.CODE.SYSTEM_ERROR,
         message: MESSAGES.SYSTEM.ERROR
       });

       res.json(data || err);
     })
  }
