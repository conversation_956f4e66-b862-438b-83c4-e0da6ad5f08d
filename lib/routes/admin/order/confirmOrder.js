const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreModel = require('../../../models/orderStore');
const StoreModel = require('../../../models/store');
const MemberModel = require('../../../models/member');
const TransactionLogModel = require('../../../models/transactionLog');
const ProductModel = require('../../../models/product');
const OrderStoreLogModel = require('../../../models/orderStoreLog');
const StaffModel = require('../../../models/staff');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const PushNotifyManager = require('../../../job/pushNotify');
const OrderManager = require('../../../job/orderManager');
const redisConnections = require('../../../connections/redis')
const orderHelper = require('../../../utils/order');
const OrderInteractionManager = require('../../../job/orderInteractionManager');

const BlockRequestJob = require('../../../job/blockRequestJob');
const MailUtil = require('../../../utils/mail');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.id', '');
  let orderInf;
  let token;
  let idOrderShip;
  let minMoney = 0;
  let userInf
  let userId
  let staffs = [];

  const checkParams = (next) => {
    if (!orderId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.ERROR
      });
    }

    if(BlockRequestJob.checkRequest(orderId)) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        message: MESSAGES.ORDER.BLOCK_CREATE_ORDER
      })
    }

    next();
  }

  const getOrderInf = (next) => {
    OrderStoreModel
      .findOne({
        _id: orderId,
        status: CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION
      })
      .populate('store')
      .populate('customer', 'phone')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        orderInf = result;
        userId = orderInf.store.member
        orderInf.merchantPaySalary = orderInf.merchantPaySalary || 0;

        next();
      })
  }

  const getCart = (next) => {
    if (!orderInf) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data: orderInf
      });
    }

    let cart = orderInf.cart;
    const cartIds = cart.map(cart => cart._id);

    ProductModel
      .find({
        _id: { $in: cartIds }
      }, 'name price pricePromote images description')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL
          });
        }

        results.some(result => {
          const indexInCart = _.findIndex(cart, item => item._id === result._id.toHexString());

          if (indexInCart !== -1) {
            cart[indexInCart] = _.merge(cart[indexInCart], result);
            cart[indexInCart].price = cart[indexInCart].pricePromote;
            orderInf.cart = cart;
            return;
          }
        })

        next()
      })
  }

  const getMemberToken = (next) => {
    let stringToken = 'user'
    if(orderInf.platform === 'web') {
      stringToken = 'tickboxWeb'
    }
    redisConnections('master').getConnection().get(`${stringToken}:${orderInf.customer._id.toHexString()}`, (err, result) => {
      if (err || !result) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      token = result;

      next();
    });
  }

  const getMinMoney = (next) => {
    minMoney = orderHelper.calculateMinMoney(orderInf);

    next();
  }

  const checkMoney = (next) => {

    const moneyCompare = minMoney + orderInf.serviceChargeMerchant

    MemberModel
      .findOne({
        _id: userId
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        if((result.money && ((orderInf.serviceChargeMerchant > 0 && result.money < moneyCompare) || (orderInf.merchantPaySalary && result.money < orderInf.merchantPaySalary))) || result.isBlockStore) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: {
              head: 'Thông báo',
              body: 'Tài khoản TickBox của Shop không đủ để xác nhận đơn hàng. Vui lòng báo shop nạp thêm tiền để xác nhận đơn. Xin cảm ơn'
            }
          })
        }

        next();
      });
  }

  const decreaseMoney = (next) => {
    MemberModel
      .increaseMoney(userId, -(orderInf.serviceChargeMerchant + orderInf.merchantPaySalary), (err, data) => {
        if(err) {
          return next(err);
        }
        userInf = data

        const minMoneyPush = OrderInteractionManager.getConfig(orderInf.region).minMoneyPush || -50000;
        if (userInf.money < minMoneyPush) {
          PushNotifyManager.sendToMember(userId.toString(), 'Thông báo', `Số dư tài khoản của bạn hiện nhỏ hơn số dư tối thiểu. Bạn vui lòng nạp thêm tiền vào tài khoản để tiếp tục bán hàng qua TickBox. Xin cảm ơn.`, { link: 'PaymentInAppScreen', extras: {} }, 'profile_update', 'tickbox');
        }

        next();
      })
  }

  const createOrderSystem = (next) => {
    const body = {
      phone: orderInf.customer.phone,
      note: orderInf.note,
      paymentMethod: orderInf.paymentMethod === 'cash' ? 'cash' : 'inapp',
      cardNumber: orderInf.cardNumber,
      deposit: orderInf.paymentMethod === 'cash' ? orderInf.money : 0,
      salary: orderInf.salary,
      CODFee: orderInf.salaryStrategy.CODFee,
      tip: orderInf.salaryStrategy.tip,
      inapp: orderInf.salaryStrategy.inapp || 0,
      inappDeposit: orderInf.inappDeposit,
      return: 0,
      orderType: orderInf.orderType,
      distance: orderInf.distance,
      service: orderInf.store.service,
      images: [],
      salaryReturn: 0,
      origin_place: {
        address: orderInf.store.address,
        geometry: {
          lat: orderInf.store.location.coordinates[1],
          long: orderInf.store.location.coordinates[0]
        },
        subName: orderInf.store.subAddress
      },
      destination_places: [{
        address: orderInf.receiver.address.name,
        geometry: {
          lat: orderInf.receiver.address.location.lat,
          long: orderInf.receiver.address.location.lng
        },
        receiver: {
          address: orderInf.receiver.address.subName,
          name: orderInf.receiver.name,
          phone: orderInf.receiver.phone,
          deposit: orderInf.paymentMethod === 'cash' ? orderInf.money : 0
        }
      }],
      cartStore: {
        storeInf: {
          _id: orderInf.store._id,
          name: orderInf.store.name || '',
          phone: orderInf.store.phone || [],
          isMerchant: 1,
          member: orderInf.store.member
        },
        cartInf: orderInf.cart,
        orderStore: orderInf._id
      },
      polylines: orderInf.polylines,
      memberToken: token
    }

    if (orderInf.promote) {
      body.promote = orderInf.promote;
      body.discountMoney = orderInf.discountMoney;
    }

    if (orderInf.promoteStore) {
      body.promoteStore = orderInf.promoteStore;
      body.discountMoneyDeposit = orderInf.discountMoneyDeposit;
    }

    if (orderInf.platform) {
      body.platform = orderInf.platform;
    }

    if (orderInf.nativeVersion) {
      body.nativeVersion = orderInf.nativeVersion;
    }

    if(orderInf.platform === 'web') {
      body.appName = 'tickboxWeb'
    }
    BlockRequestJob.setRequest(orderId);
    const options = {
      method: 'POST',
      uri: `${config.proxyRequestServer.orderSystem}/api/v4.1/order/create`,
      body: body,
      json: true // Automatically stringifies the body to JSON
    };

    rp(options)
      .then((result) => {
        console.log('ahihi',result);
        console.log('ahihi body', body);
        if (result.code !== CONSTANTS.CODE.SUCCESS) {
          if(orderInf.serviceChargeMerchant + orderInf.merchantPaySalary) {
            MemberModel
              .increaseMoney(userId, orderInf.serviceChargeMerchant + orderInf.merchantPaySalary, (err, data) => {})
          }

          BlockRequestJob.remove(orderId);

          return next(result)
        }

        idOrderShip = result.data;

        next();
      })
      .catch((err) => {
        if(orderInf.serviceChargeMerchant + orderInf.merchantPaySalary) {
          MemberModel
            .increaseMoney(userId, orderInf.serviceChargeMerchant + orderInf.merchantPaySalary, (err, data) => {})
        }
        BlockRequestJob.remove(orderId);

        console.log('ahihi',err);
        next(err);
      });
  }

  const updateOrder = (next) => {
    const objUpdate = {
      status: CONSTANTS.ORDER_STATUS.PREPARE_PRODUCT
    };

    if (idOrderShip) {
      objUpdate.delivery = idOrderShip;
    }

    if(orderInf.serviceChargeMerchant + orderInf.merchantPaySalary >= 0) {
      TransactionLogModel
        .create({
          member: userId,
          region: orderInf.region,
          message: "Phí dịch vụ đơn hàng",
          data: {
            amount: -orderInf.serviceChargeMerchant,
            idOrder: orderId,
            type: 31,
            back: 0,
            finalCoints: userInf.coints,
            initialCoints: userInf.coints,
            initialRealMoneyShop: userInf.realMoneyShop,
            finalRealMoneyShop: userInf.realMoneyShop,
            initialRealMoney: userInf.realMoney,
            finalRealMoney: userInf.realMoney,
            initialMoney: userInf.money + orderInf.serviceChargeMerchant + orderInf.merchantPaySalary,
            finalMoney: userInf.money + orderInf.merchantPaySalary
          }
        }, () => {
          if (orderInf.merchantPaySalary > 0) {
            TransactionLogModel
              .create({
                member: userId,
                region: orderInf.region,
                message: "Phí ship chương trình đồng tài trợ với HeyU",
                data: {
                  amount: -orderInf.merchantPaySalary,
                  idOrder: orderId,
                  type: 32,
                  back: 0,
                  finalCoints: userInf.coints,
                  initialCoints: userInf.coints,
                  initialRealMoneyShop: userInf.realMoneyShop,
                  finalRealMoneyShop: userInf.realMoneyShop,
                  initialRealMoney: userInf.realMoney,
                  finalRealMoney: userInf.realMoney,
                  initialMoney: userInf.money + orderInf.merchantPaySalary,
                  finalMoney: userInf.money
                }
              }, () => {
                PushNotifyManager.sendToMember(userId, '', ``, { link: '' }, 'profile_update', 'tickbox')
              })
          }
        })
    }

    OrderStoreModel
      .findOneAndUpdate({
        _id: orderId,
        status: {
          $in: [CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION]
        }
      }, objUpdate)
      .populate('store')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.ORDER.CONFIRM_FAIL
          })
        }

        orderInf = result;
        orderInf.merchantPaySalary = orderInf.merchantPaySalary || 0;

        next();
      });
  }

  const getStaff = (next) => {
    StaffModel
      .find({
        store: orderInf.store._id,
        status: 1,
        online: 1
      }, 'member')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next()
        }

        staffs = results.map(result => result.member.toString());

        next();
      })
  }

  const otherTask = (next) => {
    PushNotifyManager.sendToMember(orderInf.customer._id.toHexString(), 'Xác nhận đơn hàng', `Đơn hàng mua tại ${orderInf.store.name} đã được cửa hàng xác nhận. Bạn vui lòng đợi ít phút để tài xế giao hàng cho bạn. Xin cảm ơn`, { link: 'SBHDetailOrderForShopScreen', extras: { id: orderInf._id, idOrder: idOrderShip || '' } }, 'order_update_customer');
    PushNotifyManager.sendToMember(orderInf.store.member.toString(), 'Xác nhận đơn hàng', `Đơn hàng tại ${orderInf.receiver.address.name} đã được xác nhận. Bạn vui lòng làm đồ cho khách. Xin cảm ơn`, { link: 'SBHDetailOrderScreen', extras: { id: orderInf._id, idOrder: idOrderShip || '' } }, 'order_update_merchant');
    if (staffs && staffs.length) {
      staffs.map(staff => {
        PushNotifyManager.sendToMember(staff.toString(), 'Xác nhận đơn hàng', `Đơn hàng tại ${orderInf.receiver.address.name} đã được xác nhận. Bạn vui lòng làm đồ cho khách. Xin cảm ơn`, { link: 'SBHDetailOrderScreen', extras: { id: orderInf._id, idOrder: idOrderShip || '' } }, 'order_update_merchant');
      })
    }

    OrderStoreLogModel.create({
      member: '594e082d885ac733cdb9fa26',
      order: orderInf._id,
      store: orderInf.store._id,
      customer: orderInf.customer._id,
      merchant: orderInf.store.member,
      type: CONSTANTS.ORDER_LOG.CONFIRM
    });

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.ORDER.CONFIRM_SUCCESS
    });
  }

  async.waterfall([
    checkParams,
    getOrderInf,
    getCart,
    getMemberToken,
    getMinMoney,
    checkMoney,
    decreaseMoney,
    createOrderSystem,
    updateOrder,
    getStaff,
    otherTask
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
    OrderManager.remove && OrderManager.remove(orderId);
    OrderManager.removeJobRePush && OrderManager.removeJobRePush(orderId);
  });
}
