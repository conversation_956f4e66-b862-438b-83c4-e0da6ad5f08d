const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const MemberModel = require('../../../models/member');
const BusinessTypeModel = require('../../../models/businessType');
const StoreLogModel = require('../../../models/storeLog');
const UserStoreModel = require('../../../models/userStore');
const UserModel = require('../../../models/user');
const ProductModel = require('../../../models/product');
const OrderStoreModel = require('../../../models/orderStore');
const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
  const {
    body: {
      startTime = moment().startOf('date').valueOf(),
      endTime = moment().endOf('date').valueOf(),
      region
    }
  } = req

  let orders = []
  let statisticData = {}
  let regionsData = {}
  let regions
  let statuses
  let statusTrans = [
        'Chờ xác nhận đơn',
        'Chờ chỉnh sửa đơn',
        'Đang chuẩn bị hàng',
        'Đang giao',
        'Hoàn thành',
        'Huỷ',
      ]
  const checkParams = (next) => {
    next()
  }

  const getOrders = (next) => {
    let times = []
    for (let i = startTime; i < endTime; i += ms('1h')) {
      let time = {
        $gte: i,
        $lte: i+ ms('1h') < endTime ? i + ms('1h') : endTime
      }
      times.push(time)
    }

    async.mapLimit(times, 10,(time, done)=>{
      const query = {
        createdAt: time
      }

      if (region) {
        query.region = region
      }
      OrderStoreModel
        .find(query)
        .lean()
        .exec((err, results)=>{
          orders = orders.concat(results)
          done()
        })
    },(err)=>{
      regions = Array.from(new Set(orders.map(order=>order.region)))
      statuses = Array.from(new Set(orders.map(order=>statusTrans[order.status]).filter(stat=>stat)))
      next();
    })
  }

  const calcTotals = next => {
    statuses.forEach((stt, i) => {
      statisticData[stt] = 0
    });

    regions.forEach((reg, i) => {
      regionsData[reg] = {}
      statuses.forEach((stt, i) => {
        regionsData[reg][stt] = 0
      });
    });

    orders.forEach((order, i) => {
      statisticData[statusTrans[order.status]] += 1
      regionsData[order.region][statusTrans[order.status]] += 1
    });


    next()
  }

  async.waterfall([
    checkParams,
    getOrders,
    calcTotals,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err || {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        statisticData,
        regionsData
      }
    });
  })
}
