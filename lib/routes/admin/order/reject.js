const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const config = require('config')
const OrderStoreModel = require('../../../models/orderStore');
const OrderStoreLogModel = require('../../../models/orderStoreLog');
const StoreModel = require('../../../models/store');
const OrderSystemModel = require('../../../models/orderSystem');
const MemberModel = require('../../../models/member');
const TransactionLogModel = require('../../../models/transactionLog');
const StaffModel = require('../../../models/staff');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const PushNotifyManager = require('../../../job/pushNotify');
const OrderManager = require('../../../job/orderManager');
const memberHelper = require('../../../utils/member');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.id', '');
  const backSSM = _.get(req, 'body.backSSM', 0);
  const shipperWrong = _.get(req, 'body.shipperWrong', 0);
  const autoReject = _.get(req, 'body.autoReject', 0);
  const supporter = _.get(req, 'body.supporter', '');
  const reason = _.get(req, 'body.reason', '');

  let orderInf;
  let userId
  let staffs = [];

  const checkParams = (next) => {
    if (!orderId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const updateOrderToReject = (next) => {
    const listStatus = [CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION, CONSTANTS.ORDER_STATUS.WAIT_FOR_EDIT, CONSTANTS.ORDER_STATUS.PREPARE_PRODUCT, CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT];
    if (!autoReject) {
      listStatus.push(CONSTANTS.ORDER_STATUS.DELIVERING);
    }

    OrderStoreModel
      .findOneAndUpdate({
        _id: orderId,
        status: {
          $in: listStatus
        }
      }, {
        status: CONSTANTS.ORDER_STATUS.REJECT
      })
      .populate('store', '-phone')
      .populate('delivery','status')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.ORDER.REJECT_FAIL
          })
        }

        orderInf = result;
        userId = orderInf.store.member
        orderInf.merchantPaySalary = orderInf.merchantPaySalary || 0;

        next();
      });
  }

  const updateOrderShip = (next) => {
    if (!orderInf.delivery || (orderInf.delivery && (orderInf.delivery.status === 4 || orderInf.delivery.status === 5))) {
      return next();
    }

    // OrderSystemModel
    //   .update({
    //     _id: orderInf.delivery
    //   }, {
    //     status: 5
    //   }, (err, result) => {
    //     next();
    //   })

    const options = {
      method: 'POST',
      uri: `${config.proxyRequestServer.orderSystem}/api/v1.0/admin/order/reject`,
      body: {
        id: orderInf.delivery._id,
        userId: orderInf.customer,
        backSSM,
        shipperWrong,
        orderUpdated: 1,
        supporter,
        reason
      },
      json: true // Automatically stringifies the body to JSON
    };

    rp(options)
      .then((result) => {
        if (result.code === 200) {
          next();
        } else {
          return next(result);
        }
      })
      .catch((err) => {
        return next(err);
      });
  }

  const handleBackMoney = (next) => {

    if (!orderInf.delivery) {
      return next();
    }

    MemberModel
      .increaseMoney(userId, orderInf.serviceChargeMerchant + orderInf.merchantPaySalary, (err, data) => {

        if(err) {
          return next(err);
        }

        next();

        TransactionLogModel
          .create({
            member: userId,
            region: orderInf.region,
            message: "Trả lại phí dịch vụ đơn hàng",
            data: {
              amount: orderInf.serviceChargeMerchant,
              idOrder: orderId,
              type: 31,
              back: 1,
              finalCoints: data.coints,
              initialCoints: data.coints,
              initialRealMoneyShop: data.realMoneyShop,
              finalRealMoneyShop: data.realMoneyShop,
              initialRealMoney: data.realMoney,
              finalRealMoney: data.realMoney,
              initialMoney: data.money - orderInf.serviceChargeMerchant - orderInf.merchantPaySalary,
              finalMoney: data.money - orderInf.merchantPaySalary
            }
          }, () => {
            if (orderInf.merchantPaySalary > 0) {
              TransactionLogModel
                .create({
                  member: userId,
                  region: orderInf.region,
                  message: "Trả lại phí ship chương trình đồng tài trợ với HeyU",
                  data: {
                    amount: orderInf.merchantPaySalary,
                    idOrder: orderId,
                    type: 32,
                    back: 1,
                    finalCoints: data.coints,
                    initialCoints: data.coints,
                    initialRealMoneyShop: data.realMoneyShop,
                    finalRealMoneyShop: data.realMoneyShop,
                    initialRealMoney: data.realMoney,
                    finalRealMoney: data.realMoney,
                    initialMoney: data.money - orderInf.merchantPaySalary,
                    finalMoney: data.money
                  }
                }, () => {
                  PushNotifyManager.sendToMember(userId, '', ``, { link: '' }, 'profile_update', 'tickbox')
                })
            }
          })
      })
  }

  const getStaff = (next) => {
    StaffModel
      .find({
        store: orderInf.store._id,
        status: 1,
        online: 1
      }, 'member')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next()
        }

        staffs = results.map(result => result.member.toString());

        next();
      })
  }

  const handleBackMoneyInapp = (next) => {
    const totalPaymentInapp = orderInf.salaryStrategy.inapp + orderInf.inappDeposit
    if(!totalPaymentInapp || orderInf.status === CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT) {
      return next();
    }

    memberHelper.handleBackDeposit({
      userId: orderInf.customer,
      orderId: orderId,
      inapp: orderInf.salaryStrategy.inapp,
      inappDeposit: orderInf.inappDeposit,
      region: orderInf.region,
      orderInf
    }, (err) => {
      if(err) {
        logger.logError([err], __dirname);
      }

      if (orderInf.salaryStrategy.inapp && orderInf.paymentMethod === 'inapp') {
        setTimeout(() => {
          PushNotifyManager
            .sendToMember(orderInf.customer.toHexString(), 'Thông báo', `Hệ thống vừa hoàn trả ${orderInf.salaryStrategy.inapp + orderInf.inappDeposit} đ vào tài khoản HeyU của bạn do đơn hàng bị huỷ.`, {link: '', extras: {tabFocus: 2}}, 'profile_update')
        },2000)
      }

      next();
    })
  }

  const otherTask = (next) => {
    PushNotifyManager.sendToMember(orderInf.customer.toHexString(), 'Huỷ đơn hàng', `Vì một lý do nào đó hệ thống đã huỷ đơn hàng của bạn. Bạn vui lòng đặt đơn hàng khác. Xin cảm ơn`, { link: 'SBHDetailOrderForShopScreen', extras: { id: orderInf._id, idOrder: orderInf.delivery && orderInf.delivery._id ? orderInf.delivery._id :  '' } }, 'order_update_customer');
    PushNotifyManager.sendToMember(orderInf.store.member.toHexString(), 'Huỷ đơn', `Vì một lý do nào đó hệ thống đã huỷ đơn hàng tại ${orderInf.receiver.address.name}`, { link: 'SBHDetailOrderScreen', extras: {id: orderId} }, 'order_update_merchant', 'tickbox');
    if (staffs && staffs.length) {
      staffs.map(staff => {
        PushNotifyManager.sendToMember(staff.toString(), 'Huỷ đơn', `Vì một lý do nào đó hệ thống đã huỷ đơn hàng tại ${orderInf.receiver.address.name}`, { link: 'SBHDetailOrderScreen', extras: {id: orderId} }, 'order_update_merchant', 'tickbox');
      })
    }

    OrderStoreLogModel.create({
      member: '594e082d885ac733cdb9fa26',
      order: orderInf._id,
      store: orderInf.store._id,
      customer: orderInf.customer,
      merchant: orderInf.store.member,
      type: CONSTANTS.ORDER_LOG.REJECT,
      supporter,
      reason
    });

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.ORDER.REJECT_SUCCESS
    });
  }

  async.waterfall([
    checkParams,
    updateOrderToReject,
    updateOrderShip,
    handleBackMoney,
    getStaff,
    handleBackMoneyInapp,
    otherTask
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);

    OrderManager.remove && OrderManager.remove(orderId);
    OrderManager.removeJobRePush && OrderManager.removeJobRePush(orderId);
  });
}
