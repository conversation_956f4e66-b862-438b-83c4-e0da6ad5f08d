const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreModel = require('../../../models/orderStore');
const StoreModel = require('../../../models/store');
const UserStoreModel = require('../../../models/userStore');
const MemberModel = require('../../../models/member');
const TransactionLogModel = require('../../../models/transactionLog');
const ProductModel = require('../../../models/product');
const OrderStoreLogModel = require('../../../models/orderStoreLog');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const PushNotifyManager = require('../../../job/pushNotify');
const OrderManager = require('../../../job/orderManager');
const redisConnections = require('../../../connections/redis')
const orderHelper = require('../../../utils/order');
const OrderInteractionManager = require('../../../job/orderInteractionManager');

module.exports = (req, res) => {

  const id = _.get(req, 'body.id', '');

  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const updateMember = (next) => {
    MemberModel
      .update({
        _id: id,
        money:{
          $gt:0
        }
      },{
        isBlockStore:0
      },(err, result) => {
        if(err) {
          return next(err);
        }

        if(!result || !result.n) {
          return next('NOT FOUND MEMBER')
        }

        next();
      })
  }

  const updateStore = (next) => {
    StoreModel
      .update({
        member: id
      },{
        golive: 1,
        golink: 1,
        messageGoLive: ''
      },(err, result) => {
        if(err) {
          return next(err)
        }

        next();
      })
  }

  const updateUserStore = (next) => {
    UserStoreModel
      .update({
        member: id
      },{
        golive: 1,
        golink: 1,
        messageGoLive: ''
      },(err, result) => {
        if(err) {
          return next(err)
        }

        next();
      })
  }

  const pushNotify = (next) => {

    next(null,{
      code: CONSTANTS.CODE.SUCCESS
    })

    PushNotifyManager.sendToMember(id, 'Xin chúc mừng', `Cửa hàng của bạn đã hoạt động trở lại trên HeyU.`, { link: '', extras: { } }, 'store_update', 'tickbox');

  }


  async.waterfall([
    checkParams,
    updateMember,
    updateStore,
    updateUserStore,
    pushNotify
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);

  });
}
