const rp = require("request-promise");
const CONSTANTS = require("../../../const");
const MESSAGES = require("../../../message");
const config = require("config");
const _ = require("lodash");
const async = require("async");
const OrderStoreModel = require("../../../models/orderStore");
const ProductTypeModel = require("../../../models/productType");
const MemberModel = require("../../../models/member");
const BusinessTypeModel = require("../../../models/businessType");
const StoreLogModel = require("../../../models/storeLog");
const UserStoreModel = require("../../../models/userStore");
const UserModel = require("../../../models/user");
const ProductModel = require("../../../models/product");
const tool = require("../../../utils/tool");
const ms = require("ms");

var nodemailer = require("nodemailer");
const Excel = require("exceljs");
const path = require("path");
const ABSPATH = path.dirname(process.mainModule.filename);
const fs = require("fs");

module.exports = (req, res) => {
  const {
    body: {
      startTime = moment().startOf("month").valueOf(),
      endTime = moment().valueOf(),
      phones,
      email,
    },
  } = req;
  let times = [], step = ms('30d')
  for (let i = startTime + 0; i < endTime + 0; i += step) {
    let time = {
      $gte: i,
      $lte: i+ step < endTime ? i - 1 + step : endTime + 0
    }
    times.push(time)
  }

  let stores = [];
  let statisticData = [];
  let filepath = `${ABSPATH}/reportrevenueto${moment(startTime).format(
    "DD-MM-YYYY"
  )}.xlsx`;

  const checkParams = (next) => {
    if (_.isEmpty(phones) || !email) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }
    next();
  };

  const getMembersId = (next) => {
    MemberModel.find({
      phone: { $in: phones },
    })
      .select("phone store")
      .populate("store", "name")
      .lean()
      .exec((err, results) => {
        if (err || !results || !results.length) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        results.forEach(result=>{
          result.store.forEach(store=>{
            statisticData.push({
              ...result,
              store
            })
          })
        })

        next();
      });
  };

  const getStoreData = (next) => {
    async.parallelLimit(
      statisticData.map((data) => (callback) => {
        data.orders = []
        async.mapLimit(times, 5, (time, done)=>{
          OrderStoreModel.find({
            store: _.get(data, "store._id"),
            status: CONSTANTS.ORDER_STATUS.DONE,
            updatedAt: time,
          })
            .lean()
            .exec((err, results) => {
              data.orders = data.orders.concat(results)
              done(err)
            });
        },(err)=>{
          callback(err, data);
        })
      }),
      5,
      (err, results) => {
        if (err) {
          return next(err);
        }
        statisticData = results;
        next();
      }
    );
  };

  const createExcelFile = (next) => {
    let records = [];
    statisticData.forEach((record, index) => {
      record.orders.forEach(order => {
        records.push({
          "Mã đơn hàng": order.code,
          'Thời gian': moment(order.updatedAt).format("HH:mm DD/MM/YYYY"),
          "Tên cửa hàng": _.get(record, "store.name"),
          SĐT: _.get(record, 'phone'),
          "Doanh thu": order.money,
          "Khuyến mãi": order.discountMoneyDeposit || 0,
          "Phí dịch vụ": order.serviceChargeMerchant,
          "Thực nhận": order.money - (order.discountMoneyDeposit || 0) - order.serviceChargeMerchant,
        });
      })
    });

    var workbook = new Excel.Workbook();
    var sheet = workbook.addWorksheet("1");
    sheet.addRow().values = ["Công ty Cổ phần Công nghệ HeyU Việt Nam"];
    sheet.addRow().values = [
      `Tài Liệu Thống Kê Doanh Thu Cửa hàng (Thời gian ${moment(
        startTime
      ).format("HH:mm DD/MM/YYYY")} đến ${moment(endTime).format(
        "HH:mm DD/MM/YYYY"
      )})`,
    ];
    sheet.addRow()
    if (records[0]) {
      sheet.addRow().values = Object.keys(records[0]);
    }

    records.forEach(function (item) {
      var valueArray = [];
      valueArray = _.values(item);
      sheet.addRow().values = valueArray;
    });

    workbook.xlsx.writeFile(filepath).then(function () {
      next();
    });
  };

  const sendMail = (next) => {
    var transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: config.mail.sender,
        pass: config.mail.password,
      },
    });
    var mailOptions = {
      from: config.mail.sender,
      to: email,
      subject: `Thống Kê Doanh Thu - Công ty Cổ phần Công nghệ HeyU Việt Nam`,
      text: `Tài Liệu Thống Kê Doanh Thu Cửa hàng (Thời gian ${moment(
        startTime
      ).format("HH:mm DD/MM/YYYY")} đến ${moment(endTime).format(
        "HH:mm DD/MM/YYYY"
      )})`,
      attachments: [
        {
          path: filepath,
        },
      ],
    };
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        return next(error);
      } else {
        next();
      }
    });
  };

  const removeFile = (next) => {
    fs.unlink(filepath, (err) => {});
    next();
  };

  async.waterfall(
    [
      checkParams,
      getMembersId,
      getStoreData,
      createExcelFile,
      sendMail,
      removeFile,
    ],
    (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err &&
        _.isError(err) &&
        (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
        });

      res.json(data || err || { code: CONSTANTS.CODE.SUCCESS });
    }
  );
};
