const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const MemberModel = require('../../../models/member');
const BusinessTypeModel = require('../../../models/businessType');
const StoreLogModel = require('../../../models/storeLog');
const UserStoreModel = require('../../../models/userStore');
const UserModel = require('../../../models/user');
const ProductModel = require('../../../models/product');
const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
  const {
    body: {
      startTime = moment().startOf('date').valueOf(),
      endTime = moment().endOf('date').valueOf(),
      supporter,
      region
    }
  } = req

  let stores = []
  let logs = []
  let rawLogs = []
  let statisticData = {}
  let businessTypeData = {}
  let actions

  const checkParams = (next) => {
    next()
  }

  const getUserStore = (next) => {
    const query = {
      createdAt: {
        $gte: startTime,
        $lte: endTime
      },
      member: {
        $exists: true
      }
    }

    if (region) {
      query.region = region
    }
    UserStoreModel
      .find(query)
      .populate('products', '_id' , {status: 1})
      .populate('businessType', 'name')
      .select('_id level products golive golink businessType')
      .lean()
      .exec((err, results) => {
        stores = results
        statisticData["Tất cả"] = stores.length
        statisticData["Đã xóa"] = stores.filter(store=>store.level === CONSTANTS.STORE_LEVEL.DELETED).length
        statisticData["> 5SP"] = stores.filter(store=>store.level === CONSTANTS.STORE_LEVEL.APPROVED && store.products && store.products.length >= 5).length
        statisticData["> 5SP Chưa Golive"] = stores.filter(store=>store.level === CONSTANTS.STORE_LEVEL.APPROVED && store.products && store.products.length >= 5 && !store.golive).length
        statisticData["> 5SP Chưa Golink"] = stores.filter(store=>store.level === CONSTANTS.STORE_LEVEL.APPROVED && store.products && store.products.length >= 5 && !store.golink).length
        statisticData["golive"] = stores.filter(store=>store.golive).length
        statisticData["golink"] = stores.filter(store=>store.golink).length
        Object.values(CONSTANTS.STORE_LEVEL).forEach((level) => {
          statisticData[tool.store_level_trans(level)] = stores.filter(store=>store && store.level === level).length
        });

        next()
      })
  }

  const getLogs = (next) => {
    let times = []
    for (let i = startTime; i < endTime; i += ms('1h')) {
      let time = {
        $gte: i,
        $lte: i+ ms('1h') < endTime ? i + ms('1h') : endTime
      }
      times.push(time)
    }

    async.mapLimit(times, 10,(time, done)=>{
      const query = {
        createdAt: time,
        store: {$in: stores.map(store=>store._id)},
        action: {$ne: 'Admin chỉnh sửa thông tin'}
      }

      if (supporter) {
        query.userId = supporter
      }

      if (region) {
        query.region = region
      }
      StoreLogModel
        .find(query)
        .select('-author -phone')
        .populate({
          path: 'store',
          populate: [{
            path: 'businessType',
            select: 'name',
          },{
            path: 'products',
            select: '_id',
          }],
          select: 'businessType golive golink level _id',
        })
        .sort('-createdAt')
        .lean()
        .exec((err, results)=>{
          results.forEach((log, i) => {
            rawLogs.push(log)
          });
          done()
        })
    },(err)=>{
      actions = Array.from(new Set(rawLogs.map(log=>log.action).filter(action=>action)))
      let storeIds = []
      rawLogs.filter(log=>log.store).forEach((log, i) => {
        if (!storeIds.includes(log.store._id)) {
          storeIds.push(log.store._id)
          logs.push(log)
        }
      });
      next();
    })
  }

  const calcTotals = next => {
    if (actions) {
      actions.forEach((action, i) => {
        statisticData[action] = Array.from(
          new Set(
            rawLogs.filter(log=>log.action === action)
            .map(log=>_.get(log, 'store._id','').toString())
            .filter(id=>id)
          )
        ).length
      });
    }
    next()
  }

  const calcBusinessType = next => {
    let businessTypes = Array.from(new Set(
      stores.map(store=>store.businessType)
        .reduce((a,b)=>_.isArray(b) ? a.concat(b) : a.concat([b]),[])
        .map(type=>JSON.stringify(type))
      )).map(strType=>JSON.parse(strType))
    businessTypeData = businessTypes
    businessTypes.forEach((type, i) => {
      let storeSelected = stores.filter(store=>store && _.isArray(store.businessType) && store.businessType.map(t=>t._id.toString()).includes(type._id.toString()))
      businessTypeData[i]['data'] = {}
      businessTypeData[i]['data']["Tất cả"] = storeSelected.length
      businessTypeData[i]['data']["> 5SP"] = storeSelected.filter(store=>store.level === CONSTANTS.STORE_LEVEL.APPROVED && store.products && store.products.length >= 5).length
      businessTypeData[i]['data']["Đã xóa"] = storeSelected.filter(store=>store.level === CONSTANTS.STORE_LEVEL.DELETED).length
      businessTypeData[i]['data']["> 5SP Chưa Golive"] = storeSelected.filter(store=>store.level === CONSTANTS.STORE_LEVEL.APPROVED && store.products && store.products.length >= 5 && !store.golive).length
      businessTypeData[i]['data']["> 5SP Chưa Golink"] = storeSelected.filter(store=>store.level === CONSTANTS.STORE_LEVEL.APPROVED && store.products && store.products.length >= 5 && !store.golink).length
      businessTypeData[i]['data']["golive"] = storeSelected.filter(store=>store && store.golive).length
      businessTypeData[i]['data']["golink"] = storeSelected.filter(store=>store && store.golink).length
      Object.values(CONSTANTS.STORE_LEVEL).forEach((level) => {
        businessTypeData[i]['data'][tool.store_level_trans(level)] = storeSelected.filter(store=>store && store.level === level).length
      });
      // if (actions) {
      //
      //   actions.forEach((action) => {
      //     businessTypeData[i]['data'][action] = Array.from(
      //         new Set(
      //           rawLogs.filter(log=>log.action === action && log.store && log.store.businessType && log.store.businessType.map(t=>t && t._id.toString()).includes(type._id.toString()))
      //           .map(log=>_.get(log, 'store._id','').toString())
      //           .filter(id=>id)
      //         )
      //       ).length
      //   });
      // }
    });
    next()
  }

  async.waterfall([
    checkParams,
    getUserStore,
    // getLogs,
    // calcTotals,
    calcBusinessType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err || {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        statisticData,
        businessTypeData
      }
    });
  })
}
