const _ = require('lodash');
const async = require('async');
const ConfigModel = require('../../../models/config');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const SubTypeModel = require('../../../models/subType');


module.exports = (req, res) => {
  const {
    body: {
      location,
      limit = 50,
      category,
      hasProduct,
      productType,
      service,
      isOpen,
      isGolive,
      distance = 5000,
      isTickbox,
      businessType
    }
  } = req
  const newDate = new Date();
  const dayNum = newDate.getDay();
  const startDate = newDate.setHours(0, 0, 0, 0);
  const duration = Date.now() - startDate;

  let query = { status: 1 };
  if (!location) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS
    });
  }

  if (category) {
    query.category = category;
  }

  if (_.isNumber(isTickbox)) {
    query.member = {$exists: isTickbox ? true : false};
  }

  if(hasProduct) {
    query.hasProduct = hasProduct;
  }

  if (businessType) {
    query.businessType = businessType
  }

  const fields = 'address description phone name location timeSettings hasProduct type image background storeNote member service';
  const findProductType = (next) => {

    if(!productType) {
      return next();
    }

    let models = ProductTypeModel;
    if (service === '5d4cea5468731c9493253bb9') {
      models = SubTypeModel;
    }

    models
      .findOne({
        _id: productType
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(result && result.storeType) {
          query['$or'] = [
            {type:result.storeType},
            {productTypes:productType}
          ]
        } else {
          if (service === '5d4cea5468731c9493253bb9') {
            query.subType = productType;
          } else {
            query.productTypes = productType
          }
        }

        next();
      })

  }

  const listStore = (next) => {
    if (_.isNumber(isGolive)) {
      query.golive  = isGolive
    }

    StoreModel
      .getNearest(location, distance, query, fields, { limit }, (err, results) => {
        if (err) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        results.map(result => {
          result.isOpen = 0;

          if (result.member) {
            result.isMerchant = 1;
            delete result.member;
          }

          result.timeSettings && result.timeSettings[dayNum] && result.timeSettings[dayNum].map(time => {
            if (duration >= time.startTime && duration <= time.endTime) {
              result.isOpen = 1;
            }
          })

          // delete result.timeSettings;
        })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      });
  }

  async.waterfall([
    findProductType,
    listStore,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
