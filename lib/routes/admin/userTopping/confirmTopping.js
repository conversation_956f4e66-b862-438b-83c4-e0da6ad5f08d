const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const Topping = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const ProductLogs = require('../../../models/productLog');
const User = require('../../../models/user');
const ToppingModel = require('../../../models/topping');
const ToppingGroupModel = require('../../../models/toppingGroup');
const StoreModel = require('../../../models/store');
const UserProductModel = require('../../../models/userProduct');
const UserToppingModel = require('../../../models/userTopping');
const UserToppingGroupModel = require('../../../models/userToppingGroup');
const UserStoreModel = require('../../../models/userStore');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
      body: {id, store, level, approveReason},
      user
    } = req

    let newToppingGroup

    const checkParams = (next) => {
      if (!id || !store || !_.isNumber(level)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        });
      }
      next()
    }

    const updatedTopping = (next) => {
      UserToppingModel
        .update({
          _id: id
        },{level},{
          new: true
        })
        .lean()
        .exec((err, result)=>{
          if (level !== 1) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật thành công!'
              }
            })
          }
          ToppingModel.findOneAndUpdate({
            _id: id
          }, result, {
            new: true,
            upsert: true,
            setDefaultsOnInsert: true
          })
          .lean()
          .exec((err, result)=>{
            next({
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật thành công!'
              }
            })
          })
        })
    }

     async.waterfall([
       checkParams,
       // updatedToppingGroup,
       updatedTopping
     ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

       err && _.isError(err) && (data = {
         code: CONSTANTS.CODE.SYSTEM_ERROR,
         message: MESSAGES.SYSTEM.ERROR
       });

       res.json(data || err);
     })
  }
