const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ToppingModel = require('../../../models/topping');
const ToppingGroupModel = require('../../../models/toppingGroup');
const StoreModel = require('../../../models/store');
const UserProductModel = require('../../../models/userProduct');
const UserToppingModel = require('../../../models/userTopping');
const UserToppingGroupModel = require('../../../models/userToppingGroup');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {name = '', store = '', id} = req.body
  let newTopping
  const checkParams = (next) => {
    if(!id || !name || !name.trim() || !store){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const findOld = (next) => {
    UserToppingGroupModel
      .findOne({
        _id: id,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }
        next();
      })
  }

  const updateUserTopping = (next) => {

    const objUpdate = {
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()),
      updatedAt: Date.now()
    }

    UserToppingGroupModel
      .update({
        _id: id
      },objUpdate,(err, result) => {
        if(err) {
          return next(err);
        }
        newTopping = result
        next()
      })
  }

  const updateTopping = next => {
    if (!newTopping || (newTopping && newTopping.level !== 1)) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật thành công!'
        }
      })
    }

    ToppingGroupModel
      .findOneAndUpdate({
        _id: newTopping._id
      },
      newTopping,
      {
        new: true,
        upsert: true,
        setDefaultsOnInsert: true
      })
      .lean()
      .exec((err, result)=>{
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật thành công!'
          }
        })
      })
  }

  async.waterfall([
    checkParams,
    findOld,
    // checkNameExists,
    updateUserTopping,
    updateTopping
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
