const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ToppingGroupModel = require('../../../models/toppingGroup');
const UserToppingGroupModel = require('../../../models/userToppingGroup');

module.exports = (req, res) => {
  const { id } = req.body
  let newTopping

  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const findOld = (next) => {
    UserToppingGroupModel
      .findOne({
        _id: id,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }

        next();
      })
  }

  const updateUserTopping = (next) => {

    const objUpdate = {
      status: 0,
      updatedAt: Date.now()
    }

    UserToppingGroupModel
      .update({
        _id: id
      }, objUpdate, (err, result) => {
        if (err) {
          return next(err);
        }

        newTopping = result

        next()
      })
  }

  const updateTopping = next => {
    if (!newTopping || (newTopping && newTopping.level !== 1)) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật thành công!'
        }
      })
    }

    ToppingGroupModel
      .findOneAndUpdate({
        _id: newTopping._id
      },
        newTopping,
        {
          new: true,
          upsert: true,
          setDefaultsOnInsert: true
        })
      .lean()
      .exec((err, result) => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật thành công!'
          }
        })
      })
  }

  async.waterfall([
    checkParams,
    findOld,
    updateUserTopping,
    updateTopping
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
