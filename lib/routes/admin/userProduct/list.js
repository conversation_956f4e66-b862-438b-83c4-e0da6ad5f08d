const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLog = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Store = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
      limit = 50,
      page = 0,
      key = '',
      sort = -1,
      region,
      level,
      status,
      store
    } = req.body

    const getList = (next) => {
      let query = {
        status: 1
      }
      if (store) query.store = store

      if (key) {
        let orArr = [
          {nameAlias: new RegExp(tool.change_alias(key), 'gi')},
          {phone: key.trim()},
        ]

        query = {
          $or: orArr
        }
      }

      if (region) query.region = region

      if (_.isNumber(level)) query.level = level

      if (_.isNumber(status)) query.isAvailable = status ? store : {$ne: store}

      UserProduct
        .find(query)
        .populate('productType')
        // .populate('store')
        .populate('product')
        .populate('topping')
        .sort({
          updatedAt: 1
        })
        .skip(page*limit)
        .limit(limit)
        .lean()
        .exec((err, results)=>{
          if (err) {
            return next({
              code: 500,
              message: {
                head: 'Thông báo',
                body: 'Hệ thống bận vui lòng thử lại sau'
              }
            });
          }

          results.map(item => {
            const isAvailable = item.isAvailable.map(value => value.toString());

            item.isAvailable = 0;
            if (isAvailable.includes(store)) {
              item.isAvailable = 1;
            }
          })

          next(null, {
            code: 200,
            data: results
          })
        })
      }

    async.waterfall([
      getList
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  }
