const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
  BusinessType
    .find({status: 1})
    .lean()
    .exec((err, results)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      res.json({
        code:200,
        data: results
      })
    })

    // const checkParams = (next) => {
    //   // if (!id || !_.isNumber(level)) {
    //   //   return next({
    //   //     code: CONSTANTS.CODE.WRONG_PARAMS,
    //   //     message: MESSAGES.SYSTEM.WRONG_PARAMS
    //   //   });
    //   // }
    //   next()
    // }

     // async.waterfall([
     //   checkParams,
     //   changeUserProductLevel,
     //   getProductTypeDefault,
     //   writeLogs,
     //   checkStore,
     //   updatedProduct,
     //   updatedProductType,
     //   updateLevelStatusProduct,
     //   updateStore
     // ], (err, data)=>{
     //   err && _.isError(err) && (data = {
     //     code: CONSTANTS.CODE.SYSTEM_ERROR,
     //     message: MESSAGES.SYSTEM.ERROR
     //   });
     //
     //   res.json(data || err);
     // })
  }
