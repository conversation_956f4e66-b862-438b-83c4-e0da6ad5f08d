const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductLogs = require('../../../models/productLog');
const UserProduct = require('../../../models/userProduct');
const Product = require('../../../models/product');

module.exports = (req, res) => {
  const {
    productId,
    reason
  } = req.body

  let newProduct

  const checkParams = next => {
    if (!productId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const updateUserProduct = next => {
    UserProduct
      .findOneAndUpdate({
        _id: productId,
        status: 1
      }, { status: 0, updatedAt: Date.now() }, { new: true })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        newProduct = result

        next()
      })
  }

  const updateProduct = next => {
    console.log('haha:newProduct', newProduct)
    if (newProduct.level !== 1) {
      return next();
    }

    Product
      .findOneAndUpdate({
        _id: productId
      }, newProduct, { new: true })
      .populate('store', 'region')
      .lean()
      .exec((err, result) => {
        console.log('haha:err', err, result)
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        newProduct = result

        next();
      })
  }

  const writeLogs = next => {
    const log = {
      author: _.get(req, 'user.fullName'),
      action: "Admin xoá sản phẩm",
      reason,
      userId: req.user._id,
      member: newProduct.member,
      region: _.get(newProduct, 'store.region', req.user.region),
      product: productId,
    };

    ProductLogs.create(log);

    next({
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Xoá sản phẩm thành công!'
      }
    })
  }

  async.waterfall([
    checkParams,
    updateUserProduct,
    updateProduct,
    writeLogs
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
