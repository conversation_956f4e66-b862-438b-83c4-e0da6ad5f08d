const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const UserProductType = require('../../../models/userProductType');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const ProductLogs = require('../../../models/productLog');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
      id,
      level,
      approveReason,
      pushNoti = false
    } = req.body
    let newProduct
    let newProductTypes = []
    const checkParams = (next) => {
      if (!id || !_.isNumber(level)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        });
      }
      next()
    }

    const changeUserProductLevel = (next) => {
      UserProduct
        .findOneAndUpdate({
          _id: id,
          level: {$ne: level}
        }, {
          level,
          approveReason
        }, {new: true})
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.FAIL,
              message: {
          			head: 'Thông báo',
          			body: 'Không thể cập nhật cửa hàng do bạn chưa thay đổi sang trạng thái mới'
          		}
            })
          }
          newProduct = result
          next()
        })
    }

    const getProductTypeDefault = next => {
      ProductType
        .find({
          _id: {$in: newProduct.productType},
          default: 1
        })
        .select("_id")
        .lean()
        .exec((err, results)=>{
          if (results.length) {
            newProductTypes = results.map(res=>res._id)
          }
          next()
        })

    }

    const writeLogs = (next) => {
      ProductLogs.findOne({
        product: id,
        level: {$in: [0,2]}
      })
        .select('store level')
        .populate('store', 'region')
        .sort("-createdAt")
        .lean()
        .exec((err, result)=>{
          let action
          if (!result) {
            action =  level === 1 ? 'Duyệt sản phẩm' : level === -1 ? "Từ chối duyệt sản phẩm" : "Từ chối cập nhật sản phẩm"
          } else {
            action =  level === 1 ? (result.level === 0 ? 'Duyệt sản phẩm' : 'Duyệt cập nhật') : level === -1 ? "Từ chối duyệt" : "Từ chối cập nhật"
          }
          const log = {
            author: _.get(req, 'user.fullName'),
            action,
            level,
            reason: approveReason,
            userId: req.user._id,
            member: newProduct.member,
            product: id,
            region: _.get(result, 'store.region', req.user.region),
          };
          ProductLogs.create(log);
          next()
        })

    }

    const checkStore = (next) => {
    UserStore
      .findOne({
        _id: newProduct.store,
      })
      .populate('businessType')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Không thể cập nhật cửa hàng do không thấy cửa hàng'
            }
          })
        }

        if (_.get(result, 'businessType.productType')) {
          if (!_.get(newProduct, 'productType.length')) newProduct.productType = []
          newProduct.productType.push(result.businessType.productType);
          newProductTypes.push(result.businessType.productType)
        }

        next();
      })
  }

    const updatedProduct = (next) => {
      if (level !== 1) {
        return next()
      }
      Product
        .findOneAndUpdate({
          _id: id
        }, newProduct, {
          new: true,
          upsert: true,
          setDefaultsOnInsert: true
        })
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.FAIL,
              message: {
          			head: 'Thông báo',
          			body: 'Không thể cập nhật cửa hàng do bạn chưa thay đổi sang trạng thái mới!'
          		}
            })
          }
          next()
        })
    }
    const updatedProductType = (next) => {
      if (level !== 1 || !newProduct || _.isEmpty(newProduct.productType)) {
        return next()
      }
      async.eachLimit(newProduct.productType, 3, (ptype, done)=>{
        UserProductType
          .findOneAndUpdate({
            _id: ptype
          },{level},{
            new: true
          })
          .lean()
          .exec((err, newPtype)=>{
            ProductType.findOneAndUpdate({
              _id: newPtype._id
            }, newPtype, {
              new: true,
              upsert: true,
              setDefaultsOnInsert: true
            })
            .lean()
            .exec((err, result)=>{
              done()
            })
          })
      }, (finish)=>{
        next()
      })
    }

    const updateLevelStatusProduct = next => {
      UserProduct.findOne({
        store: newProduct.store,
        level: {$in: [0, 2]},
        status: 1
      }).select('store level')
        .lean()
        .exec((err, result)=>{
          UserStore
            .findOneAndUpdate({_id: newProduct.store}, {levelStatusProduct: result ? 1 : 0}, {})
            .lean()
            .exec((err, res)=>{
              next()
            })
        })
    }

    const updateStore = (next) => {
      if (level !== 1) {
        return next({
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật thành công!'
          }
        })
      }
      Stores
        .findOneAndUpdate({
          _id: newProduct.store,
        }, {
          hasProduct: 1,
          $addToSet: {
            productTypes: {
              $each: newProductTypes
            },
            productSearch: {
              _id: newProduct._id,
              nameAlias: newProduct.nameAlias
            }
          }
        }, {new: true})
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Cập nhật thành công!'
              }
            })
          }
          next({
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật thành công!'
            }
          })
        })
    }

     async.waterfall([
       checkParams,
       changeUserProductLevel,
       getProductTypeDefault,
       writeLogs,
       checkStore,
       updatedProduct,
       updatedProductType,
       updateLevelStatusProduct,
       updateStore
     ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

       err && _.isError(err) && (data = {
         code: CONSTANTS.CODE.SYSTEM_ERROR,
         message: MESSAGES.SYSTEM.ERROR
       });

       res.json(data || err);
     })
  }
