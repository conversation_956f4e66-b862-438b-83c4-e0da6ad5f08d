const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const ProductLogs = require('../../../models/productLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
  const {
    product,
    name,
    unit,
    description,
    reason
  } = req.body
  let newProduct
  const checkParams = next => {
    if (!product) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next()
  }

  const updateUserProduct = next => {
    let update = {
      name,
      unit,
      description
    }

    UserProduct
      .findOneAndUpdate({
        _id: product
      }, update, {new: true})
      .lean()
      .exec((err, result)=>{
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        newProduct = result
        next()
      })
  }

  const updateProduct = next => {
    if (newProduct.level !== 1) {
      return next();
    }
    Product
      .findOneAndUpdate({
        _id: product
      }, newProduct, {new: true})
      .populate('store', 'region')
      .lean()
      .exec((err, result)=>{
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        newProduct = result
        next();
      })
  }

  const writeLogs = next => {
    const log = {
      author: _.get(req, 'user.fullName'),
      action: "Admin chỉnh sửa thông tin",
      reason,
      userId: req.user._id,
      member: newProduct.member,
      region: _.get(newProduct, 'store.region', req.user.region),
      product,
    };
    ProductLogs.create(log);
    next({
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật thành công!'
      }
    })
  }

  async.waterfall([
    checkParams,
    updateUserProduct,
    updateProduct,
    writeLogs
  ], (err, data)=>{
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
