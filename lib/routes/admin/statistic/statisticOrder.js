const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const nodemailer = require('nodemailer');
const Excel = require('exceljs');
const moment = require('moment');

module.exports = (req, res) => {
  const limit = 'infinity';
  let startDate = _.get(req, 'body.startDate', 0);
  const endDate = _.get(req, 'body.endDate', Date.now());
  const region = _.get(req, 'body.region', null);
  const receiver = _.get(req, 'body.receiver', '');
  const storeIds = _.get(req, 'body.storeIds', []);
  let stores = [];
  let transactionRecord = [];
  let statisticData;
  let date = new Date();
  let firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  let lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

  const checkParams = (next) => {
    if (!startDate) {
      startDate = firstDay.getTime();
    }

    if (!storeIds || !storeIds.length || !startDate || !receiver) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const listStore = (next) => {
    StoreModel
      .find({
        _id: {$in: storeIds}
      }, 'name code')
      .lean()
      .exec((err, results) => {
        if (err || !results || !results.length) {
          return next(err || new Error('Store not found'));
        }

        stores = results;

        next();
      })
  }

  const listOrderTransaction = (next) => {
    listHandle({
      body: {
        limit, startDate, endDate, type, region
      }
    }, {
      json: (data) => {
        if (data.code !== CONSTANT.CODE.SUCCESS) {
          return next(data);
        }

        transactionRecord = data.data.transactions;

        next();
      }
    })
  }
  const statistic = (next) => {
    statisticHandle({
      body: {
        startDate, endDate, type, region
      }
    }, {
      json: (data) => {
        if (data.code !== CONSTANT.CODE.SUCCESS) {
          return next(data);
        }

        statisticData = data.data;

        next();
      }
    })
  }

  const exportExcel = (next) => {
    let records = [];
    transactionRecord.forEach((record) => {
      records.push({
        'Mã đơn hàng': record.idOrder ? record.idOrder.toString() : "",
        'Mã Shipper': record.shipper.toString(),
        'Tiền dịch vụ(₫)': record.amount,
        'Số tiền lúc đầu(₫)': record.initialCoints,
        'Số tiền còn lại(₫)': record.finalCoints,
        'Coints L2 đầu': record.initialCointShop,
        'Coints L2 còn lại(₫)': record.finalCointShop,
        'Nội dung': record.description,
        'Ngày giao dịch': moment(record.createdAt).format('HH:mm:ss  DD/MM/YYYY'),
        'Khu vực': record.region
      })
    })



    var workbook = new Excel.Workbook();
    var sheet = workbook.addWorksheet('1');
    sheet.addRow().values = ['Công ty Cổ phần Công nghệ HeyU Việt Nam'];
    sheet.addRow().values = [`Tài Liệu Thống Kê Phí dịch vụ đơn hàng( Thời gian ${moment(startDate).format('HH:mm:ss DD/MM/YYYY')} đến ${moment(endDate).format('HH:mm:ss DD/MM/YYYY')} )`];
    if (records[0]) {
      sheet.addRow().values = Object.keys(records[0]);
    }
    records.forEach(function (item) {
      var valueArray = [];
      valueArray = _.values(item);
      sheet.addRow().values = valueArray;
    })
    recordSize = records.length;

    sheet.mergeCells('A1:G1');
    sheet.mergeCells('A2:G2');
    sheet.getCell('A1').alignment = { vertical: 'center', horizontal: 'center' };
    sheet.getCell('A1').font = {
      size: 18,
      bold: true
    };
    sheet.getCell('A2').font = {
      size: 16,
      bold: true
    };

    sheet.getColumn('A').width = 28;
    sheet.getColumn('B').width = 28;
    sheet.getColumn('C').width = 18;
    sheet.getColumn('D').width = 18;
    sheet.getColumn('E').width = 18;
    sheet.getColumn('F').width = 25;
    sheet.getColumn('G').width = 20;
    sheet.getColumn('J').width = 24;
    sheet.getColumn('K').width = 15;
    sheet.getColumn('L').width = 20;

    sheet.getRow('3').height = 20;

    for (let i = 1; i <= recordSize + 1; i++) {
      const cellNameLeft = `A${i + 2}`
      const cellNameRight = `G${i + 2}`
      sheet.getCell(cellNameLeft).border = {
        left: { style: 'thick' }
      };
      sheet.getCell(cellNameRight).border = {
        right: { style: 'thick' }
      };
    }
    ['B3', 'C3', 'D3', 'E3', 'F3', 'G3'].map(key => {
      sheet.getCell(key).border = {
        top: { style: 'thick' },
        bottom: { style: 'thin' }
      };
      sheet.getCell(key).alignment = { vertical: 'middle', horizontal: 'center' };
      sheet.getCell(key).font = {
        size: 12,
        bold: true
      };
    });
    [`B${recordSize + 3}`, `C${recordSize + 3}`, `D${recordSize + 3}`, `E${recordSize + 3}`, `F${recordSize + 3}`, `G${recordSize + 3}`].map(key => {
      sheet.getCell(key).border = {
        bottom: { style: 'thick' }
      };
    });
    sheet.getCell('A3').border = {
      top: { style: 'thick' },
      left: { style: 'thick' },
      bottom: { style: 'thin' }
    };
    sheet.getCell('A3').alignment = { vertical: 'center', horizontal: 'center' };
    sheet.getCell('A3').font = {
      size: 12,
      bold: true
    };
    sheet.getCell('G3').border = {
      top: { style: 'thick' },
      right: { style: 'thick' },
      bottom: { style: 'thin' }
    };
    sheet.getCell(`A${recordSize + 3}`).border = {
      left: { style: 'thick' },
      bottom: { style: 'thick' }
    };
    sheet.getCell(`G${recordSize + 3}`).border = {
      right: { style: 'thick' },
      bottom: { style: 'thick' }
    };

    sheet.getCell('A2').alignment = { vertical: 'center', horizontal: 'center' };


    ['J6', 'J7'].map(key => {
      sheet.getCell(key).border = {
        left: { style: 'thin' }
      };
      sheet.getCell(key).font = {
        size: 12,
        bold: true
      };
    });
    ['L6', 'L7'].map(key => {
      sheet.getCell(key).border = {
        right: { style: 'thin' }
      };
    });
    //sheet.mergeCells('I5:G2');
    workbook.xlsx.writeFile('report.xlsx').then(function () {
      next();
    })
  }

  const sendEmail = (next) => {
    var transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: config.mail.sender,
        pass: config.mail.password
      }
    });
    var mailOptions = {
      from: config.mail.sender,
      to: receiver,
      subject: 'Công ty Cổ phần Công nghệ HeyU Việt Nam Thống kê đơn hàng',
      text: `Thống kê đơn hàng của đối tác Five Star thời gian ${moment(startDate).format('HH:mm:ss DD/MM/YYYY')} đến ${moment(endDate).format('HH:mm:ss DD/MM/YYYY')}`,
      attachments: [
        {
          path: ABSPATH + '/report.xlsx'
        }
      ]
    };
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        return next(error);
      } else {
        next();
      }
    });
  }
  async.waterfall([
    checkParams,
    listOrderTransaction,
    // statistic,
    exportExcel,
    sendEmail
  ], (err, data) => {
    err && _.isError(err) && (data = {
      code: CONSTANT.CODE.SYSTEM_ERROR
    });

    res.json({
      code: 200
    } || err);
  })
}
