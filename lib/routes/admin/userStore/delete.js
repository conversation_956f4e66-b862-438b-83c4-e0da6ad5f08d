const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const Staff = require('../../../models/staff');
const User = require('../../../models/user');
const PushNotifyManager = require('../../../job/pushNotify');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {

    const {
      store,
      reason,
    } = req.body
    let newStore
    const checkParams = next => {
      if (!store) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        })
      }
      next()
    }

    const updateUserStore = next => {
      let update = {
        golink: 0,
        golive: 0,
        level: CONSTANTS.STORE_LEVEL.DELETED,
        member: store
      }

      UserStore
        .findOneAndUpdate({
          _id: store
        }, update, {new: true})
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err||{
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Không thể cập nhật cửa hàng'
              }
            })
          }
          newStore = result

          if (result.focus) {
            UserStore
              .update({
                member: result.member,
                level: {$ne: CONSTANTS.STORE_LEVEL.DELETED}
              }, {focus: 1})
              .exec((err, result)=>{
                if (err) {
                  return next(err)
                }

                next()
              })
          } else {
            next();
          }
        })
    }

    const updateStore = next => {
      if (![
        CONSTANTS.STORE_LEVEL.APPROVED,
        CONSTANTS.STORE_LEVEL.DELETED,
      ].includes(newStore.level)) {
        return next();
      }

      Stores
        .findOneAndUpdate({
          _id: store
        }, newStore, {new: true})
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.FAIL,
              message: {
          			head: 'Thông báo',
          			body: 'Không thể cập nhật cửa hàng trên app'
          		}
            })
          }
          next();
        })
    }

    const updateStaffs = next => {
      if (_.isEmpty(newStore)) {
        return next();
      }

      Staff.update({ store: newStore._id, status: 1 }, { status: 0 }, { multi: true }, (err) => {
        next();
      })
    }

    const writeLogs = next => {
      const log = {
        author: _.get(req, 'user.fullName'),
        phone: newStore.phone,
        action: "Admin xoá cửa hàng",
        reason,
        userId: req.user._id,
        member: newStore.member,
        region: newStore.region,
        store,
      };
      StoreLogs.create(log);
      next({
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật thành công!'
        }
      })
    }

    async.waterfall([
      checkParams,
      updateUserStore,
      updateStore,
      updateStaffs,
      writeLogs,
    ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  }
