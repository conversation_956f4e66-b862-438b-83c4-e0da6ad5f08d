const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {

    const {
      id,
      adminNote,
      needAdminContat
    } = req.body

    const checkParams = (next) => {
      if (!id) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        });
      }
      next()
    }

    const noteStore = next => {
      UserStore.findOneAndUpdate({
        _id: id
      }, {adminNote, needAdminContat})
        .lean()
        .exec((err, result)=>{
          next(err, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
        			head: 'Thông báo',
        			body: 'Cập nhật thành công!'
        		}
          });
        })
    }

    async.waterfall([
      checkParams,
      noteStore
    ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  }
