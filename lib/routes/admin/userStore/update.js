const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');
const PushNotifyManager = require('../../../job/pushNotify');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {

    const {
      store,
      name,
      address,
      location,
      subAddress,
      businessType,
      subType,
      description,
      reason,
      status,
      service,
      paymentMethod
    } = req.body
    let newStore
    const checkParams = next => {
      if (!store) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        })
      }
      next()
    }

    const updateUserStore = next => {
      let update = {
      }
      if (name) {
        update.name = name
      }
      if (address) {
        update.address = address
      }
      if (location) {
        update.location = {
          type : "Point",
          coordinates : [
            location.lng,
            location.lat
          ]
        }
      }
      if (subAddress) {
        update.subAddress = subAddress
      }
      if (description) {
        update.description = description
      }
      if (businessType) {
        update.businessType = businessType
      }
      if (subType) {
        update.subType = subType
      }
      if (service) {
        update.service = service
      }
      if (_.isNumber(status)) {
        update.status = status
      }
      UserStore
        .findOneAndUpdate({
          _id: store
        }, update, {new: true})
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err||{
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Không thể cập nhật cửa hàng'
              }
            })
          }
          newStore = result
          next()
        })
    }

    const updateStore = next => {
      if (newStore.level !== 1 && !_.isNumber(status)) {
        return next();
      }

      newStore.timeSettings = newStore.workingTime;
      if (newStore.workingTime && newStore.workingTime.from && newStore.workingTime.to) {
        newStore.timeSettings = {
          "0" : [
              {
                  "endTime" : _.get(newStore, 'workingTime.to',79200000),
                  "startTime" : _.get(newStore, 'workingTime.from',28800000)
              }
          ],
          "1" : [
              {
                  "endTime" : _.get(newStore, 'workingTime.to',79200000),
                  "startTime" : _.get(newStore, 'workingTime.from',28800000)
              }
          ],
          "2" : [
              {
                  "endTime" : _.get(newStore, 'workingTime.to',79200000),
                  "startTime" : _.get(newStore, 'workingTime.from',28800000)
              }
          ],
          "3" : [
              {
                  "endTime" : _.get(newStore, 'workingTime.to',79200000),
                  "startTime" : _.get(newStore, 'workingTime.from',28800000)
              }
          ],
          "4" : [
              {
                  "endTime" : _.get(newStore, 'workingTime.to',79200000),
                  "startTime" : _.get(newStore, 'workingTime.from',28800000)
              }
          ],
          "5" : [
              {
                  "endTime" : _.get(newStore, 'workingTime.to',79200000),
                  "startTime" : _.get(newStore, 'workingTime.from',28800000)
              }
          ],
          "6" : [
              {
                  "endTime" : _.get(newStore, 'workingTime.to',79200000),
                  "startTime" : _.get(newStore, 'workingTime.from',28800000)
              }
          ]
        }
      }

      if (paymentMethod && !_.isEmpty(paymentMethod)) {
        newStore.paymentMethod = paymentMethod
      }

      Stores
        .findOneAndUpdate({
          _id: store
        }, newStore, {new: true})
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.FAIL,
              message: {
          			head: 'Thông báo',
          			body: 'Không thể cập nhật cửa hàng trên app'
          		}
            })
          }
          next();
        })
    }

    const pushNoti = next => {
      PushNotifyManager.sendToMember(newStore.member, '', ``, { link: '', extras: { } },'store_update', 'tickbox');
      next()
    }

    const writeLogs = next => {
      const log = {
        author: _.get(req, 'user.fullName'),
        phone: newStore.phone,
        action: status === 1 ? "Admin mở gian hàng" : status === 0 ? "Admin đóng gian hàng"  : "Admin chỉnh sửa thông tin",
        reason,
        userId: req.user._id,
        member: newStore.member,
        region: newStore.region,
        store,
      };
      StoreLogs.create(log);
      next({
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật thành công!'
        }
      })
    }

    async.waterfall([
      checkParams,
      updateUserStore,
      updateStore,
      pushNoti,
      writeLogs,
    ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  }
