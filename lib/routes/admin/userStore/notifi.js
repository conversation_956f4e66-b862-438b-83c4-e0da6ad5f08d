const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const _ = require('lodash')
const async = require('async')
const PushNotifyManager = require('../../../job/pushNotify');

module.exports = (req, res) => {
  const {
    member,
    title,
    message
  } = req.body

  const checkParams = (next) => {
    if (!member || !title || !message) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    next()
  }

  const pushNotification = (next) => {
    PushNotifyManager.sendToMember(
        member,
        title,
        message,
        {
          extras: {
          },
          link: "SBHManageProductScreen"
        },
        'product_update',
        'tickbox'
      )
        .then((result) => {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
        			head: 'Thông báo',
        			body: 'Thông báo thành công!'
        		}
          });
        })
        .catch((err) => {
          return next(err)
        })
  }

  async.waterfall([
    checkParams,
    pushNotification
  ], (err, data)=>{
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })

}
