const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const Topping = require('../../../models/topping');
const ToppingGroup = require('../../../models/toppingGroup');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');

const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const UserProductType = require('../../../models/userProductType');
const UserTopping = require('../../../models/userTopping');
const UserToppingGroup = require('../../../models/userToppingGroup');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
      id,
      level,
      approveReason
    } = req.body
    let newStore
    const checkParams = (next) => {
      if (!id || !_.isNumber(level)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        });
      }
      next()
    }

    const updateUserStore = (next) => {
      UserStore
        .findOneAndUpdate({
          _id: id,
          level: {$ne: level}
        }, {
          level,
          approveReason
        }, {new: true})
        .populate("businessType")
        .lean()
        .exec((err, result)=>{
          if (err || !result) {
            return next(err || {
              code: CONSTANTS.CODE.FAIL,
              message: {
          			head: 'Thông báo',
          			body: 'Không thể cập nhật cửa hàng do bạn chưa thay đổi sang trạng thái mới'
          		}
            })
          }
          newStore = result
          if(newStore.region === 'vietnam:laocai') {
            newStore.paymentMethod = ["cash"]
          }
          next()
        })
    }

    const writeLogs = (next) => {
      StoreLogs.findOne({
        store: id,
        level: {$in: [0,2]}
      })
        .sort("-createdAt")
        .lean()
        .exec((err, result)=>{
          let action
          if (!result) {
            action =  level === 1 ? 'Duyệt cửa hàng' : level === -1 ? "Từ chối duyệt" : "Từ chối cập nhật"
          } else {
            action =  level === 1 ? (result.level === 0 ? 'Duyệt cửa hàng' : 'Duyệt cập nhật') : level === -1 ? "Từ chối duyệt" : "Từ chối cập nhật"
          }
          const log = {
            author: _.get(req, 'user.fullName'),
            phone: newStore.phone,
            action,
            level,
            reason: approveReason,
            userId: req.user._id,
            member: newStore.member,
            region: newStore.region,
            store: id,
          };
          StoreLogs.create(log);
          next()
        })

    }

    const checkProduct = (next) => {
      if (level !== 1) {
        return next()
      }
      UserProduct.find({
        store: newStore._id
      })
        .lean()
        .exec((err, results)=>{
          if (results.length) {
            newStore.hasProduct = 1
          }
          next()
        })
    }

    const updateProductType  = (next) => {
      if (!newStore.hasProduct || !newStore.type) {
        return next();
      }

      ProductType
        .update({
          member: newStore.member
        }, {
          storeType: newStore.type,
          $addToSet: {
            store: newStore._id
          }
        }, { multi: true })
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          next();
        })
    }

    const updateTopping = (next) => {
      if (!newStore.hasProduct || !newStore.type) {
        return next();
      }

      Topping
        .update({
          member: newStore.member
        }, {
          storeType: newStore.type,
          $addToSet: {
            store: newStore._id
          }
        }, { multi: true })
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          next();
        })
    }

    const updateToppingGroup = (next) => {
      if (!newStore.hasProduct || !newStore.type) {
        return next();
      }

      ToppingGroup
        .update({
          member: newStore.member
        }, {
          storeType: newStore.type,
          $addToSet: {
            store: newStore._id
          }
        }, { multi: true })
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          next();
        })
    }

    const updateProduct = (next) => {
      if (!newStore.hasProduct || !newStore.type) {
        return next();
      }

      Product
        .update({
          member: newStore.member
        }, {
          storeType: newStore.type,
          $addToSet: {
            store: newStore._id,
            isAvailable: newStore._id
          }
        }, { multi: true })
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          next();
        })
    }

    const updatedStore = (next) => {
      if (level !== 1) {
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
      			head: 'Thông báo',
      			body: 'Cập nhật thành công!'
      		}
        })
      }

      newStore.timeSettings = newStore.workingTime;
      if (newStore.workingTime && newStore.workingTime.from && newStore.workingTime.to) {
        newStore.timeSettings = {
          0: [
            {
              startTime: newStore.workingTime.from,
              endTime: newStore.workingTime.to
            }
          ],
          1: [
            {
              startTime: newStore.workingTime.from,
              endTime: newStore.workingTime.to
            }
          ],
          2: [
            {
              startTime: newStore.workingTime.from,
              endTime: newStore.workingTime.to
            }
          ],
          3: [
            {
              startTime: newStore.workingTime.from,
              endTime: newStore.workingTime.to
            }
          ],
          4: [
            {
              startTime: newStore.workingTime.from,
              endTime: newStore.workingTime.to
            }
          ],
          5: [
            {
              startTime: newStore.workingTime.from,
              endTime: newStore.workingTime.to
            }
          ],
          6: [
            {
              startTime: newStore.workingTime.from,
              endTime: newStore.workingTime.to
            }
          ],
        }
      }

      Stores
        .findOneAndUpdate({
          _id: id
        }, newStore, {
          new: true,
          upsert: true,
          setDefaultsOnInsert: true
        }, (err, result)=>{
          if (err || !result) {
            return next({
              code:300,
              message: 'Cửa hàng chưa được thay đổi trạng thái!'
            })
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
        			head: 'Thông báo',
        			body: 'Cập nhật thành công!'
        		}
          })
        })
    }
    
    const waterfallFuncs = [
      checkParams,
      updateUserStore,
      writeLogs,
      checkProduct,
      updateProductType,
      updateTopping,
      updateToppingGroup,
      updateProduct,
      updatedStore
    ]
    async.waterfall(waterfallFuncs, (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  }
