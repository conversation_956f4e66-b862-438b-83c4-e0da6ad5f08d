const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
        id,
        golink = 1,
        messageGoLink = '',
        member
      } = req.body
      let store, newStore
      const updateStore = next => {
        Stores
          .findOneAndUpdate({
            _id: id
          }, {
            golink
          }, {new: true})
          .lean()
          .exec((err, result)=>{
            store = result || {}
            next()
          })
      }

      const updateUserStore = next => {
        UserStore
          .findOneAndUpdate({
            _id: id
          }, {
            golink,
            messageGoLink
          }, {new: true})
          .lean()
          .exec((err, result)=>{
            newStore = result
            next()
          })
      }
      const writeLogs = next => {
        const log = {
          author: _.get(req, 'user.fullName'),
          phone: _.get(newStore, 'phone'),
          action: golink ? 'Admin mở Golink' : 'Admin tắt Golink',
          reason: messageGoLink,
          userId: _.get(req, 'user._id'),
          member: _.get(newStore, 'member'),
          region: _.get(newStore, 'region'),
          store,
        };
        StoreLogs.create(log);
        next(null, {
          code:200,
          message: 'Cập nhật thành công!'
        })
      }

     async.waterfall([
       updateStore,
       updateUserStore,
     ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

       err && _.isError(err) && (data = {
         code: CONSTANTS.CODE.SYSTEM_ERROR,
         message: MESSAGES.SYSTEM.ERROR
       });

       res.json(data || err);
     })
  }
