const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const StoreLogs = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Stores = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');
const PhoneTeleModel = require('../../../models/phoneTele');
const PhoneTeleLogModel = require('../../../models/phoneTeleLog');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
        id,
        golive = 1,
        messageGoLive = '',
        member
      } = req.body
      let store
      let newStore
      const getStore = next => {
        Stores
          .findOne({
            _id: id
          })
          .select('goliveAt')
          .lean()
          .exec((err, result)=>{
            store = result || {}
            next()
          })
      }
      const updateStore = next => {
        Stores
          .findOneAndUpdate({
            _id: id
          }, {
            golive,
            goliveAt: store.goliveAt || Date.now()
          }, {new: true})
          .lean()
          .exec((err, result)=>{
            if (result.goliveAt && result.golive) {
              PhoneTeleModel.findOneAndUpdate({
                member: result.member
              }, {
                tickboxStatus: 2,
                schedule:{
                  callAt: Date.now()
                },
                updatedAt: Date.now(),
                statusJob: 2
              }, {upsert: true, setDefaultsOnInsert: true, new: true})
                .populate('member', 'phone', Member)
                .lean()
                .exec((error, phoneTele)=>{
                  PhoneTeleLogModel.create({
                    phone: _.get(phoneTele,'member.phone'),
                    supporter: _.get(phoneTele,'supporter'),
                    currentActivityStatus: _.get(phoneTele,'currentActivityStatus'),
                    lastActivityStatus: _.get(phoneTele,'lastActivityStatus'),
                    departments: _.get(phoneTele,'departments'),
                    createdAt: result.goliveAt,
                    region: _.get(phoneTele,'region'),
                    type: 22,
                    tickboxStatus: 2
                  })
                })
            }
            next()
          })
      }

      const updateUserStore = next => {
        UserStore
          .findOneAndUpdate({
            _id: id
          }, {
            golive,
            messageGoLive,
            goliveAt: store.goliveAt || Date.now()
          }, {new: true})
          .lean()
          .exec((err, result)=>{
            newStore = result
            next()
          })
      }

      const writeLogs = next => {
        const log = {
          author: _.get(req, 'user.fullName'),
          phone: _.get(newStore, 'phone'),
          action: golive ? 'Admin mở Golive' : 'Admin tắt Golive',
          reason: messageGoLive,
          userId: _.get(req, 'user._id'),
          member: _.get(newStore, 'member'),
          region: _.get(newStore, 'region'),
          store,
        };
        StoreLogs.create(log);
        next(null, {
          code:200,
          message: 'Cập nhật thành công!'
        })
      }

     async.waterfall([
       getStore,
       updateStore,
       updateUserStore,
       writeLogs
     ], (err, data)=>{
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

       err && _.isError(err) && (data = {
         code: CONSTANTS.CODE.SYSTEM_ERROR,
         message: MESSAGES.SYSTEM.ERROR
       });

       res.json(data || err);
     })
  }
