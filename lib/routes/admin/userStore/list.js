const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductType = require('../../../models/productType');
const PotentialPhone = require('../../../models/potentialPhone');
const PhoneTele = require('../../../models/phoneTele');
const StoreLog = require('../../../models/storeLog');
const UserStore = require('../../../models/userStore');
const UserProduct = require('../../../models/userProduct');
const Store = require('../../../models/store');
const Product = require('../../../models/product');
const Member = require('../../../models/member');
const SubType = require('../../../models/subType');
const BusinessType = require('../../../models/businessType');
const User = require('../../../models/user');

const tool = require('../../../utils/tool');
const ms = require('ms')

module.exports = (req, res) => {
    const {
      body: {
        limit = 50,
        page = 0,
        key,
        sort = -1,
        region,
        level,
        status,
        levelStatusProduct,
        businessType,
        adminNote,
        hasProduct,
        golive,
        golink,
      }
    } = req

    let memberId
    let storeWrongStatus = []
    let data = []
    const getMemberInf = (next) => {
      if (!key.trim()) {
        return next();
      }
      Member.findOne({$or: [
        {phone: key},
        {code: key}
      ]})
      .select('_id')
      .lean()
      .exec((err, result)=>{
        memberId = result ? result._id : null
        next()
      })
    }

    const getList = (next) =>{
      let query = {
        member: {$exists: true},
        level: {$ne: CONSTANTS.STORE_LEVEL.DELETED}
      }

      if (key) {
        let orArr = [
          {nameAlias: new RegExp(tool.change_alias(key), 'gi')},
          {phone: key.trim()},
          {addressAlias: new RegExp(tool.change_alias(key), 'gi')}
        ]

        if (memberId) {
          orArr.push({member: memberId})
        }

        query = {
          $or: orArr
        }

      } else {
        // query.lastUpdatedAt = {$gte: Date.now() - ms('30d')}
      }

      if (region) {
        query.region = region
      }

      if (_.isNumber(level)) {
        query.level = level
      }

      if (_.isNumber(status)) {
        query.status = status
      }

      if (_.isNumber(hasProduct)) {
        query.hasProduct = hasProduct
      }
      if (_.isNumber(golive)) {
        query.golive = golive
      }
      if (_.isNumber(golink)) {
        query.golink = golink
      }

      if (businessType) {
        query.businessType = {$in:[businessType]}
      }

      if (_.isNumber(levelStatusProduct)) {
        query.levelStatusProduct = levelStatusProduct
      }

      if (adminNote === "1") {
        query.adminNote = {$nin: [null, '']}
      }

      if (adminNote === "2") {
        query.needAdminContat = 1
      }

      UserStore
        .find(query)
        .populate({
          path: 'phoneInf',
          populate: {
            path: 'supporter',
            model: User,
            select: 'name',
          },
          select: 'supporter',
        })
        .populate('lastLogs', 'author action', null, { sort: { 'createdAt': -1 }})
        .populate('businessType')
        .populate('subType')
        .populate({
          path: 'member',
          populate: {
            path: 'phoneInf',
            populate: {
              path: 'supporter',
              model: User,
              select: 'name',
            },
            select: 'supporter',
          },
          select: 'phone facebook',
        })
        .populate('products', 'level status', {level: {$in: [0,2]}, status: 1})
        .populate('toppingGroups', 'level status', {level: {$in: [0,2]}, status: 1})
        .populate('toppings', 'level status', {level: {$in: [0,2]}, status: 1})
        .populate('store')
        .populate({
          model: PhoneTele,
          path: 'phoneTele',
          populate: {
            model: User,
            path: 'supporter',
          }
        })
        .populate('staffs', '', {status: 1})
        .sort({
          lastUpdatedAt: 1
        })
        .skip(page*limit)
        .limit(limit)
        .lean()
        .exec((err, results)=>{
          if (err) {
            return next(err);
          }
          if (levelStatusProduct === 1) {
            storeWrongStatus = results.filter(result=>_.isEmpty(result.products)).map(item=>item._id)
            data = results.filter(result=>!_.isEmpty(result.products))
          } else {
            data = results
          }
          next()
        })
      }

      const updateWrongStatus = (next) =>{
        if (!_.isEmpty(storeWrongStatus)) {
          UserStore.update({_id: {$in: storeWrongStatus}}, {$set: {levelStatusProduct: 0}}, (err, results)=>{})
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data
        })
      }

    async.waterfall([
      getMemberInf,
      getList,
      updateWrongStatus
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  }
