const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ProductModel = require('../../../models/product');
const StoreModel = require('../../../models/store');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {
    limit = 15,
    page = 0,
    sort = 1,
    name = '',
    store,
    productType = []
  } = req.body

  let storeType

  const getStoreType = (next) => {
    StoreModel
      .findOne({
        _id: store,
        status: 1
      })
      .select('type')
      .lean()
      .exec((err, result)=>{
        if (_.get(result, 'type')) {
          storeType = _.get(result, 'type')
        }
        next()
      })
  }

  const listProductType = (next) => {

    if(!store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.NOT_FOUND_STORE
      })
    }

    let objSeach = storeType ?{
      storeType
    } : {
      store
    }

    if(name && name.trim()) {
      objSeach.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
    }

    if(productType && productType.length) {
      objSeach.productType = {
        $in:productType
      }
    }

    const skip = page*limit;
    const options = {
      limit,
      skip,
      sort: sort == 1 ? 'createdAt' : '-createdAt'
    }

    ProductModel
      .find(objSeach,"",options)
      .populate('productType')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }

        results.map(item => {
          if (item.isAvailable && typeof item.isAvailable !== 'number') {
            const isAvailable = item.isAvailable.map(value => value.toString());

            item.isAvailable = 0;
            if (isAvailable.includes(store)) {
              item.isAvailable = 1;
            }
          }
        })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    getStoreType,
    listProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
