const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const ProductModel = require('../../../models/product');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const id = _.get(req,'body.id','')
  let name = _.get(req,'body.name','');
  const price = _.get(req,'body.price',0);
  const images = _.get(req,'body.images',[]);
  const productType = _.get(req,'body.productType','');
  const store = _.get(req,'body.store','');

  const description = _.get(req,'body.description','');
  let pricePromote = _.get(req,'body.pricePromote',0);

  let oldProduct;
  const checkParams = (next) => {
    if(!id || !name || !store || !productType || !price || !images.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    if(pricePromote > price) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.PRICE_PROMOTE
      })
    }

    if(!pricePromote) {
      pricePromote = price
    }

    name = name.trim();

    next();
  }

  let storeType

  const getStoreType = (next) => {
    StoreModel
      .findOne({
        _id: store
      })
      .select('type')
      .lean()
      .exec((err, result)=>{
        if (_.get(result, 'type')) {
          storeType = _.get(result, 'type')
        }
        next()
      })
  }

  const findOld = (next) => {
    ProductModel
      .findOne({
        _id: id,
        status: 1,
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_NOT_EXISTS
          })
        }
        oldProduct = result
        next();
      })
  }

  const checkProductType = (next) => {
    if(oldProduct.productType.toString() === productType) {
      return next()
    }
    ProductTypeModel
      .count({
        productType: productType,
        store
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(!count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }
        next();
      })
  }

  const checkNameExists = (next) => {

    if(oldProduct.name.trim() === name) {
      return next();
    }

    let query = {
      name: name
    }

    if (storeType) {
      query.storeType = storeType
    } else {
      query.store = store
    }

    ProductModel
      .findOne(query)
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_EXISTS
          })
        }
        next();
      })
  }


  const update = (next) => {

    const objUpdate = {
      name,
      price,
      pricePromote,
      images,
      productType,
      description,
      store,
      nameAlias: tool.change_alias(name)
    }
    ProductModel
      .findOneAndUpdate({
        _id: id
      },objUpdate,{},(err, result) => {
        if(err) {
          return next(err);
        }

        if (result.nameAlias !== tool.change_alias(name)) {
          let query = {
            'productSearch._id': result._id
          }
          if (storeType) {
            query.type = storeType
          } else {
            query._id = store
          }

          let updateObj = {
            'productSearch.$.nameAlias': tool.change_alias(name)
          }


          StoreModel.update(query, updateObj, {multi: true})
          .lean()
          .exec((err, results)=>{
            console.log('updated');
          })
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    getStoreType,
    findOld,
    //checkProductType,
    checkNameExists,
    update
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
