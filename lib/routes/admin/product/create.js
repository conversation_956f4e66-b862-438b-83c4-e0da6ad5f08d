const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const StoreModel = require('../../../models/store');
const ProductModel = require('../../../models/product');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  let name = _.get(req,'body.name','');
  const price = _.get(req,'body.price',0);
  const images = _.get(req,'body.images',[]);
  const productType = _.get(req,'body.productType',[]);
  const store = _.get(req,'body.store','');

  const description = _.get(req,'body.description','');
  let pricePromote = _.get(req,'body.pricePromote',0);

  let storeType
  let service

  const checkParams = (next) => {
    if(!name || !store || !productType.length || !price || !images.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    if(pricePromote > price) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.PRICE_PROMOTE
      })
    }

    if(!pricePromote) {
      pricePromote = price
    }

    name = name.trim();

    next();
  }

  const checkStore = (next) => {

    StoreModel
      .findOne({
        _id: store,
        status: 1
      })
      .select('type service')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }
        if (_.get(result, 'type')) {
          storeType = _.get(result, 'type')
        }

        if (_.get(result, 'service')) {
          service = _.get(result, 'service')
        } else {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }

        next();
      })
  }

  const checkProductType = (next) => {
    ProductTypeModel
      .count({
        _id: productType,
        status: 1
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(!count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }
        next();
      })
  }

  const checkName = (next) => {
    ProductModel
      .count({
        store: store,
        productType: productType,
        name: name
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_EXISTS
          })
        }
        next();
      })
  }

  const createProduct = (next) => {
    const prodObj = {
      name,
      price,
      pricePromote,
      images,
      store,
      productType,
      description,
      status:1,
      nameAlias: tool.change_alias(name),
      service
    }
    if (storeType) {
      prodObj.storeType = storeType
    }

    ProductModel
      .create(prodObj,(err,result) => {
        if(err) {
          return next(err)
        }
        if (result) {
          let query = {}
          let updateObj = {
            hasProduct: 1,
            $addToSet:{
              productSearch: {
                _id: result._id,
                nameAlias: result.nameAlias,
              }
            }
          }
          if (storeType) {
            query = {
              type: storeType
            }
          } else {
            query = {
              _id: store
            }
          }
          StoreModel
            .update(query, updateObj, {multi: true})
            .lean()
            .exec((err, results)=>{})
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    checkStore,
    //checkProductType,
    checkName,
    createProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
