const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ServiceModel = require('../../../models/services');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {
    region = 'all'
  } = req.body

  const listService = (next) => {
    ServiceModel
      .find({
        active: 1,
        open: 1,
        $or:[{
          'region.allow':'all',
          'region.deny':{
            $ne:region
          }
        },{
          'region.allow': region
        }]
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    listService
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
