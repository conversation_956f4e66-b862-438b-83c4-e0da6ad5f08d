const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const MemberModel = require('../../../models/member');
const UserStoreModel = require('../../../models/userStore');
const TransactionLogModel = require('../../../models/transactionLog');
const ChargeMoneyLogsModel = require('../../../models/chargeMoneyLogs');
const TickboxUserModel = require('../../../models/tickboxUser');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const PushNotifyManager = require('../../../job/pushNotify');
const redisConnections = require('../../../connections/redis')
const orderHelper = require('../../../utils/order');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {
    body: {
      page = 0,
      limit = 20,
      key,
      startTime,
      endTime,
      supporter,
      region
    },
    user
  } = req
  let memberInf
  const checkParams = (next) => {
    next()
  }

  const getMemberInf = next => {
    if (!key) {
      return next();
    }
    MemberModel.findOne({
      $or: [
        {phone: key},
        {code: key},
      ]
    })
    .select('_id')
    .lean()
    .exec((err, result)=> {
      if (err || !result) {
        return next(err || {
          code: CONSTANTS.CODE.SUCCESS,
          data: []
        })
      }
      memberInf = result
      next()
    })
  }

  const listLogs = next => {
    let query = {}
    if (startTime) {
      query.createdAt = {
        $gte: startTime
      }
    }

    if (endTime) {
      query.createdAt = {
        $lte: endTime
      }
    }

    if (startTime && endTime) {
      query.createdAt = {
        $gte: startTime,
        $lte: endTime
      }
    }

    if (memberInf) {
      query.member = memberInf._id
    }

    if (supporter) {
      query.supporter = supporter
    }

    if (!key || region) {
      query.region = region ? region : user.region
    }

    ChargeMoneyLogsModel
    .find(query)
    .skip(page*limit)
    .limit(limit)
    .sort('-createdAt')
    .populate('member', 'facebook code')
    .populate('supporter', 'name fullName', TickboxUserModel)
    .lean()
    .exec((err, logs)=>{
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: logs
      })
    })
  }

  async.waterfall([
    checkParams,
    getMemberInf,
    listLogs
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }
    err && _.isError(err) &&(data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err);
  });
}
