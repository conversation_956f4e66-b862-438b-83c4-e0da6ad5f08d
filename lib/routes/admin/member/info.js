const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const MemberModel = require('../../../models/member');
const UserStoreModel = require('../../../models/userStore');
const TransactionLogModel = require('../../../models/transactionLog');
const ChargeMoneyLogsModel = require('../../../models/chargeMoneyLogs');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const PushNotifyManager = require('../../../job/pushNotify');
const redisConnections = require('../../../connections/redis')
const orderHelper = require('../../../utils/order');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const {
    body: {
      key,
    },
    user
  } = req

  const checkParams = (next) => {
    if (!key) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getMemberInf = next => {
    MemberModel.findOne({
      $or: [
        {phone: key},
        {code: key.toUpperCase()},
      ]
    })
    .select('facebook money userStore money')
    .populate('userStore', 'address image name')
    .lean()
    .exec((err, result)=> {
      if (err) {
        return next(err)
      }
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          member: result
        }
      })
    })
  }


  async.waterfall([
    checkParams,
    getMemberInf
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) &&(data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err);
  });
}
