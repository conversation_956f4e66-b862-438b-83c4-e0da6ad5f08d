const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const MemberModel = require('../../../models/member');
const UserStoreModel = require('../../../models/userStore');
const TransactionLogModel = require('../../../models/transactionLog');
const ChargeMoneyLogsModel = require('../../../models/chargeMoneyLogs');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const PushNotifyManager = require('../../../job/pushNotify');
const redisConnections = require('../../../connections/redis')
const orderHelper = require('../../../utils/order');
const tool = require('../../../utils/tool');
const unBlockStoreHandle = require('../store/unBlock')

module.exports = (req, res) => {
  const {
    body: {
      member,
      amount = 0,
      password
    },
    user
  } = req
  let storeInf
  let memberInf
  let newMemberInf
  let moneyLog
  const checkParams = (next) => {
    if (!member || !password || !amount || amount > 5000000) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (_.isArray(user.roles) && user.roles.map(rol=>rol.toString()).includes('61e8f7a055e4d540a9a04e47')) {
    return next();
    }

    next({
      code: CONSTANTS.CODE.FAIL,
      message: {
        head: 'Thông báo',
        body: "Bạn không có quyền thực hiện thao tác này"
      }
    });
  }

  const checkPassword = (next) => {
      if(!user.password) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: {
            'head': 'Thông báo',
            'body': 'Bạn chưa có mật khẩu, vui lòng liên hệ admin để được cấp mật khẩu.'
          }
        })
      }
      bcrypt.compare(password, user.password, (err, res) => {
        if(err) {
          return next(err);
        }

        if(!res) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Mật khẩu không chính xác, vui lòng thử lại. Xin cảm ơn.'
            }
          })
        }
        next();
      });
  }

  const getMemberInf = next => {
    MemberModel.findOne({
      _id: member
    })
    .lean()
    .exec((err, result)=> {
      if (err || !result) {
        return next(err || {
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: `Không tìm thấy thành viên ${member}`
          }
        })
      }
      memberInf = result
      next()
    })
  }

  const getStoreInf = next => {
    UserStoreModel.findOne({
      member
    })
    .lean()
    .exec((err, result)=>{
      if (err || !result) {
        return next(err || {
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: `Không tìm thấy cửa hàng từ thành viên này (${member})`
          }
        })
      }
      storeInf = result
      next()
    })
  }

  const increaseMoney = next => {
    MemberModel.increaseMoney(member, amount, (err, result)=>{
      if (err || !result) {
        return next(err || {
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: `Lỗi nạp tiền liên hệ kỹ thuật ${member}`
          }
        })
      }
      newMemberInf = result

      if(result && result.isBlockStore && result.money > 0) {
        unBlockStoreHandle({
          body: {
            id: member
          }
        }, {
          json: (data) => {

          }
        })
      }

      next()
    })
  }

  const createmoneyLog = next => {
    ChargeMoneyLogsModel.create({
      member,
      amount,
      initialMoney : memberInf.money,
      finalMoney : newMemberInf.money,
      supporter: user._id,
      region: storeInf.region
    }, (err, log)=>{
      moneyLog = log || {}
      next()
    })
  }

  const createTransactionLog = next => {
    let transactionLog = {
      "message" : "Nạp trực tiếp tại văn phòng",
      member,
      "data" : {
          "type" : 30,
          "gateway" : "direct",
          "bonus" : 0,
          "discount" : 0,
          amount,
          "initialCoints" : memberInf.coints,
          "finalCoints" : newMemberInf.coints,
          "initialRealMoney" : memberInf.realMoney,
          "finalRealMoney" : newMemberInf.realMoney,
          "initialRealMoneyShop" : memberInf.realMoneyShop,
          "finalRealMoneyShop" : newMemberInf.realMoneyShop,
          "initialMoney" : memberInf.money,
          "finalMoney" : newMemberInf.money,
          "idTransaction": moneyLog._id
      },
      "region": storeInf.region,
      "createdAt" : Date.now()
    }
    TransactionLogModel.create(transactionLog, (err, result) => {
      transactionLogId = result._id
      next();
    })
  }

  const pushNotify = next => {
    PushNotifyManager.sendToMember(
        member,
        `Thông báo`,
        `Bạn được nạp ${tool.formatMoney(amount)} vào tài khoản HeyU. Số dư của bạn hiện tại là ${tool.formatMoney(newMemberInf.money)}. Xin cảm ơn!`,
        { link: 'PaymentInAppScreen', extras: {} },
        'profile_update',
        'tickbox'
      )
        .then((result) => {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
        			head: 'Thông báo',
        			body: 'Nạp tiền thành công!'
        		}
          });
        })
        .catch((err) => {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
        			head: 'Thông báo',
        			body: 'Nạp tiền thành công!, Không gửi được thông báo cho thành viên'
        		}
          })
        })
  }

  async.waterfall([
    checkParams,
    checkPassword,
    getMemberInf,
    getStoreInf,
    increaseMoney,
    createmoneyLog,
    createTransactionLog,
    pushNotify
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }
    err && _.isError(err) &&(data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err);
  });
}
