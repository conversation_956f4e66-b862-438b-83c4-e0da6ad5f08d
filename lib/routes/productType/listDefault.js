const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const locationHelper = require('../../utils/location');

const ProductTypeModel = require('../../models/productType');
const BusinessTypeModel = require('../../models/businessType');
const SubTypeModel = require('../../models/subType');

module.exports = (req, res) => {
  const service = req.body.service;
  const limit = req.body.limit || 0;
  let region = req.body.regionName;
  const location = req.body.location || ''
  const userId = _.get(req, 'user.id', '');
  let businessType;

  const checkParams = (next) => {
    if(!service) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const findRegion = (next) => {
    if(!location) {
      return next();
    }


    locationHelper
      .getRegionByLatLng(location, 2, (err, regionName) => {
        if(err) {
          return next(err);
        }

        region = regionName;

        next();

      })
  }

  const getBusinessType = (next) => {
    if (service !== '5d4cea5468731c9493253bb9') {
      return next();
    }

    BusinessTypeModel
      .findOne({
        status: 1,
        service
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        businessType = result._id;

        next();
      })
  }

  const listProductType = (next) => {
    let models = ProductTypeModel;
    let query = {
      status: 1,
      default: 1,
      service,
      $or:[{
        'region.allow':'all',
        'region.deny':{
          $ne:region
        }
      },{
        'region.allow': region
      }]
    }

    if (businessType) {
      query = {
        status: 1,
        businessType
      }

      models = SubTypeModel;
    }

    models
      .find(query)
      .sort('createdAt')
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if(err || !results) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          })
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    checkParams,
    findRegion,
    getBusinessType,
    listProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
