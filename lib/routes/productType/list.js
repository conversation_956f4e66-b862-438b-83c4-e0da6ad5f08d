const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')

const ProductTypeModel = require('../../models/productType');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');

module.exports = (req, res) => {
  const storeId = req.body.storeId;
  const type = req.body.type || ''
  const limit = req.body.limit || 10;
  const page = req.body.page || 0;
  const platform = req.body.platform || '';
  let service
  const parent = req.body.parent
  let listParentLevel1 = []
  let businessType;
  let storeInf;

  const checkParams = (next) => {
    if(!storeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const checkStore = (next) => {
    StoreModel
      .findOne({
        _id: storeId
      })
      .populate('businessType')
      .lean()
      .exec((err,result) => {
        if(err) {
          return next(err)
        }

        storeInf = result;
        service = result.service
        businessType = result.businessType;

        next();
      })
  }

  const findProductTypeLevel1 = (next) => {
    if(!parent || !type || (type && type !== '614170d2a467a91f8c75df22')) {
      return next();
    }

    ProductTypeModel
      .find({
        storeType: type,
        parent
      })
      .lean()
      .exec((err,results) => {
        if(err) {
          return next(err);
        }

        results.forEach((item, i) => {
          listParentLevel1.push(item._id)
        });
        next();
      })
  }

  const listProductType = (next) => {
    const serviceActive = ['615c43506461254304f9712e', '615dd0859b619727c7c67072'];
    let query = {
      status:1
    }
    if(type && !storeInf.member) {
      query.storeType = type
      if(type === '614170d2a467a91f8c75df22') {
        query.level = 0
        if(parent) {
          query.level = 2,
          query.parent = {
            $in: listParentLevel1
          }
        }
      }
    } else if(service && service.toString() === '613ef6b37b4adc4587d630e5') {
      query.type = 'mart'
    } else if(service && serviceActive.includes(service.toString())) {
      query.service = service;
    }else{
      query.store = storeId
    }

    if (businessType && businessType.productType) {
      query = {
        status: 1,
        parent: businessType.productType
      }
    }

    console.log('haha:query', query)
    ProductTypeModel
      .find(query)
      .sort('order')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: []
          })
        }

        if (platform !== 'web') {
          results.unshift({_id: '', name: 'Tất cả'});
        }

        let count = platform !== 'web' ? 1 : 0;
        const length = results.length;
        for (let index = 0; index < results.length; index++) {
          const element = results[index];

          results[index].icon = '';

          if (element.name !== 'Tất cả') {
            ProductModel
              .findOne({ productType: element._id }, '_id')
              .lean()
              .exec((err, result) => {
                if (!result) {
                  results.splice(index, 1);
                  index--;
                }

                count++;
                if (count === length) {

                  next(null, {
                    code: CONSTANTS.CODE.SUCCESS,
                    data: results
                  })
                }
              })
          } else {
            count++;
            if (count === length) {
              next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                data: results
              })
            }
          }
        }
      })
  }

  async.waterfall([
    checkParams,
    checkStore,
    findProductTypeLevel1,
    listProductType
  ], (err, data) => {
    console.log('haha:err', err, data)
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
