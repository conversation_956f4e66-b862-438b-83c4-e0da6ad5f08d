const _ = require('lodash');
const async = require('async');
const ConfigModel = require('../../models/config');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message');
const ProductTypeModel = require('../../models/productType');
const StoreModel = require('../../models/store');
const PromoteStore = require('../../models/promoteStore');
const Member = require('../../models/member');
const tool = require('../../utils/tool');
const utils = require('../../utils/utils')

module.exports = (req, res) => {
  const id = req.body.id || ''
  const phone = _.get(req, 'body.phone', '').trim();
  const code = _.get(req, 'body.code', '').toUpperCase();
  const platform = _.get(req, 'body.platform', '');
  let userId;
  let storeInf;

  // let query = { status: 1 };

  const checkParams = (next) => {
    if (!id && !phone && !code) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }
    next();
  }

  const getMember = (next) => {
    if (!code) {
      return next();
    }

    Member
      .findOne({
        code
      }, '_id')
      .lean()
      .exec((err, member) => {
        if (err || !member) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        userId = member._id;

        next();
      })
  }

  // query[`timeSettings.${dayNum}`] = {
  //   $elemMatch: {
  //     startTime: {
  //       $lte: duration
  //     },
  //     endTime: {
  //       $gte: duration
  //     }
  //   }
  // }

  const fields = 'address description name location timeSettings hasProduct type image background storeNote member service region status totalOrders';

  const getStore = (next) => {
    let query = {
      _id: id,
      hasProduct: 1
    }

    if (phone) {
      query = {
        phone: new RegExp(`${phone}`)
      }
    }

    if (userId) {
      query = {
        member: userId
      }
    }

    if (platform === 'web') {
      query.golink = 1
    }

    StoreModel
      .findOne(query, fields)
      .lean()
      .exec((err,result) => {
        if(err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          })
        }

        if (result.member) {
          result.isMerchant = 1;
          delete result.member;
        }

        if (result.region !== 'hn' && result.region !== 'hcm') {
          result.address = tool.short_address(result.address, result.region);
        }

        const newDate = new Date();
        const dayNum = newDate.getDay();
        const startDate = newDate.setHours(0, 0, 0, 0);
        const duration = Date.now() - startDate;

        result.timeSettings && result.timeSettings[dayNum] && result.timeSettings[dayNum].map(time => {
          if (duration >= time.startTime && duration <= time.endTime) {
            result.isOpen = 1;
          }
        })

        result.totalOrders = tool.getTextTotalOrders(result.totalOrders);

        storeInf = result;

        next();
      })
  }

  const checkPromotion = (next) => {
    PromoteStore
      .find({
        store: storeInf._id,
        status: 1,
        showList: 1
      }, 'name code condition')
      .lean()
      .exec((err, results) => {
        if (results && results.length) {
          if (!storeInf.tags) {
            storeInf.tags = [];
          }

          const currentTime = Date.now();
          results.map((result) => {
            if (utils.checkRange(currentTime, result.condition.time.value)) {
              let title = result.code;
              if (result.code.includes('_')) {
                title = result.code.split('_')[0];
              }

              storeInf.tags.push({
                title,
                description: result.name
              })
            }
          })
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: storeInf
        })
      })
  }

  async.waterfall([
    checkParams,
    getMember,
    getStore,
    checkPromotion
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
