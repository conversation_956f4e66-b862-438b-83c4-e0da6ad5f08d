const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const ConfigModel = require('../../models/config');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message');
const Product = require('../../models/product');
const ProductTypeModel = require('../../models/productType');
const StoreModel = require('../../models/store');
const SubTypeModel = require('../../models/subType');
const PromoteStore = require('../../models/promoteStore');
const tool = require('../../utils/tool');
const utils = require('../../utils/utils')


module.exports = (req, res) => {
  const location = req.body.location;
  const skip = req.body.skip || 0;
  const limit = req.body.limit || 10;
  const region = req.body.regionName;
  const hasProduct = req.body.requireProduct || '';
  const newDate = new Date();
  const dayNum = newDate.getDay();
  const startDate = newDate.setHours(0, 0, 0, 0);
  const duration = Date.now() - startDate;
  const productType = req.body.productType || '';
  const queryOpening = req.body.queryOpening || 0;

  const name = req.body.name || '';
  const service = req.body.serviceId ||  ''
  let query = { service, golive: 1, status: 1 };
  let stores
  const checkParams = (next) => {
    if (!location || !service) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }
    next()
  }

  if (queryOpening) {
    query[`timeSettings.${dayNum}`] = {
      $elemMatch: {
        startTime: {
          $lte: duration
        },
        endTime: {
          $gte: duration
        }
      }
    }
  }

  if(hasProduct) {
    query.hasProduct = hasProduct;
  }

  if(name && name.trim()) {
    query['$or'] = [
      {
        'productSearch.nameAlias': new RegExp(`${tool.change_alias(name.trim())}`)
      },
      {
        nameAlias: new RegExp(`${tool.change_alias(name.trim())}`)
      }
    ]
  }


  const fields = 'address description phone name location timeSettings hasProduct type image background storeNote member status goliveAt totalOrders';

  const findProductType = (next) => {

    if(!productType) {
      return next();
    }

    let models = ProductTypeModel;
    if (service === '5d4cea5468731c9493253bb9') {
      models = SubTypeModel;
    }

    models
      .findOne({
        _id: productType
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          if(result.storeType) {
            query.type = result.storeType
          } else {
            if (service === '5d4cea5468731c9493253bb9') {
              query.subType = productType;
            } else {
              query.productTypes = productType
            }
          }
        }

        next();
      })

  }
  const listStore = (next) => {
    let distance = 5000
    if(region === 'vietnam:hungyen') {
      distance = 20000
    }
    if(region === 'vietnam:haiduong' || region === 'vietnam:bacninh' || region === 'vietnam:nghean') {
      distance = 25000
    }
    const timeTagNewStore = region === 'vietnam:nghean' ? ms('7d') : ms('2d');

    StoreModel
      .getNearest(location, distance, query, fields, { limit, skip }, (err, results) => {
        if (err) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }
        results.map(result => {
          result.isOpen = 0;

          if (result.member) {
            result.isMerchant = 1;
            delete result.member;
          }

          if (result.goliveAt < 1662397200000 && region === 'hcm') {
            result.goliveAt = 1662397200000;
          }

          if (Date.now() - result.goliveAt < timeTagNewStore) {
            result.tagImageHeader = 'https://media.heyu.asia/uploads/mobiles/2022-12-13-tagnewstore.png';
          }

          result.timeSettings && result.timeSettings[dayNum] && result.timeSettings[dayNum].map(time => {
            if (duration >= time.startTime && duration <= time.endTime) {
              result.isOpen = 1;
            }
          })

          if (region !== 'hn' && region !== 'hcm') {
            result.address = tool.short_address(result.address);
          }

          result.totalOrders = tool.getTextTotalOrders(result.totalOrders);

          // delete result.timeSettings;
          // delete result.status;
        })
        stores = results

        next()
      });
  }

  const checkPromotion = (next) => {
    if (!stores || !stores.length) {
      return next();
    }

    async.mapLimit(stores, stores.length, (store, done) => {
      PromoteStore
        .find({
          store: store._id,
          status: 1,
          showList: 1
        }, 'name code condition')
        .lean()
        .exec((err, results) => {
          if (results && results.length) {
            store.tags = [];

            const currentTime = Date.now();
            results.map((result) => {
              if (utils.checkRange(currentTime, result.condition.time.value)) {
                let title = result.code;
                if (result.code.includes('_')) {
                  title = result.code.split('_')[0];
                }

                store.tags.push({
                  title,
                  description: result.name
                })
              }
            })

            if (store.tagImageHeader === 'https://media.heyu.asia/uploads/mobiles/2022-12-13-tagnewstore.png' && store.tags.length) {
              store.tagImageHeader = '';
            }
          }

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next();
    })
  }

  const insertProduct = (next) => {
    async.mapLimit(stores,stores.length,(store, done) => {
      let queryProduct = {
        status: 1
      }

      if(store.type) {
        queryProduct.storeType = store.type
      } else {
        queryProduct.store = store._id
        queryProduct.isAvailable = store._id
      }

      if(productType && service !== '5d4cea5468731c9493253bb9') {
        queryProduct.productType = productType
      }

      if(name && name.trim()) {
        queryProduct.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
      }
      Product
        .find(queryProduct)
        .sort({
          createdAt:1
        })
        .limit(5)
        .lean()
        .exec((err, results) => {
          if(err) {
            return done(err)
          }
          let products = [];

          results.forEach((item, i) => {
            let category
            if(item.storeType && item.storeType.toString() === '614170d2a467a91f8c75df22') {
              category = item.productType[0]
            } else {
              category = item.productType && item.productType.length ? item.productType[0] : ''
            }

            products.push({
              _id: item._id,
              name: item.name,
              images: item.images,
              price: item.price,
              pricePromote: item.pricePromote,
              category,
              store: store._id
            })
          });
          store.products = products
          done()

        })
    },(err,result) => {
      if(err) {
        return next(err)
      }
      next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data: stores
      })
    })
  }

  async.waterfall([
    checkParams,
    findProductType,
    listStore,
    checkPromotion,
    insertProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
