const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../models/productType');
const UserStoreModel = require('../../models/userStore');
const BannerProduct = require('../../models/bannerProduct');
const tool = require('../../utils/tool');

module.exports = (req, res) => {

  const userId = _.get(req,'user.id','');
  const region = _.get(req,'body.regionName','');
  const platform = req.body.platform;
  const nativeVersion = req.body.nativeVersion;

  const checkParams = (next) => {
    if(!userId || !region) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const getBanners = (next) => {

    let query = {
      $or: [
        {
          'region.allow': region
        },
        {
          'region.allow': 'all',
          'region.deny': {
            $ne: region
          }
        }
      ]
    }

    query[`platform.${platform}.from`] = {$lte: Number(nativeVersion)};
    query[`platform.${platform}.to`] = {$gte: Number(nativeVersion)};
    query[`platform.${platform}.deny`] = {$ne: Number(nativeVersion)};
    BannerProduct.findOne(query,'config')
    .lean()
    .exec((err, result) => {
      if (err) {
        return next(err)
      }
      next({
        code: CONSTANTS.CODE.SUCCESS,
        data: []
      });
    })
  }

  async.waterfall([
    checkParams,
    getBanners
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
