const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const ConfigModel = require('../../models/config');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message');
const ProductTypeModel = require('../../models/productType');
const StoreModel = require('../../models/store');
const SubTypeModel = require('../../models/subType');
const PromoteStore = require('../../models/promoteStore');
const PromoteCodeModel = require('../../models/promoteCode');
const locationHelper = require('../../utils/location');
const tool = require('../../utils/tool');
const utils = require('../../utils/utils')

module.exports = (req, res) => {
  const location = req.body.location;
  const skip = req.body.skip || 0;
  const limit = req.body.limit || 10;
  let regionName = req.body.regionName;
  const category = req.body.category;
  const hasProduct = req.body.requireProduct || '';
  const productType = req.body.productType || '';
  const service = req.body.serviceId || '';
  const queryOpening = req.body.queryOpening || 0;
  const newDate = new Date();
  const dayNum = newDate.getDay();
  const startDate = newDate.setHours(0, 0, 0, 0);
  const duration = Date.now() - startDate;

  let query = {status: 1};
  if (!location) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS
    });
  }
  let stores;
  let storeIds = [];

  if (queryOpening) {
    query[`timeSettings.${dayNum}`] = {
      $elemMatch: {
        startTime: {
          $lte: duration
        },
        endTime: {
          $gte: duration
        }
      }
    }
  }

  if (category) {
    query.category = category;
  }

  if(hasProduct) {
    query.hasProduct = hasProduct;
  }

  const fields = 'address description name location timeSettings hasProduct type image background storeNote member service status goliveAt totalOrders';
  const findProductType = (next) => {

    if(!productType) {
      return next();
    }

    let models = ProductTypeModel;
    if (service === '5d4cea5468731c9493253bb9') {
      models = SubTypeModel;
    }

    models
      .findOne({
        _id: productType
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if(result && result.storeType) {
          query['$or'] = [
            {type:result.storeType},
            {productTypes:productType}
          ]
        } else {
          if (service === '5d4cea5468731c9493253bb9') {
            query.subType = productType;
          } else {
            query.productTypes = productType
          }
        }

        next();
      })

  }

  const determineRegion = (next) => {
    locationHelper
      .getRegionByLatLng(location, 2, (err, region) => {
        if (err) {
          return next(err);
        }

        regionName = region;

        next();
      })
  }

  const getPromote = (next) => {
    PromoteCodeModel
      .find({coSponsors: 1, status: 1}, 'condition.store.whiteList condition.time')
      .lean()
      .exec((err, results) => {
        if (err || !results || !results.length) {
          return next();
        }

        const currentTime = Date.now();
        results.map(result => {
          if (utils.checkRange(currentTime, result.condition.time.value)) {
            storeIds = _.union(storeIds, _.get(result, 'condition.store.whiteList', []));
          }
        })

        next();
      })
  }

  const listStore = (next) => {
    query.service = service
    query.golive  = 1
    let distance = 10000
    if(regionName.includes('vietnam:hungyen')) {
      distance = 20000
    }
    if(regionName === 'vietnam:haiduong' || regionName === 'vietnam:bacninh' || regionName === 'vietnam:nghean') {
      distance = 25000
    }

    if (regionName === 'vietnam:laocai') {
      query[`timeSettings.${dayNum}`] = {
        $elemMatch: {
          startTime: {
            $lte: duration
          },
          endTime: {
            $gte: duration
          }
        }
      }
    }
    const timeTagNewStore = regionName === 'vietnam:nghean' ? ms('7d') : ms('2d');

    StoreModel
      .getNearest(location, distance, query, fields, { limit, skip }, (err, results) => {
        if (err) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        results.map(result => {
          result.isOpen = 0;

          if (result.member) {
            result.isMerchant = 1;
            delete result.member;
          }

          if (result.goliveAt < 1662397200000 && regionName === 'hcm') {
            result.goliveAt = 1662397200000;
          }

          if (Date.now() - result.goliveAt < timeTagNewStore) {
            result.tagImageHeader = 'https://media.heyu.asia/uploads/mobiles/2022-12-13-tagnewstore.png';
          }

          if (storeIds.includes(result._id.toString())) {
            result.imageFrame = 'https://media.heyu.asia/uploads/mobiles/2022-01-29-frame-free-ship.png';
            result.tags = [{
              title: 'FREESHIP',
              description: 'Giảm 15k phí ship đơn hàng'
            }];
          }

          result.timeSettings && result.timeSettings[dayNum] && result.timeSettings[dayNum].map(time => {
            if (duration >= time.startTime && duration <= time.endTime) {
              result.isOpen = 1;
            }
          })

          if (regionName !== 'hn' && regionName !== 'hcm') {
            result.address = tool.short_address(result.address, regionName);
          }

          result.totalOrders = tool.getTextTotalOrders(result.totalOrders);

          // delete result.timeSettings;
          // delete result.status;
        })

        stores = results;

        next();
      });
  }

  const getStoreDemo = (next) => {
    if ((regionName !== 'vietnam:cantho' && regionName !== 'vietnam:danang') || skip !== 0) {
      return next();
    }

    StoreModel
      .findOne({
        _id: '61b2b04e4bdf4cb43da8c325'
      }, fields)
      .populate('businessType')
      .populate('subType', 'name')
      .lean()
      .exec((err, result) => {
        if (result) {
          result.isOpen = 0;

          if (result.member) {
            result.isMerchant = 1;
            delete result.member;
          }

          result.tagImageHeader = 'https://media.heyu.asia/uploads/mobiles/2022-12-13-tagnewstore.png';

          result.timeSettings && result.timeSettings[dayNum] && result.timeSettings[dayNum].map(time => {
            if (duration >= time.startTime && duration <= time.endTime) {
              result.isOpen = 1;
            }
          })

          // delete result.timeSettings;
          // delete result.status;

          if (regionName === 'vietnam:cantho') {
            result.address = '64 Xô Viết Nghệ Tĩnh, An Hội, Ninh Kiều, Cần Thơ';
            result.location = {
              coordinates: [105.7814943, 10.038681],
              type: 'Point'
            };
          } else if (regionName === 'vietnam:danang') {
            result.address = '291 Hải Phòng, Tân Chính, Thanh Khê, Đà Nẵng';
            result.location = {
              coordinates: [108.2067079, 16.0706964],
              type: 'Point'
            };
          }

          stores.unshift(result);
        }

        next();
      })
  }

  const checkPromotion = (next) => {
    if (!stores || !stores.length) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: []
      });
    }

    async.mapLimit(stores, stores.length, (store, done) => {
      PromoteStore
        .find({
          store: store._id,
          status: 1,
          showList: 1
        }, 'name code condition')
        .lean()
        .exec((err, results) => {
          if (results && results.length) {
            if (!store.tags) {
              store.tags = [];
            }

            const currentTime = Date.now();
            results.map((result) => {
              if (utils.checkRange(currentTime, result.condition.time.value)) {
                let title = result.code;
                if (result.code.includes('_')) {
                  title = result.code.split('_')[0];
                }

                store.tags.push({
                  title,
                  description: result.name
                })
              }
            })

            if (store.tagImageHeader === 'https://media.heyu.asia/uploads/mobiles/2022-12-13-tagnewstore.png' && store.tags.length) {
              store.tagImageHeader = '';
            }
          }

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      stores.sort((a, b) => {
        if (a.isOpen > b.isOpen) return -1
      });

      const data = {
        code: CONSTANTS.CODE.SUCCESS,
        data: stores
      }

      // if ((regionName === 'vietnam:haiphong') && service === '5d4cea5468731c9493253bb9' && skip === 0 && !productType) {
      //   data.message = MESSAGES.ORDER.STORE_DEMO
      // }

      next(null, data)
    })
  }

  async.waterfall([
    findProductType,
    determineRegion,
    getPromote,
    listStore,
    // getStoreDemo,
    checkPromotion
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
