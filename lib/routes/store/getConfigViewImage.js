const _ = require('lodash');
const async = require('async');
const ConfigModel = require('../../models/config');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message');
const ProductTypeModel = require('../../models/productType');


module.exports = (req, res) => {
  const regionName = _.get(req, 'body.regionName', '');

  const checkParams = (next) => {
    if (!regionName) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getConfig = (next) => {
    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.VIEW_IMAGE, regionName, (err, result) => {
        if (err) {
          return next(err);
        }

        if (!result || !result.config) {
          return next(null, {
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result.config
        })
      })
  }

  async.waterfall([
    checkParams,
    getConfig
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
