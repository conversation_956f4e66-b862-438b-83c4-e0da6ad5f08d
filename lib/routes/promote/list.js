const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const PromoteStoreModel = require('../../models/promoteStore');
const utils = require('../../utils/utils')
const promoteUtil = require('../../utils/promote')

module.exports = (req, res) => {
  const storeId = _.get(req, 'body.storeId', '');
  const userId = _.get(req, 'user.id', '');
  let promotes = [];

  const checkParams = (next) => {
    if (!storeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const listPromote = (next) => {
    PromoteStoreModel
      .find({
        store: storeId,
        status: 1,
        showList: 1
      }, '-status -createdAt -updatedAt -showList')
      .sort('-createdAt')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        promotes = results;

        next();
      })
  }

  const checkDate = (next) => {
    const currentTime = Date.now();

    for (let i = 0; i < promotes.length; i++) {
      if (!utils.checkRange(currentTime, promotes[i].condition.time.value)) {
        promotes.splice(i, 1);
        i--;
      }
    }

    next();
  }

  const checkUsed = (next) => {
    let promoteAvailable = [];

    async.eachLimit(promotes, 10, (promoteInf, done) => {
      const condition = promoteInf.condition || {};

      promoteUtil.checkTotalOrderUse(userId, promoteInf._id, condition, (err, data) => {
        if (err) {
          return done(err);
        }

        if (data.valid) {
          promoteInf.numberOfUses = data.numberOfUses;
          promoteAvailable.push(promoteInf);
        }

        done();
      })
    }, (err) => {
      if (err) {
        return next(err);
      }

      promotes = promoteAvailable;

      next();
    })
  }

  const getPromoteOrderDescription = (next) => {
    promotes.forEach((promoteInf) => {
      if (!promoteInf.icon) {
        promoteInf.icon = 'https://media.heyu.asia/uploads/mobiles/2021-12-15-icon-promote.png';
      }

      promoteInf.description = utils.getDescriptionPromote(promoteInf);
      if (promoteInf.numberOfUses) {
        promoteInf.description += `\n- Số lần sử dụng còn lại: ${promoteInf.numberOfUses}`
      }

      if (promoteInf.condition && promoteInf.condition.time && promoteInf.condition.time.value && promoteInf.condition.time.value.to) {
        promoteInf.expireTime = promoteInf.condition.time.value.to
      }

      if (promoteInf.condition.time.value.to) {
        promoteInf.description += `\n- HSD: ${moment(promoteInf.expireTime).format('H:mm - DD/MM/YYYY')}`
      }

      if (promoteInf.strategy && promoteInf.strategy.minimumPrice) {
        promoteInf.description += `\n- Giá trị đơn tối thiểu: ${promoteInf.strategy.minimumPrice.toLocaleString().replace(/,/g, ".")}₫`
      }

      promoteInf.description += `\n- Nhấp vào đây để sử dụng.`
      promoteInf.notification = promoteInf.condition && promoteInf.condition.notification ? promoteInf.condition.notification : ''

      _.unset(promoteInf, 'condition');
      _.unset(promoteInf, 'strategy');
      _.unset(promoteInf, 'expireTime');
    })

    promotes.sort((a, b) => {
      return b.createdAt - a.createdAt
    })

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: promotes
    })
  }

  async.waterfall([
    checkParams,
    listPromote,
    checkDate,
    checkUsed,
    getPromoteOrderDescription
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
