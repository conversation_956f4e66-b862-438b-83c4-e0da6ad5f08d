const async = require('async');
const _ = require('lodash');
const PromoteStoreModel = require('../../models/promoteStore')
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message')
const utils = require('../../utils/utils')
const promoteUtil = require('../../utils/promote')
const rp = require('request-promise')
const config = require('config')


module.exports = (req, res) => {
  // userId, distance, location, orderType, code, money...
  const userId = req.user.id;
  let code = req.body.code.trim().toUpperCase();
  let promoteInf, condition, orderInf;

  const checkParams = (next) => {
    if (!req.body.code || !req.body.money || !req.body.storeId) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        msg: MESSAGES.PROMOTE.ERROR
      })
    }

    next();
  }

  const getPromoteInf = (next) => {
    PromoteStoreModel
      .findOne({
        code,
        store: req.body.storeId
      })
      .sort('-createdAt')
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            msg: MESSAGES.PROMOTE.NOT_EXISTS
          })
        }

        if (!result.status) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            msg: MESSAGES.PROMOTE.NOT_ACTIVE
          })
        }

        promoteInf = result;
        condition = result.condition;

        next();
      })
  }

  const checkDate = (next) => {
    const currentTime = Date.now();

    const valid = utils.checkRange(currentTime, condition.time.value);

    if (!valid) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        msg: utils.getMessageFromTimeRange(condition.time.value)
      })
    }

    next();
  }

  const checkMinimumPrice = (next) => {
    if (promoteInf.strategy && promoteInf.strategy.minimumPrice && req.body.money < promoteInf.strategy.minimumPrice) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        msg: 'Bạn chưa đạt giá trị đơn hàng tối thiểu để sử dụng mã'
      })
    }

    next();
  }

  const generateOrderInf = (next) => {
    orderInf = {
      money: req.body.money
    }

    next();
  }


  const checkUsed = (next) => {
    promoteUtil.checkTotalOrderUse(userId, promoteInf._id, condition, (err, data) => {
      if (err) {
        return next(err);
      }

      if (!data.valid) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          msg: data.message
        })
      }

      next();
    })
  }

  const calculateDiscount = (next) => {
    promoteUtil.calculateDiscount(orderInf, promoteInf, (err, discount) => {
      if (err) {
        return next(err);
      }

      let data = {
        id: promoteInf._id,
        discountDeposit: discount
      }

      if (promoteInf.strategy && promoteInf.strategy.cashBackPointRate) {
        data.cashBackPointRate = promoteInf.strategy.cashBackPointRate
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data
      })
    })
  }

  async.waterfall([
    checkParams,
    getPromoteInf,
    checkDate,
    checkMinimumPrice,
    generateOrderInf,
    checkUsed,
    calculateDiscount
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      msg: "Đã có lỗi xảy ra vui lòng thử lại"
    });

    res.json(data || err);
  });
}
