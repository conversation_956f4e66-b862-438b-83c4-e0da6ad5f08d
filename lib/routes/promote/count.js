const async = require('async');
const _ = require('lodash');
const MemberModel = require('../../models/member')
const OrderTypeModel = require('../../models/orderType')
const OrderSystemModel = require('../../models/orderSystem')
// const PromoteCodeModel = require('../../models/promotecode')
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message')
const utils = require('../../utils/utils')
const promoteUtil = require('../../utils/promote')
const listHandle = require('./list');
const ms = require('ms')

module.exports = (req, res) => {
  const countPromote = (next) => {
    let count = 0;

    listHandle(req, {
      json: (data) => {
        if(data.code === CONSTANTS.CODE.SUCCESS) {
          count = data.data.length;
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: count
        })
      }
    })
  }

  async.waterfall([
    countPromote
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
