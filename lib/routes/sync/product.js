const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../models/productType');
const ProductModel = require('../../models/product');
const StoreModel = require('../../models/store');
const VinmartStore = require('../../models/vinmartStore');

const ProductPhamacy = require('../../models/productPhamacy');
const VinmartMenu = require('../../models/vinmartMenu');

const PharmacityCategory = require('../../models/pharmacityCategory');
const tool = require('../../utils/tool');

module.exports = (req, res) => {

  let products

  const findPharma = (next) => {
    VinmartMenu
      .find({})
      .lean()
      .sort('createdAt')
      .skip(req.body.skip)
      .limit(10000)
      .exec((err, results) => {
        products = results
        next();

      })
  }

  const objProductType = {
    "613ecbac7b4adc4587d63092":["6142ce07efd48c3dda53a639"],
    "613ecbb47b4adc4587d63097":["6142ce07efd48c3dda53a63a"],
    "613ecbb67b4adc4587d63098":["6142ce07efd48c3dda53a63b"],
    "613ecdfa7b4adc4587d630a6":["6142ce07efd48c3dda53a63c"],
    "613ecbb07b4adc4587d63095":["6142ce07efd48c3dda53a638"],
    "613ecbae7b4adc4587d63093":["6142ce07efd48c3dda53a637"],
    "613ecbac7b4adc4587d63092":["6142ce07efd48c3dda53a636"],
    "613ecbaa7b4adc4587d63091":["6142ce07efd48c3dda53a635"]
  }

  const sync = (next) => {
    let i=0

    async.eachSeries(products,(product, done) => {
      // ProductModel.update({
      //   'ref.id': product.id
      // },{
      //   description: product.description
      // },done)
      // // let productTypes = [];
      // // product.productTypes.forEach((item, i) => {
      // //   productTypes.push(item._id)
      // // });
      // //
      const img = 'https://media.heyu.asia/uploads/vinmart/'+product.id+'.jpg'
      let name = product.name;
      let price = product.price;
      let pricePromote = product.salePrice;
      if(product.uoms && product.uoms.length) {
        const uom = product.uoms[0];
        if(uom.price) {
          price = uom.price
        }
        if(uom.salePrice) {
          pricePromote = uom.salePrice
        }
        if(uom.variants && uom.variants.length) {
          name = name+` (${uom.variants[0].label})`
        } else {
          name = name+` (${uom.uomName})`
        }
      }


      let idStoreVinmart,idStore, idProduct;

      const findStoreVinmart = (cb) => {
        VinmartStore
          .findOne({
            _id:product.store
          })
          .lean()
          .exec((err,result) => {
            if(err) {
              return cb(err)
            }
            idStoreVinmart = result.id
            cb();
          })
      }

      const findStore = (cb) => {
        StoreModel
          .findOne({
            'ref.id': idStoreVinmart
          })
          .lean()
          .exec((err,result) => {
            if(err) {
              return cb(err)
            }
            if(!result || !result._id) {
              return cb('errr')
            }
            idStore = result._id
            cb();
          })
      }
      const createProduct = (cb) => {
        ProductModel
          .create({
            name: name,
            nameAlias: tool.change_alias(product.name.trim()),
            description: product.content,
            price,
            pricePromote,
            isAvailable: idStore,
            ref: {
              id: product.id,
              refName: 'Vinmart'
            },
            type: 'mart',
            images: [img],
            store: idStore,
            productType:objProductType[product.slug],
            status:1
          },(err,result) => {
            if(err) {
              return cb(err)
            }
            idProduct = result._id
            cb()
          })
      }

      const syncStore = (cb) => {
        StoreModel
          .update({
            _id: idStore
          },{
            $addToSet:{
              productSearch:{
                _id: idProduct,
                nameAlias: tool.change_alias(product.name.trim())
              }
            }
          },(err,result) => {
            if(err) {
              return cb(err)
            }
            i++
            console.log(i)
            cb()
          })
      }
      async.waterfall([
        findStoreVinmart,
        findStore,
        createProduct,
        syncStore
      ],(err,result) => {
        done()
      })
    },(err,result) => {
      console.log('ahihi finish');
      if(err) {
        return next(err)
      }
      next(null,{
        code:200
      })
    })
  }

  async.waterfall([
    findPharma,
    sync
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
