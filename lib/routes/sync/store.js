const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../models/productType');
const ProductModel = require('../../models/product');
const ProductPhamacy = require('../../models/productPhamacy');
const StoreModel = require('../../models/store');
const VinmartStoreModel = require('../../models/vinmartStore');
const VinmartProvince = require('../../models/vinmartProvince');
const VinmartWard= require('../../models/vinmartWard');
const VinmartDistrict= require('../../models/vinmartDistrict');

const PharmacityCategory = require('../../models/pharmacityCategory');
const tool = require('../../utils/tool');

module.exports = (req, res) => {

  let stores
  let region;
  const findPharma = (next) => {
    VinmartStoreModel
      .find({
        provinceCode:{$in:["HNI","HCM","HTH","NAN","THA","DNG"]}
      })
      .populate('province', 'description')
      .populate('ward', 'description')
      .populate('district', 'description')
      .lean()
      .exec((err, results) => {
        stores = results
        next();

      })
  }

  const sync = (next) => {

    async.eachSeries(stores,(store, done) => {
      switch (store.provinceCode) {
        case "HNI":
          region = "hn"
          break;
        case "HCM":
          region = "hcm"
          break;
        case "HTH":
          region = "vietnam:hatinh"
          break;
        case "NAN":
          region = "vietnam:nghean"
          break;
        case "THA":
          region = "vietnam:thanhhoa"
          break;
        case "DNG":
          region = "vietnam:danang"
          break;
        default:

      }
      let name = store.storeName;
      name = name.replace(`VM VMM ${store.provinceCode}`,"Vinmart")
      name = name.replace(`VM+ ${store.provinceCode}`,"Vinmart+")
      console.log('ahihi',name);
      StoreModel
        .update({
          'ref.id':store.id
        },{
          status: store.businessStatus ? 1 : 0
        },done)
    },(err,result) => {
      if(err) {
        return next(err)
      }
      next(null,{
        code:200
      })
    })
  }

  async.waterfall([
    findPharma,
    sync
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
