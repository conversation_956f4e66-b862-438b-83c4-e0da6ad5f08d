const rp = require('request-promise');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../models/productType');
const PharmacityCategory = require('../../models/pharmacityCategory');
const tool = require('../../utils/tool');
const VinmartCategory = require('../../models/vinmartCategory');

module.exports = (req, res) => {

  let categories

  const findPharma = (next) => {
    VinmartCategory
      .find({

      })
      .lean()
      .exec((err, results) => {
        categories = results
        next();

      })
  }

  const sync = (next) => {
    async.eachSeries(categories,(cate, done) => {

      ProductTypeModel
        .create({
          level: 0,
          ref:{
            id: cate.parent.id,
            refName: 'Vinmart'
          },
          name: cate.parent.description,
          parent: null,
          type: 'mart',
          default: 1,
          status: 1,
          nameAlias: tool.change_alias(cate.parent.description.trim()),
          icon:""
        },done)

    },(err,result) => {
      if(err) {
        return next(err)
      }
      next(null,{
        code:200
      })
    })
  }

  async.waterfall([
    findPharma,
    sync
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
