const _ = require('lodash')
const async = require('async')
const validate = require('express-validation')
const Joi = require('joi')
const config = require('config')
const rp = require('request-promise')
const ms = require('ms')
const mongoose = require('mongoose')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const MemberModel = require('../../models/member');
const TransactionLogModel = require('../../models/transactionLog');
const OrderStoreLogModel = require('../../models/orderStoreLog');
const PromoteCodeModel = require('../../models/promoteCode');
const ConfigModel = require('../../models/config');
const StaffModel = require('../../models/staff');
const OrderTypeModel = require('../../models/orderType');
const orderHelper = require('../../utils/order');
const locationHelper = require('../../utils/location');

const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');
const OrderManager = require('../../job/orderManager');
const PushConfirmManager = require('../../job/pushConfirmationManager');
const PaymentManager = require('../../job/paymentManager');

const generate = require('nanoid/generate')
const checkPromotion = require('../promote/checkPromotion');
const utils = require('../../utils/utils')
const BlockRequestJob = require('../../job/blockRequestJob');


Joi.objectId = require('joi-objectid')(Joi);

const schemaInput = {
  store: Joi.objectId().required(),
  receiver: Joi.object().keys({
    "name": Joi.string().empty('').default(''),
    "phone": Joi.string().required(),
    "address": Joi.object().keys({
      "name": Joi.string().required(),
      "location": Joi.object().keys({
        "lat": Joi.number().required(),
        "lng": Joi.number().required()
      }).required(),
      "subName": Joi.string().empty('').default('')
    })
  }).required(),
  cart: Joi.array().items(Joi.object().keys({
    _id: Joi.string().required(),
    quantity: Joi.number().required()
  })).required(),
  inapp: Joi.number().default(0),
  inappDeposit: Joi.number().default(0),
  money: Joi.number().required(),
  note: Joi.string().empty('').default(''),
  noteStore: Joi.string().empty('').default('')
}

module.exports = (req, res) => {
  req.body._id = mongoose.Types.ObjectId();
  req.body.code = generate('0123456789', 6);

  const storeId = _.get(req.body, 'store', '');
  const carts = _.get(req.body, 'cart', []);
  const appName = _.get(req.body, 'appName', 'customer');
  const platform = _.get(req.body, 'platform', '');
  const nativeVersion = _.get(req.body, 'nativeVersion', 0);

  const userId = _.get(req.user, 'id', '');
  req.body.customer = userId;

  let storeInf;
  let money = 0;
  let listStaff = [];
  const pendingPaymentMethods = ['momo', 'zalo', 'shopee', 'tokenization']

  if(!req.body.paymentMethod) {
    req.body.paymentMethod = 'cash'
  }

  if (req.body.paymentMethod === 'tokenization' && platform === 'ios' && nativeVersion == 3500061) {
    req.body.paymentMethod = 'cash'
    req.body.inapp = 0
    req.body.inappDeposit = 0
  }

  if(req.body.paymentMethod === 'tokenization') {
    req.body.inappDeposit = req.body.money
  }

  if(pendingPaymentMethods.includes(req.body.paymentMethod)) {
    req.body.status = CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT
  }

  const checkParams = (next) => {
    const result = Joi.validate(req.body, schemaInput, { allowUnknown: true, convert: true });

    if (result.error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if (!req.body.receiver.name) {
      req.body.receiver.name = `${req.body.receiver.phone.substring(0, 5)}*****`;
    }

    if(req.body.inappDeposit < 0) {
      req.body.inappDeposit = 0
    }

    next();
  }

  const checkCanCreate = (next) => {
    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.CREATE_ORDER, req.body.region, (err, data) => {
        if (err) {
          return next(err);
        }

        if (!data || !data.config || !data.config.canCreate) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Hiện tại hệ thống xử lý đơn hàng đang bảo trì. Bạn vui lòng quay lại sau ít phút. Xin cám ơn!'
            }
          });
        }

        next();
      });
  }

  const checkBlockCreate = (next) => {
    MemberModel
      .findOne({ _id: userId })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        if (result && result.blockCreateOrder) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.ORDER.BLOCK_CREATE_ORDER
          })
        }

        next();
      });
  }

  const checkStoreDemo = (next) => {
    if (storeId === '61b2b04e4bdf4cb43da8c325') {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.ORDER.STORE_DEMO
      });
    }

    next();
  }

  const checkDistance = (next) => {
    let maxDistance = 20;
    if(req.body.region === 'vietnam:hungyen') {
      maxDistance = 20
    }
    if(req.body.region === 'vietnam:haiduong' || req.body.region === 'vietnam:bacninh' || req.body.region === 'vietnam:nghean') {
      maxDistance = 25
    }
    if (req.body.distance > maxDistance) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.ORDER.DISTANCE_TOO_FAR
      });
    }

    next();
  }

  const checkSalary = (next) => {
    if (req.body.salary <= 0) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.ORDER.MONEY_IS_NOMORE_VALID
      });
    }

    next();
  }

  const preventCreateMultipleOrders = (next) => {
    const currentTime = Date.now();

    OrderStoreModel
      .count({
        store: storeId,
        customer: userId,
        updatedAt: {
          $gte: currentTime - ms('30s')
        },
        status: CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION
      })
      .exec((err, numberOrder) => {
        if (err) {
          return next(err);
        }

        if (numberOrder > 0) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.SYSTEM.ERROR
          });
        }

        next();
      })
  }

  const checkStore = (next) => {
    StoreModel
      .findOne({
        _id: storeId,
        status: 1
      })
      .populate('member', 'code')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result || (!result.golive && !result.golink)) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.ORDER.STORE_CLOSED
          })
        }

        storeInf = result;

        next();
      })
  }

  const checkCustomer = (next) => {
    if (storeInf.member._id.toHexString() === userId) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Thông báo',
          body: 'Xin lỗi, Bạn không thể tự mua hàng ở cửa hàng của chính mình. Xin cảm ơn.'
        }
      })
    }

    next();
  }

  const checkTimeSettings = (next) => {
    const newDate = new Date();
    const dayNum = newDate.getDay();
    const startDate = newDate.setHours(0, 0, 0, 0);
    const duration = Date.now() - startDate;
    let isOpen = 0;

    storeInf.timeSettings && storeInf.timeSettings[dayNum].map(time => {
      if (duration >= time.startTime && duration <= time.endTime) {
        isOpen = 1;
      }
    })

    if (!isOpen) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.ORDER.STORE_CLOSED
      });
    }

    next();
  }

  const getCart = (next) => {
    if (!carts || !carts.length) {
      return next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }

    const cartIds = carts.map(cart => cart._id);

    ProductModel
      .find({
        _id: { $in: cartIds }
      }, 'name price pricePromote images description isAvailable')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL
          });
        }

        results.map(result => {
          const isAvailable = result.isAvailable.map(item => item.toString());
          if (!isAvailable.includes(storeId)) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: MESSAGES.ORDER.PRODUCT_IS_AVAILABLE
            })
          }

          carts.map(cart => {
            if (cart._id === result._id.toHexString()) {
              cart = _.merge(cart, result);
            }
          })
        })

        next()
      })
  }

  const checkMoney = (next) => {
    carts.map(cart => {
      let moneyTopping = 0;
      if (cart.toppingSelected) {
        cart.toppingSelected.map(toppingSelected => {
          if (toppingSelected.topping) {
            toppingSelected.topping.map(topping => {
              moneyTopping += (topping.price * topping.quantity);
            })
          }
        })
      }

      money += (cart.pricePromote + moneyTopping) * cart.quantity;
    })

    if (money !== req.body.money) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  }

  const determineRegionOrder = (next) => {
    const location = {
      lat: storeInf.location.coordinates[1],
      lng: storeInf.location.coordinates[0]
    };

    locationHelper
      .getRegionByLatLng(location, 3, (err, region) => {
        if (err) {
          return next(err);
        }

        req.body.regionDistrict = region;
        let regionCity

        if (region.includes('hanoi')) {
          regionCity = 'hn'
        } else if (region.includes('hochiminh')) {
          regionCity = 'hcm'
        } else {
          const regionCityArray = region.split(":")
          regionCity = `${regionCityArray[0]}:${regionCityArray[1]}`;
          if(config.specialDistrict && config.specialDistrict.includes(region)) {
            regionCity = `${regionCityArray[0]}:${regionCityArray[1]}-${regionCityArray[2]}`
          }
        }

        req.body.region = regionCity;

        next();
      })
  }

  const getOrderTypeInf = (next) => {
    if (req.body.orderType) {
      return next();
    }

    OrderTypeModel
      .findOne({
        status: 1,
        $or: [
          {
            'region.allow': req.body.region
          },
          {
            'region.allow': 'all',
            'region.deny': {
              $ne: req.body.region
            }
          }
        ],
        service: storeInf.service
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        req.body.orderType = result._id;

        next();
      })
  }

  const checkPromotionCode = (next) => {
    if (!req.body.promoteStore) {
      return next();
    }

    const body = {
      regionName: req.body.region,
      promoteId: req.body.promoteStore,
      distance: req.body.distance,
      money: req.body.money,
      orderType: req.body.orderType,
      originName: storeInf.address,
      originPlace: {
        lat: storeInf.location.coordinates[1],
        lng: storeInf.location.coordinates[0]
      },
      destinationNames: [req.body.receiver.address.name],
      destinationPlaces: [req.body.receiver.address.location]
    }

    checkPromotion({
      body,
      user: req.user
    }, {
      json: (data) => {
        if (data.code !== CONSTANTS.CODE.SUCCESS) {
          return next(data);
        }

        if (data.data.discountDeposit !== req.body.discountMoneyDeposit) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PROMOTE.CHECK_PROMOTE
          })
        }

        req.body.money -= data.data.discountDeposit;

        next();
      }
    })
  }

  const calculateServiceCharge = (next) => {
    req.body.serviceChargeMerchant = 0;

    if (storeInf.service.toHexString() === '5d4cea5468731c9493253bb9') {
      req.body.serviceChargeMerchant = orderHelper.calculateServiceCharge(req.body);
    }

    next();
  }

  const calculatePromote = (next) => {
    if (!req.body.promote) {
      return next();
    }

    PromoteCodeModel
      .findOne({ _id: req.body.promote })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        const currentTime = Date.now();
        if (!result || !utils.checkRange(currentTime, result.condition.time.value)) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Mã khuyến mãi không tồn tại hoặc hết hiệu lực. Bạn vui lòng sử dụng mã khác. Xin cảm ơn.'
            }
          });
        }

        if (result.ratio && result.ratio.merchant) {
          req.body.merchantPaySalary = req.body.discountMoney * result.ratio.merchant / 100;
        }

        next();
      })
  }

  const checkRegionSupport = (next) => {
    // TODO:
    next();
  }

  const syncSalary = (next) => {
    if(!req.body.salaryStrategy) {
      req.body.salaryStrategy = {
        direct: req.body.salary,
        pay: 0,
        inapp: 0,
        discountMoneyPoint: 0
      }
    }

    req.body.salaryStrategy.tip = req.body.tip;
    req.body.salaryStrategy.CODFee = req.body.CODFee;
    req.body.salaryStrategy.weightFee = req.body.weightFee;

    req.body.salaryStrategy.VAT = 0;
    req.body.salaryStrategy.inapp = 0;
    req.body.salaryStrategy.discountMoneyPoint = req.body.discountMoneyPoint || 0;
    req.body.salaryStrategy.priceIncrease = 1;

    next();
  }

  const getStaff = (next) => {
    StaffModel
      .find({
        store: storeId,
        status: 1,
        online: 1
      }, 'member')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results) {
          return next();
        }

        listStaff = results.map(result => result.member);

        next();
      })
  }

  const addInappForPendingMethod = (next) => {
    if(!pendingPaymentMethods.includes(req.body.paymentMethod)) {
      return next();
    }
    req.body.salaryStrategy.inapp = req.body.inapp;
    next();
  }

  const checkAndHandlePaymentViaApp = (next) => {
    const totalMoneyInapp = req.body.inapp + req.body.inappDeposit
    if(!totalMoneyInapp || pendingPaymentMethods.includes(req.body.paymentMethod)) {
      return next();
    }

    let memberInfo;

    const descreaseDeposit = (done) => {
      MemberModel
        .findOneAndUpdate({
          _id: req.user.id,
          deposit: {
            $gte: totalMoneyInapp
          }
        }, {
          $inc: {
            deposit: -totalMoneyInapp
          }
        })
        .exec((err, result) => {
          if(err) {
            return done(err);
          }

          if(!result) {
            return done({
              code: CONSTANTS.CODE.FAIL,
              message: {
                'head': 'Thông báo',
                'body': `Tài khoản số dư của bạn hiện không còn đủ ${totalMoneyInapp.toLocaleString().replace(/,/g, ".")}₫ để thanh toán cho đơn hàng. Vui lòng chọn loại thanh toán khác. Xin cảm ơn.`
              }
            })
          }

          memberInfo = result;

          req.body.salaryStrategy.inapp = req.body.inapp;

          done();
        })
    }

    const writeLogTransaction = (done) => {
      TransactionLogModel
        .create({
          member: req.user.id,
          region: req.body.region,
          message: "Thanh toán phí ship đơn hàng",
          data: {
            amount: -req.body.inapp,
            idOrder: req.body._id,
            type: 11,
            back: 0,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney,
            initialRealMoney: memberInfo.realMoney,
            initialDeposit: memberInfo.deposit,
            finalDeposit: memberInfo.deposit - req.body.inapp,
          }
        }, (err) => {
          done();
        })
    }

    const writeLogTransactionDeposit = (done) => {
      TransactionLogModel
        .create({
          member: req.user.id,
          region: req.body.region,
          message: "Thanh toán COD đơn hàng",
          data: {
            amount: -req.body.inappDeposit,
            idOrder: req.body._id,
            type: 33,
            back: 0,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney,
            initialRealMoney: memberInfo.realMoney,
            initialDeposit: memberInfo.deposit - req.body.inapp,
            finalDeposit: memberInfo.deposit - totalMoneyInapp,
          }
        }, (err) => {
          done();
        })
    }

    async.waterfall([
      descreaseDeposit,
      writeLogTransaction,
      writeLogTransactionDeposit
    ], (err) => {
      if(err) {
        return next(err);
      }
      PushNotifyManager.sendToMember(req.user.id, 'Thông báo', `Tài khoản HeyU của bạn đã bị trừ ${totalMoneyInapp} đ để thanh toán cho đơn hàng.`, {link:'', extras:{tabFocus:2}},'profile_update');
      next();
    })
  }

  const insertToDb = (next) => {
    OrderStoreModel.create(req.body, next);
  }

  async.waterfall([
    checkParams,
    checkBlockCreate,
    checkStoreDemo,
    checkSalary,
    preventCreateMultipleOrders,
    checkStore,
    checkCustomer,
    checkTimeSettings,
    getCart,
    determineRegionOrder,
    checkCanCreate,
    checkDistance,
    checkMoney,
    getOrderTypeInf,
    checkPromotionCode,
    calculateServiceCharge,
    calculatePromote,
    checkRegionSupport,
    syncSalary,
    getStaff,
    addInappForPendingMethod,
    checkAndHandlePaymentViaApp,
    insertToDb
  ], (err, data) => {
    if (err) {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      return res.json(_.isError(err) ? {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      } : err)
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: data._id
    });

    if(pendingPaymentMethods.includes(req.body.paymentMethod)) {

      OrderStoreLogModel.create({
        member: userId,
        order: data._id,
        store: storeId,
        customer: userId,
        merchant: storeInf.member._id,
        type: CONSTANTS.ORDER_LOG.WAIT_FOR_PAYMENT
      });

      PaymentManager
        .add(data._id);

    } else {
      let sound = 'sound_heyu.wav';

      PushNotifyManager.sendToMember(storeInf.member._id.toHexString(), 'Bạn có đơn hàng mới', `Một đơn hàng đang chờ bạn xác nhận tại HeyU. Nhấn để xem chi tiết`, { link: 'SBHDetailOrderScreen', extras: {id: data._id}, sound }, 'new_order_merchant', 'tickbox');
      if (listStaff && listStaff.length) {
        listStaff.map(staff => {
          PushNotifyManager.sendToMember(staff.toString(), 'Bạn có đơn hàng mới', `Một đơn hàng đang chờ bạn xác nhận tại HeyU. Nhấn để xem chi tiết`, { link: 'SBHDetailOrderScreen', extras: {id: data._id}, sound }, 'new_order_merchant', 'tickbox');
        })
      }
      PushConfirmManager.add(data._id, sound);
      BlockRequestJob.remove(data._id);

      OrderStoreLogModel.create({
        member: userId,
        order: data._id,
        store: storeId,
        customer: userId,
        merchant: storeInf.member._id,
        type: CONSTANTS.ORDER_LOG.CREATE
      });

      OrderManager.add(data._id, req.body.region, storeInf.phone);
    }

  });
}
