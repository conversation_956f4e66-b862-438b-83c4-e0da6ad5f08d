const _ = require('lodash')
const async = require('async')
const config = require('config')
const ms = require('ms')
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const ConfigMerchantModel = require('../../models/configMerchant');
const ConfigModel = require('../../models/config');
const StoreModel = require('../../models/store');
const UserStoreModel = require('../../models/userStore');
const MemberModel = require('../../models/member');
const locationHelper = require('../../utils/location');

module.exports = (req, res) => {
  const userId = req.user.id;
  const platform = _.get(req, 'body.platform', '');
  const nativeVersion = _.get(req, 'body.nativeVersion', 0);
  let regionName = _.get(req, 'body.regionName', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const newVersion = (platform === 'ios' && nativeVersion >= 2700021) || (platform === 'android' && nativeVersion >= 160072) || req.body.appName === 'tickbox';
  let data = {
    isOpenStore: 0,
    forceApp: {
      "isOpen" : 0,
      "link" : {
          "ios" : "https://itunes.apple.com/vn/app/săn-ship/id1602613715?mt=8",
          "android" : "https://play.google.com/store/apps/details?id=com.tickbox.pro"
      },
      "cancel" : 0,
      "description" : "Mời bạn tải ứng dụng mới dành riêng cho merchant"
    },
    linkZalo: '',
    tag: ''
  };
  let businessType;
  let storeInf;

  const checkParams = (next) => {
    if (!merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const checkAuthen = (next) => {
    if (!config.listRegionOpened.includes(regionName)) {
      return next();
    }

    MemberModel
      .findOne({_id: merchant}, 'ship')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next();
        }

        if (!result.ship.isAuthen) {
          data.isOpenStore = 1;
        }

        next();
      })
  }

  const checkStoreExists = (next) => {
    if (data.isOpenStore) {
      return next();
    }

    UserStoreModel
      .findOne({member: merchant, focus: 1}, 'location level')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next();
        }

        data.isOpenStore = 1;

        storeInf = result;

        next();
      })
  }

  const determineRegion = (next) => {
    if (!storeInf) {
      return next();
    }

    const location = {
      lat: storeInf.location.coordinates[1],
      lng: storeInf.location.coordinates[0]
    }

    locationHelper
      .getRegionByLatLng(location, 2, (err, region) => {
        if (err) {
          return next(err);
        }

        regionName = region;

        next();
      })
  }

  const getConfigExact = (next) => {
    if (data.isOpenStore) {
      return next();
    }

    ConfigMerchantModel
      .findOne({
        status: 1,
        member: merchant
      }, 'config')
      .lean()
      .exec((err, result) => {
        if (err || !result || !result.config) {
          return next();
        }

        if (result.config.isOpenStore) {
          data.isOpenStore = result.config.isOpenStore
        }

        next();
      })
  }

  const getConfigDefault = (next) => {
    if (data.isOpenStore) {
      return next();
    }

    ConfigMerchantModel
      .findOne({
        status: 1,
        default: 1
      }, 'config')
      .lean()
      .exec((err, result) => {
        if (err || !result || !result.config) {
          return next()
        }

        if (result.config.isOpenStore) {
          data.isOpenStore = result.config.isOpenStore
        }

        next();
      })
  }

  const getConfigDownloadApp = (next) => {
    if (!data.isOpenStore) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data: newVersion ? data : data.isOpenStore
      });
    }

    if (req.body.appName === 'tickbox') {
      return next();
    }

    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.APP_TICKBOX, regionName, (err, result) => {
        if (err || !result || !result.config) {
          return next();
        }

        data.forceApp = result.config;

        if (!storeInf || (!storeInf.golive && !storeInf.golink)) {
          data.forceApp.isOpen = 0;
        }

        next();
      })
  }

  const getConfigLinkZalo = (next) => {
    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.LINK_ZALO, regionName, (err, result) => {
        if (err || !result || !result.config) {
          return next();
        }

        UserStoreModel
          .findOne({member: merchant, focus: 1}, 'businessType')
          .lean()
          .exec((err, res) => {
            if (err || !res) {
              return next();
            }

            if (res.businessType && res.businessType[0] && res.businessType[0].toHexString() === '6155193fd5f7a54fb77b4ac7') {
              data.linkZalo = result.config.linkZalo[res.businessType[0].toHexString()];
            } else {
              data.linkZalo = result.config.linkZalo.other;
            }

            next();
          })
      })
  }

  const getConfigTag = (next) => {
    // if (_.get(req, 'body.regionName', '') !== 'hn' && _.get(req, 'body.regionName', '') !== 'hcm' && !_.get(req, 'body.regionName', '').includes('vietnam')) {
    //   return next(null, {
    //     code: CONSTANTS.CODE.SUCCESS,
    //     data: {
    //       ...data,
    //       isOpenStore: 0
    //     }
    //   });
    // }

    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.TAG, regionName, (err, result) => {
        if (err || !result || !result.config) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: newVersion ? data : data.isOpenStore
          });
        }

        data.tag = result.config.tag;

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: newVersion ? data : data.isOpenStore
        });
      })
  }

  async.waterfall([
    checkParams,
    checkAuthen,
    checkStoreExists,
    determineRegion,
    getConfigExact,
    getConfigDefault,
    getConfigDownloadApp,
    getConfigLinkZalo,
    getConfigTag
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  })
}
