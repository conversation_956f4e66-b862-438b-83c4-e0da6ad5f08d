const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreModel = require('../../models/orderStore');
const OrderStoreLogModel = require('../../models/orderStoreLog');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const UserProductModel = require('../../models/userProduct');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');
const OrderManager = require('../../job/orderManager');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.orderId', '');
  const storeId = _.get(req, 'body.storeId', '');
  const deliveryType = _.get(req, 'body.deliveryType', '');
  const status = _.get(req, 'body.status', CONSTANTS.ORDER_STATUS.DELIVERING);
  const userId = req.user.id;
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let orderInf;
  let storeInf;

  const checkParams = (next) => {
    if (!orderId || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getStoreInf = (next) => {
    StoreModel
      .findOne({
        _id: storeId,
        member: merchant
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        storeInf = result;
        next();
      })
  }

  const updateOrder = (next) => {
    const query = {
      _id: orderId,
      store: storeId,
      status: CONSTANTS.ORDER_STATUS.DELIVERING
    };
    const objUpdate = {
      status
    }

    if (status === CONSTANTS.ORDER_STATUS.DELIVERING) {
      query.status = CONSTANTS.ORDER_STATUS.PREPARE_PRODUCT;
      objUpdate.deliveryType = deliveryType;
      objUpdate.messageDelivery = 'Hiện tại HeyU không thể tìm tài xế cho đơn hàng này của bạn, vì vậy cửa hàng sẽ tự giao hàng cho bạn. Bạn vui lòng tự thoả thuận phí ship với cửa hàng để tránh nhầm lẫn. HeyU rất xin lỗi vì sự bất tiện này.';
    }

    OrderStoreModel
      .findOneAndUpdate(query, objUpdate)
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        orderInf = result;

        next();
      });
  }

  const otherTask = (next) => {
    const objLog = {
      member: userId,
      order: orderId,
      store: storeInf._id,
      customer: orderInf.customer,
      merchant: storeInf.member || '',
      type: CONSTANTS.ORDER_LOG.DONE
    };
    let messagePush = '';
    let titlePush = '';

    if (status === CONSTANTS.ORDER_STATUS.DELIVERING) {
      titlePush = 'Heyyy';
      messagePush = `Đơn hàng tại ${storeInf.name} đang được tài xe giao cho bạn. Bạn vui lòng để ý điện thoại để tài xế có thể liên hệ. Xin cảm ơn`;
      objLog.type = CONSTANTS.ORDER_LOG.SELF_DELIVERY;
    }

    PushNotifyManager.sendToMember(orderInf.customer.toHexString(), titlePush, messagePush, { link: 'SBHDetailOrderForShopScreen', extras: { id: orderId } }, 'order_update_customer')
    if (storeInf.member) {
      PushNotifyManager.sendToMember(storeInf.member.toHexString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox')
    }

    if (staffs.length) {
      staffs.map(staff => {
        PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox');
      })
    }

    OrderStoreLogModel.create(objLog);

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật trạng thái thành công'
      }
    });
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    updateOrder,
    otherTask
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
}
