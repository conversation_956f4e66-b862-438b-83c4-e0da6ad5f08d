const _ = require('lodash')
const config = require('config')
const util = require('util')
const async = require('async')
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const ConfigModel = require('../../models/config');

module.exports = (req, res) => {
  const regionName = _.get(req, 'body.regionName', '');
  const modeApp = _.get(req, 'body.modeApp', '');
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  const checkParams = (next) => {
    if (!modeApp || !regionName) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getConfig = (next) => {
    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.BAR_CODE, regionName, (err, result) => {
        if (err) {
          return next(err);
        }

        if (!result || !result.config) {
          return next(null, {
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        if (config.listUserBarCode.includes(merchant)) {
          result.config = {
            isOpen: 1
          }
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result.config
        })
      })
  }

  async.waterfall([
    checkParams,
    getConfig
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
