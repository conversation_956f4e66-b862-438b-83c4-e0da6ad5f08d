const _ = require('lodash')
const async = require('async')
const config = require('config')
const ms = require('ms')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const OrderSystemModel = require('../../models/orderSystem');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')

module.exports = (req, res) => {
  const storeId = _.get(req, 'body.storeId', '');
  const status = _.get(req, 'body.status', []);
  const skip = _.get(req, 'body.skip', 0);
  const limit = _.get(req, 'body.limit', 10);
  const userId = req.user.id;
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let listOrders = [];

  const checkParams = (next) => {
    if (!storeId || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const checkStore = (next) => {
    StoreModel
      .count({
        member: merchant,
        _id: storeId
      })
      .lean()
      .exec((err, count) => {
        if (err) {
          return next(err)
        }

        if (!count) {
          return next({
            code: CONSTANTS.CODE.FAIL
          })
        }

        next();
      })
  }

  const getOrderInf = (next) => {
    let query = {
      store: storeId
    }

    if (status && status.length) {
      query.status = {$in: status};
    }

    OrderStoreModel
      .find(query, '-receiver.phone')
      .populate({
        path: 'delivery',
        populate: [
          {path: 'shipper'}
        ]
      })
      .sort('-createdAt')
      .skip(skip)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next();
        }

        listOrders = results;

        next();
      });
  }

  const getCart = (next) => {
    if (!listOrders || !listOrders.length) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: listOrders
      });
    }

    async.eachSeries(listOrders, (order, done) => {
      if (!order.cart || !order.cart.length) {
        return done();
      }

      let cart = order.cart;
      const cartIds = cart.map(cart => cart._id);

      ProductModel
        .find({
          _id: { $in: cartIds }
        }, 'name price pricePromote images description')
        .lean()
        .exec((err, results) => {
          if (err) {
            return done(err);
          }

          if (!results || !results.length) {
            return done({
              code: CONSTANTS.CODE.FAIL
            });
          }

          results.some(result => {
            const indexInCart = _.findIndex(cart, item => item._id === result._id.toHexString());

            if (indexInCart !== -1) {
              cart[indexInCart] = _.merge(cart[indexInCart], result);
              order.cart = cart;
              return;
            }
          })

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: listOrders
      })
    })
  }

  async.waterfall([
    checkParams,
    checkStore,
    getOrderInf,
    getCart
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
