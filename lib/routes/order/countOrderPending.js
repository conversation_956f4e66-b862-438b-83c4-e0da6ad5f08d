const _ = require('lodash')
const async = require('async')
const config = require('config')
const ms = require('ms')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')

module.exports = (req, res) => {
  const userId = req.user.id;
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const store = _.get(req, 'body.myStoreId', '');
  let storeInf;

  const checkParams = (next) => {
    if (!merchant || !store) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data:  0
      })
    }

    next();
  }

  const checkStore = (next) => {
    StoreModel
      .findOne({
        _id: store,
        member: merchant
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: 0
          });
        }

        storeInf = result;

        next();
      })
  }

  const countOrder = (next) => {
    const now = Date.now();
    let query = {
      store: storeInf._id,
      updatedAt: { $gte: now - ms('5h') },
      status: CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION
    }

    OrderStoreModel
      .count(query)
      .lean()
      .exec((err, count) => {
        if (err) {
          return next(err)
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: count || 0
        });
      });
  }

  async.waterfall([
    checkParams,
    checkStore,
    countOrder
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  })
}
