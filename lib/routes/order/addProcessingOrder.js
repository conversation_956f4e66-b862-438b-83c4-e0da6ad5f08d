const _ = require('lodash')
const async = require('async')
const validate = require('express-validation')
const Joi = require('joi')
const config = require('config')
const rp = require('request-promise')
const ms = require('ms')
const mongoose = require('mongoose')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const MemberModel = require('../../models/member');
const TransactionLogModel = require('../../models/transactionLog');
const OrderStoreLogModel = require('../../models/orderStoreLog');
const PromoteCodeModel = require('../../models/promoteCode');
const StaffModel = require('../../models/staff');
const orderHelper = require('../../utils/order');
const locationHelper = require('../../utils/location');

const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');
const OrderManager = require('../../job/orderManager');
const PushConfirmManager = require('../../job/pushConfirmationManager');
const PaymentManager = require('../../job/paymentManager');

const generate = require('nanoid/generate')
const checkPromotion = require('../promote/checkPromotion');


module.exports = (req, res) => {

  const orderId = _.get(req, 'body.id', '');
  const appName = _.get(req, 'body.appName', 'customer');
  const platform = _.get(req, 'body.platform', '');
  const nativeVersion = _.get(req, 'body.nativeVersion', 0);

  let orderInf
  let storeInf
  let listStaff = [];
  const checkParams = (next) => {
    if(!orderId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const checkOrderInf = (next) => {

    OrderStoreModel
      .findById(orderId)
      .populate('store member')
      .lean()
      .exec((err, result) => {

        if(err) {
          return next(err)
        }

        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL
          })
        }
        if(result.status !== CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT) {
          return next({
            code: CONSTANTS.CODE.FAIL
          })
        }

        orderInf = result
        storeInf = orderInf.store
        next();
      })
  }

  const getStaff = (next) => {
    StaffModel
      .find({
        store: storeInf._id,
        status: 1,
        online: 1
      }, 'member')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results) {
          return next();
        }

        listStaff = results.map(result => result.member);

        next();
      })
  }


  const checkAndHandlePaymentViaApp = (next) => {

    if(!orderInf.inappDeposit) {
      return next();
    }

    const totalFeeCash = orderInf.inappDeposit + orderInf.salaryStrategy.inapp

    let memberInfo;

    const descreaseDeposit = (done) => {
      MemberModel
        .findOneAndUpdate({
          _id: orderInf.customer,
          deposit: {
            $gte: totalFeeCash
          }
        }, {
          $inc: {
            deposit: -totalFeeCash
          }
        })
        .exec((err, result) => {
          if(err) {
            return done(err);
          }

          if(!result) {
            return done({
              code: CONSTANTS.CODE.FAIL,
              message: {
                'head': 'Thông báo',
                'body': `Tài khoản số dư của bạn hiện không còn đủ ${totalFeeCash.toLocaleString().replace(/,/g, ".")}₫ để thanh toán cho đơn hàng. Vui lòng chọn loại thanh toán khác. Xin cảm ơn.`
              }
            })
          }

          memberInfo = result;

          done();
        })
    }

    const writeLogTransaction = (done) => {
      TransactionLogModel
        .create({
          member: orderInf.customer,
          region: orderInf.region,
          message: "Thanh toán phí ship đơn hàng",
          data: {
            amount: -orderInf.salaryStrategy.inapp,
            idOrder: orderInf._id,
            type: 11,
            back: 0,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney,
            initialRealMoney: memberInfo.realMoney,
            initialDeposit: memberInfo.deposit,
            finalDeposit: memberInfo.deposit - orderInf.salaryStrategy.inapp,
          }
        }, (err) => {
          done();
        })
    }

    const writeLogTransactionDeposit = (done) => {
      TransactionLogModel
        .create({
          member: orderInf.customer,
          region: orderInf.region,
          message: "Thanh toán COD đơn hàng",
          data: {
            amount: -orderInf.inappDeposit,
            idOrder: orderInf._id,
            type: 33,
            back: 0,
            finalCoints: memberInfo.coints,
            initialCoints: memberInfo.coints,
            finalRealMoneyShop: memberInfo.realMoneyShop,
            initialRealMoneyShop: memberInfo.realMoneyShop,
            finalRealMoney: memberInfo.realMoney,
            initialRealMoney: memberInfo.realMoney,
            initialDeposit: memberInfo.deposit - orderInf.salaryStrategy.inapp,
            finalDeposit: memberInfo.deposit - totalFeeCash,
          }
        }, (err) => {
          done();
        })
    }

    async.waterfall([
      descreaseDeposit,
      writeLogTransaction,
      writeLogTransactionDeposit
    ], (err) => {
      if(err) {
        return next(err);
      }

      PushNotifyManager.sendToMember(orderInf.customer, 'Thông báo', `Thanh toán đơn hàng thành công. Hệ thống đã gửi yêu cầu đến cửa hàng. Bạn vui lòng đợi xác nhận từ cửa hàng trong ít phút.`, {link: '', extras:{tabFocus:2}},'profile_update');

      next();
    })
  }


  const updateOrderInf = (next) => {
    let objUpdate = {
      status: CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION
    }
    OrderStoreModel
      .update({
        _id: orderId,
        status:  CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT
      },objUpdate,(err, result) => {
        if(err) {
          return next(err)
        }
        next()
      })

  }

  const addToProcess = (next) => {
    let sound = 'sound_heyu.wav';

    PushNotifyManager.sendToMember(storeInf.member.toString(), 'Bạn có đơn hàng mới', `Một đơn hàng đang chờ bạn xác nhận tại HeyU. Nhấn để xem chi tiết`, { link: 'SBHDetailOrderScreen', extras: {id: orderId}, sound }, 'new_order_merchant', 'tickbox');
    if (listStaff && listStaff.length) {
      listStaff.map(staff => {
        PushNotifyManager.sendToMember(staff.toString(), 'Bạn có đơn hàng mới', `Một đơn hàng đang chờ bạn xác nhận tại HeyU. Nhấn để xem chi tiết`, { link: 'SBHDetailOrderScreen', extras: {id: orderId}, sound }, 'new_order_merchant', 'tickbox');
      })
    }
    PushConfirmManager.add(orderId, sound);

    OrderStoreLogModel.create({
      member: orderInf.custome,
      order: orderId,
      store: storeInf._id,
      customer: orderInf.customer,
      merchant: storeInf.member,
      type: CONSTANTS.ORDER_LOG.CREATE
    });

    OrderManager.add(orderId, orderInf.region, storeInf.phone);


    PaymentManager
      .remove(orderId);

    next(null,{
      code: CONSTANTS.CODE.SUCCESS,
      data: orderId
    })
  }

  async.waterfall([
    checkParams,
    checkOrderInf,
    getStaff,
    checkAndHandlePaymentViaApp,
    updateOrderInf,
    addToProcess
  ], (err, data) => {
    if(_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })

}
