const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const OrderStoreLogModel = require('../../models/orderStoreLog');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');
const OrderManager = require('../../job/orderManager');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.id', '');
  const time = _.get(req, 'body.time', '');
  const userId = req.user.id;
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const appName = _.get(req, 'body.appName', 'customer');
  const platform = _.get(req, 'body.platform', '');
  const nativeVersion = _.get(req, 'body.nativeVersion', 0);
  const store = _.get(req, 'body.myStoreId', '');

  let orderInf;
  let storeInf;

  const checkParams = (next) => {
    if (!orderId || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.ERROR
      });
    }

    next();
  }

  const getStoreInf = (next) => {
    StoreModel
      .findOne({
        _id: store,
        member: merchant
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        storeInf = result;

        next();
      })
  }

  const updateOrder = (next) => {
    let messageWaitStore = `Hiện tại, cửa hàng ${storeInf.name} đang đông khách. Bạn vui lòng chờ ${time} phút để cửa hàng chuẩn bị đồ cho bạn. Xin cảm ơn`;

    OrderStoreModel
      .findOneAndUpdate({
        _id: orderId,
        store: storeInf._id
      }, { messageWaitStore, timeWait: {time, createdAt: Date.now()} })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        orderInf = result;

        OrderManager.remove(orderId);
        PushNotifyManager.sendToMember(orderInf.customer.toHexString(), 'Chờ xác nhận đơn', messageWaitStore, { link: 'SBHDetailOrderForShopScreen', extras: { id: orderInf._id } }, 'order_update_customer');
        PushNotifyManager.sendToMember(storeInf.member.toString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox');
        if (staffs.length) {
          staffs.map(staff => {
            PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox');
          })
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          msg: MESSAGES.ORDER.WAIT_NOTICE_SUCCESS
        });
      });
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    updateOrder
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);

    staffs.push(storeInf.member.toHexString());

    let sound = 'sound_heyu.wav';

    OrderManager.addJobRePushed(orderId, staffs, ms(`${time}m`), sound, orderInf.region, storeInf.phone);
  });
}
