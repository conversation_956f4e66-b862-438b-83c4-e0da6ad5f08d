const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreModel = require('../../models/orderStore');
const OrderStoreLogModel = require('../../models/orderStoreLog');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const UserProductModel = require('../../models/userProduct');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');
const OrderManager = require('../../job/orderManager');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.orderId', '');
  const storeId = _.get(req, 'body.storeId', '');
  const reason = _.get(req, 'body.reason', '');
  const userId = req.user.id;
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let orderInf;
  let storeInf;

  const checkParams = (next) => {
    if (!orderId || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getStoreInf = (next) => {
    StoreModel
      .findOne({
        _id: storeId,
        member: merchant
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        storeInf = result;
        next();
      })
  }

  const updateOrder = (next) => {
    OrderStoreModel
      .findOneAndUpdate({
        _id: orderId,
        store: storeId,
        $or: [
          {
            status: {
              $in: [CONSTANTS.ORDER_STATUS.PREPARE_PRODUCT, CONSTANTS.ORDER_STATUS.REJECT]
            }
          },
          {
            status: CONSTANTS.ORDER_STATUS.DELIVERING,
            deliveryType: 'selfDelivery'
          }
        ]
      }, {
        status: CONSTANTS.ORDER_STATUS.PREPARE_PRODUCT,
        deliveryType: 'heyu'
      })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        orderInf = result;

        next();
      });
  }

  const updateOrderShip = (next) => {
    if (!orderInf.delivery) {
      return next();
    }

    const options = {
      method: 'POST',
      uri: `${config.proxyRequestServer.orderSystem}/api/v1.0/admin/order/retry`,
      body: {
        id: orderInf.delivery,
        backSSM: 1,
        orderUpdated: 1
      },
      json: true // Automatically stringifies the body to JSON
    };

    rp(options)
      .then((result) => {
        if (result.code === CONSTANTS.CODE.SUCCESS) {
          next();
        } else {
          return next(result);
        }
      })
      .catch((err) => {
        return next(err);
      });
  }

  const otherTask = (next) => {
    PushNotifyManager.sendToMember(orderInf.customer.toHexString(), '', '', { link: 'SBHDetailOrderForShopScreen', extras: {id: orderId, idOrder: orderInf.delivery || ''} }, 'order_update_customer');
    if (storeInf.member) {
      PushNotifyManager.sendToMember(storeInf.member.toHexString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox')
    }

    if (staffs.length) {
      staffs.map(staff => {
        PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox');
      })
    }

    OrderStoreLogModel.create({
      member: userId,
      order: orderId,
      store: storeId,
      customer: orderInf.customer,
      merchant: storeInf.member || '',
      type: CONSTANTS.ORDER_LOG.RETRY_RIDER
    });

    next(null, {
      code: CONSTANTS.CODE.SUCCESS
    });
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    updateOrder,
    updateOrderShip,
    otherTask
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);

    OrderManager.remove(orderId);
    OrderManager.removeJobRePush(orderId);
  });
}
