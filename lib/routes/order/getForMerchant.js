const _ = require('lodash')
const async = require('async')
const config = require('config')
const ms = require('ms')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')

module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  const userId = req.user.id;
  let orderInf;

  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getOrderInf = (next) => {
    OrderStoreModel
      .findOne({
        _id: id
      })
      .populate({
        path: 'delivery',
        populate: [
          {path: 'shipper'}
        ]
      })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        orderInf = result;

        next();
      });
  }

  const getCart = (next) => {
    if (!orderInf.cart || !orderInf.cart.length) {
      return next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }

    let cart = orderInf.cart;
    const cartIds = cart.map(cart => cart._id);

    ProductModel
      .find({
        _id: { $in: cartIds }
      }, 'name price pricePromote images description')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL
          });
        }

        results.some(result => {
          const indexInCart = _.findIndex(cart, item => item._id === result._id.toHexString());

          if (indexInCart !== -1) {
            cart[indexInCart] = _.merge(cart[indexInCart], result);
            orderInf.cart = cart;
            return;
          }
        })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: orderInf
        })
      })
  }

  async.waterfall([
    checkParams,
    getOrderInf,
    getCart
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
