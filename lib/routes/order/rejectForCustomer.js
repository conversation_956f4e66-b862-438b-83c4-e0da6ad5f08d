const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreModel = require('../../models/orderStore');
const OrderStoreLogModel = require('../../models/orderStoreLog');
const StoreModel = require('../../models/store');
const StaffModel = require('../../models/staff');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');
const OrderManager = require('../../job/orderManager');
const redisConnections = require('../../connections/redis');
const memberHelper = require('../../utils/member');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.orderId', '');
  const userId = req.user.id;
  let orderInf;
  let oldStatus;
  let storeInf;
  let token;
  let staffs = [];

  const checkParams = (next) => {
    if (!orderId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getOrderInf = (next) => {
    OrderStoreModel
      .findOne({
        _id: orderId,
        customer: userId
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        orderInf = result;
        oldStatus = orderInf.status

        next();
      })
  }

  const checkLimitTimeReject = (next) => {
    if (orderInf.status === CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT) {
      return next()
    }

    if (orderInf.status === CONSTANTS.ORDER_STATUS.FOUND_SHIPPER && (Date.now() - orderInf.acceptedAt >= ms('10m'))) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.ORDER.LIMIT_TIME_REJECT
      })
    }

    next();
  }

  const updateOrderToReject = (next) => {
    OrderStoreModel
      .findOneAndUpdate({
        _id: orderId,
        customer: userId,
        status: {
          $in: [CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION, CONSTANTS.ORDER_STATUS.WAIT_FOR_EDIT, CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT]
        }
      }, {
        status: CONSTANTS.ORDER_STATUS.REJECT
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.ORDER.REJECT_FAIL_MERCHANT
          })
        }

        orderInf = result;

        next();
      });
  }

  const getStoreInf = (next) => {
    StoreModel
      .findOne({
        _id: orderInf.store
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        storeInf = result;
        next();
      })
  }

  const getStaff = (next) => {
    StaffModel
      .find({
        store: storeInf._id,
        status: 1,
        online: 1
      }, 'member')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next()
        }

        staffs = results.map(result => result.member.toString());

        next();
      })
  }

  const updateOrderShip = (next) => {
    if (!orderInf.delivery) {
      return next();
    }

    const options = {
      method: 'POST',
      uri: `${config.proxyRequestServer.orderSystem}/api/v3.1/order/reject-for-shop`,
      body: {
        orderId: orderInf.delivery,
        memberToken: req.body.memberToken,
        orderUpdated: 1
      },
      json: true // Automatically stringifies the body to JSON
    };

    rp(options)
      .then((result) => {
        if (result.code === 200) {
          next();
        } else {
          return next(result);
        }
      })
      .catch((err) => {
        return next(err);
      });
  }

  const handleBackMoneyInapp = (next) => {
    if((!orderInf.salaryStrategy.inapp && !orderInf.inappDeposit) || orderInf.status === CONSTANTS.ORDER_STATUS.WAIT_FOR_PAYMENT) {
      return next();
    }

    memberHelper.handleBackDeposit({
      userId: userId,
      orderId: orderId,
      inapp: orderInf.salaryStrategy.inapp,
      inappDeposit: orderInf.inappDeposit,
      region: orderInf.region,
      orderInf
    }, (err) => {
      if(err) {
        logger.logError(['handleBackMoneyInapp', err], __dirname);
      }

      if (orderInf.salaryStrategy.inapp && orderInf.paymentMethod === 'inapp') {
        PushNotifyManager
          .sendToMember(orderInf.customer.toHexString(), 'Thông báo', `Hệ thống vừa hoàn trả ${orderInf.salaryStrategy.inapp + orderInf.inappDeposit} đ vào tài khoản HeyU của bạn do đơn hàng bị huỷ.`, {link: '', extras: {tabFocus: 2}}, 'profile_update')
      }

      next();
    })
  }

  const otherTask = (next) => {
    PushNotifyManager.sendToMember(storeInf.member.toHexString(), 'Huỷ đơn', `Khách hàng tại ${orderInf.receiver.address.name} đã huỷ đơn hàng`, { link: 'SBHDetailOrderScreen', extras: {id: orderId} }, 'order_update_merchant', 'tickbox');
    if (staffs && staffs.length) {
      staffs.map(staff => {
        PushNotifyManager.sendToMember(staff, 'Huỷ đơn', `Khách hàng tại ${orderInf.receiver.address.name} đã huỷ đơn hàng`, { link: 'SBHDetailOrderScreen', extras: {id: orderId} }, 'order_update_merchant', 'tickbox');
      })
    }

    OrderStoreLogModel.create({
      member: userId,
      order: orderId,
      store: orderInf.store,
      customer: userId,
      merchant: storeInf.member,
      type: CONSTANTS.ORDER_LOG.REJECT
    });

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.ORDER.REJECT_SUCCESS
    });
  }

  async.waterfall([
    checkParams,
    updateOrderToReject,
    getStoreInf,
    getStaff,
    updateOrderShip,
    handleBackMoneyInapp,
    otherTask
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);

    OrderManager.remove(orderId);
    OrderManager.removeJobRePush(orderId);
  });
}
