const CONSTANTS = require('../../const');

module.exports = (req, res) => {
  const platform = req.body.platform || '';
  const nativeVersion = Number(req.body.nativeVersion) || 0;
  const appName = req.body.appName || '';

  if ((platform === 'ios' && nativeVersion > 2700071) || (platform === 'android' && nativeVersion > 170002) || (appName === 'tickbox')) {
    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: [
        {
          key: 'OUT_OF_STOCK',
          value: 'Cửa hàng hết hàng'
        },
        {
          key: 'CLOSED',
          value: 'Cửa hàng chưa mở cửa'
        }
      ]
    });
  }

  res.json({
    code: CONSTANTS.CODE.SUCCESS,
    data: [
      'Cửa hàng hết hàng',
      'Cửa hàng chưa mở cửa'
    ]
  });
}
