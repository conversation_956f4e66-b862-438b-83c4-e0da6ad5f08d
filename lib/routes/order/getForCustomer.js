const _ = require('lodash')
const async = require('async')
const config = require('config')
const ms = require('ms')
const crypto = require('crypto')
const OrderStoreModel = require('../../models/orderStore');
const ProductModel = require('../../models/product');
const OrderTypeModel = require('../../models/orderType');
const ShipperAuthenticationModel = require('../../models/shipperauthentication');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')

module.exports = (req, res) => {
  const id = _.get(req, 'body.id', '');
  const userId = req.user.id;
  let orderInf;

  const getOrderInf = (next) => {
    OrderStoreModel
      .findOne({
        customer: userId,
        _id: id
      })
      .populate('store', 'name image address phone member service')
      .populate({
        path: 'delivery',
        populate: [
          {path: 'shipper'}
        ]
      })
      .populate('orderType', 'resourceFee food')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next();
        }

        orderInf = result;

        next();
      });
  }

  const getShipperInfo = (next) => {
    if(orderInf.delivery && orderInf.delivery.shipper) {
        ShipperAuthenticationModel
        .findOne({
          member: orderInf.delivery.shipper._id
        })
        .lean()
        .exec((err, resultAuthen) => {
          if(resultAuthen) {
            orderInf.delivery.shipper.shipperAuthentication = resultAuthen
            next();
          } else {
            next();
          }
        })
    } else {
      next();
    }
  }

  const getCart = (next) => {
    if (!orderInf) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data: orderInf
      });
    }

    let cart = orderInf.cart;
    const cartIds = cart.map(cart => cart._id);

    ProductModel
      .find({
        _id: { $in: cartIds }
      }, 'images isAvailable name price pricePromote description')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL
          });
        }

        results.some(result => {
          const indexInCart = _.findIndex(cart, item => item._id.toString() === result._id.toString());
          const isAvailable = result.isAvailable.map(item => item.toString());

          result.isAvailable = 0;
          if (isAvailable.includes(orderInf.store._id.toString())) {
            result.isAvailable = 1;
          }

          if (indexInCart !== -1) {
            cart[indexInCart] = _.merge(cart[indexInCart], result);
            orderInf.cart = cart;
            return;
          }
        })

        next()
      })
  }

  const getLinkOrder = (next) => {
    if (orderInf.delivery) {
      const codeForEncrypt = `${orderInf.delivery._id}+${0}`;
      const token = crypto.createHash('md5').update(codeForEncrypt).digest("hex");

      orderInf.delivery.linkOrder = `${config.proxyRequestServer.heyuWeb}/track?order=${orderInf.delivery._id}&index=${0}&token=${token}`;
    }

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: orderInf
    });
  }

  async.waterfall([
    getOrderInf,
    getShipperInfo,
    getCart,
    getLinkOrder
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
