const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreModel = require('../../models/orderStore');
const OrderStoreLogModel = require('../../models/orderStoreLog');
const StoreModel = require('../../models/store');
const MemberModel = require('../../models/member');
const TransactionLogModel = require('../../models/transactionLog');
const ProductModel = require('../../models/product');
const UserProductModel = require('../../models/userProduct');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');
const OrderManager = require('../../job/orderManager');
const memberHelper = require('../../utils/member');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.id', '');
  const storeId = _.get(req, 'body.storeId', '');
  const reason = _.get(req, 'body.reason', '');
  const productNotAvailable = _.get(req, 'body.productNotAvailable', []);
  const userId = req.user.id;
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let orderInf;
  let storeInf;
  let oldStatus;

  const checkParams = (next) => {
    if (!orderId || !storeId || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getStoreInf = (next) => {
    StoreModel
      .findOne({
        _id: storeId,
        member: merchant
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        storeInf = result;
        next();
      })
  }

  const getOrderInf = (next) => {
    OrderStoreModel
      .findOne({
        _id: orderId,
        store: storeId
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        orderInf = result;
        oldStatus = orderInf.status
        orderInf.merchantPaySalary = orderInf.merchantPaySalary || 0;

        next();
      })
  }

  const checkLimitTimeReject = (next) => {

    if (orderInf.status === CONSTANTS.ORDER_STATUS.FOUND_SHIPPER && (Date.now() - orderInf.acceptedAt >= ms('10m'))) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.ORDER.LIMIT_TIME_REJECT
      })
    }

    next();
  }

  const updateOrderToReject = (next) => {
    OrderStoreModel
      .findOneAndUpdate({
        _id: orderId,
        store: storeId,
        status: {
          $in: [CONSTANTS.ORDER_STATUS.WAIT_FOR_CONFIRMATION, CONSTANTS.ORDER_STATUS.WAIT_FOR_EDIT, CONSTANTS.ORDER_STATUS.PREPARE_PRODUCT]
        }
      }, {
        status: CONSTANTS.ORDER_STATUS.REJECT
      })
      .populate('delivery','status')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.ORDER.REJECT_FAIL_MERCHANT
          })
        }

        orderInf = result;
        orderInf.merchantPaySalary = orderInf.merchantPaySalary || 0;

        next();
      });
  }

  const updateOrderShip = (next) => {
    if (!orderInf.delivery || (orderInf.delivery && (orderInf.delivery.status === 4 || orderInf.delivery.status === 5))) {
      return next();
    }

    const options = {
      method: 'POST',
      uri: `${config.proxyRequestServer.orderSystem}/api/v1.0/admin/order/reject`,
      body: {
        id: orderInf.delivery._id,
        userId: orderInf.customer,
        backSSM: 1,
        shipperWrong: 0,
        orderUpdated: 1
      },
      json: true // Automatically stringifies the body to JSON
    };

    rp(options)
      .then((result) => {
        if (result.code === 200) {
          next();
        } else {
          return next(result);
        }
      })
      .catch((err) => {
        return next(err);
      });
  }

  const handleBackMoney = (next) => {
    if (!orderInf.delivery) {
      return next();
    }

    MemberModel
      .increaseMoney(storeInf.member, (orderInf.serviceChargeMerchant + orderInf.merchantPaySalary), (err, data) => {

        if(err) {
          return next(err);
        }

        next();

        TransactionLogModel
          .create({
            member: storeInf.member,
            region: orderInf.region,
            message: "Trả lại phí dịch vụ đơn hàng",
            data: {
              amount: orderInf.serviceChargeMerchant,
              idOrder: orderId,
              type: 31,
              back: 1,
              finalCoints: data.coints,
              initialCoints: data.coints,
              initialRealMoneyShop: data.realMoneyShop,
              finalRealMoneyShop: data.realMoneyShop,
              initialRealMoney: data.realMoney,
              finalRealMoney: data.realMoney,
              initialMoney: data.money - orderInf.serviceChargeMerchant - orderInf.merchantPaySalary,
              finalMoney: data.money - orderInf.merchantPaySalary
            }
          }, () => {
            if (orderInf.merchantPaySalary > 0) {
              TransactionLogModel
                .create({
                  member: storeInf.member,
                  region: orderInf.region,
                  message: "Trả lại phí ship chương trình đồng tài trợ với HeyU",
                  data: {
                    amount: orderInf.merchantPaySalary,
                    idOrder: orderId,
                    type: 32,
                    back: 1,
                    finalCoints: data.coints,
                    initialCoints: data.coints,
                    initialRealMoneyShop: data.realMoneyShop,
                    finalRealMoneyShop: data.realMoneyShop,
                    initialRealMoney: data.realMoney,
                    finalRealMoney: data.realMoney,
                    initialMoney: data.money - orderInf.merchantPaySalary,
                    finalMoney: data.money
                  }
                }, () => {
                  PushNotifyManager.sendToMember(storeInf.member, '', ``, { link: '' }, 'profile_update', 'tickbox')
                })
            }
          })
      })
  }

  const handleBackMoneyInapp = (next) => {
    if(!orderInf.salaryStrategy.inapp && !orderInf.inappDeposit) {
      return next();
    }

    memberHelper.handleBackDeposit({
      userId: orderInf.customer,
      orderId: orderId,
      inapp: orderInf.salaryStrategy.inapp,
      inappDeposit: orderInf.inappDeposit,
      region: orderInf.region,
      orderInf
    }, (err) => {
      if(err) {
        logger.logError(['handleBackMoneyInapp', err], __dirname);
      }

      if (orderInf.salaryStrategy.inapp && orderInf.paymentMethod === 'inapp') {
        PushNotifyManager
          .sendToMember(orderInf.customer.toHexString(), 'Thông báo', `Hệ thống vừa hoàn trả ${orderInf.salaryStrategy.inapp + orderInf.inappDeposit} đ vào tài khoản HeyU của bạn do đơn hàng bị huỷ.`, {link: '', extras: {tabFocus: 2}}, 'profile_update')
      }

      next();
    })
  }

  const otherTask = (next) => {
    let message = `Vì một lý do nào đó cửa hàng ${storeInf.name} đã huỷ đơn hàng của bạn. Bạn vui lòng đặt đơn hàng khác. Xin cảm ơn`;
    if (reason) {
      message = `Cửa hàng ${storeInf.name} đã huỷ đơn hàng của bạn do ${reason}. Bạn vui lòng đặt đơn hàng khác. Xin cảm ơn`;
    }
    PushNotifyManager.sendToMember(orderInf.customer.toHexString(), 'Huỷ đơn hàng', message, { link: 'SBHDetailOrderForShopScreen', extras: {id: orderInf._id, idOrder: orderInf.delivery && orderInf.delivery._id ? orderInf.delivery._id :  ''} }, 'order_update_customer');
    PushNotifyManager.sendToMember(storeInf.member.toString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox');
    if (staffs.length) {
      staffs.map(staff => {
        PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: { id: orderId } }, 'order_update_merchant', 'tickbox');
      })
    }

    OrderStoreLogModel.create({
      member: userId,
      order: orderInf._id,
      store: storeId,
      customer: orderInf.customer,
      merchant: storeInf.member || '',
      type: CONSTANTS.ORDER_LOG.REJECT
    });

    next();
  }

  const updateProduct = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.ORDER.REJECT_SUCCESS
    });

    if (!productNotAvailable || !productNotAvailable.length) {
      return;
    }

    UserProductModel
      .update({
        _id: { $in: productNotAvailable }
      }, {
        $pull: {
          isAvailable: storeId
        }
      }, (err, result) => { })

    ProductModel
      .update({
        _id: { $in: productNotAvailable }
      }, {
        $pull: {
          isAvailable: storeId
        }
      }, (err, result) => {})
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    updateOrderToReject,
    updateOrderShip,
    handleBackMoney,
    handleBackMoneyInapp,
    otherTask,
    updateProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);

    OrderManager.remove(orderId);
    OrderManager.removeJobRePush(orderId);
  });
}
