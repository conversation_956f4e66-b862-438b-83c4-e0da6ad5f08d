const _ = require('lodash')
const ms = require('ms')
const async = require('async')
const OrderStoreLogModel = require('../../models/orderStoreLog');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')
const PushNotifyManager = require('../../job/pushNotify');

module.exports = (req, res) => {
  const orderId = _.get(req, 'body.orderId', '');

  const checkParams = (next) => {
    if (!orderId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        MESSAGES: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getTimeLine = (next) => {
    OrderStoreLogModel
      .find({
        order: orderId
      })
      .sort('createdAt')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    checkParams,
    getTimeLine
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
}
