const _ = require('lodash')
const async = require('async')
const config = require('config')
const ms = require('ms')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const OrderSystemModel = require('../../models/orderSystem');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')

module.exports = (req, res) => {
  const orderIds = _.get(req, 'body.orderIds', '');
  const skip = _.get(req, 'body.skip', 0);
  const limit = _.get(req, 'body.limit', 10);
  const sort = _.get(req, 'body.sort', 0);
  const userId = req.user.id;
  let listOrders = [];

  const checkParams = (next) => {
    if (!orderIds) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getOrderInf = (next) => {
    OrderStoreModel
      .find({
        _id: {$in: orderIds}
      }, '-receiver.phone')
      .populate({
        path: 'delivery',
        populate: [
          {path: 'shipper'}
        ]
      })
      .sort(sort ? '-createdAt' : 'createdAt')
      .skip(skip)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next();
        }

        listOrders = results;

        next();
      });
  }

  const getCart = (next) => {
    if (!listOrders || !listOrders.length) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: listOrders
      });
    }

    async.eachSeries(listOrders, (order, done) => {
      if (!order.cart || !order.cart.length) {
        return done();
      }

      let cart = order.cart;
      const cartIds = cart.map(cart => cart._id);

      ProductModel
        .find({
          _id: { $in: cartIds }
        }, 'name price pricePromote images description')
        .lean()
        .exec((err, results) => {
          if (err) {
            return done(err);
          }

          if (!results || !results.length) {
            return done({
              code: CONSTANTS.CODE.FAIL
            });
          }

          results.some(result => {
            const indexInCart = _.findIndex(cart, item => item._id === result._id.toHexString());

            if (indexInCart !== -1) {
              cart[indexInCart] = _.merge(cart[indexInCart], result);
              order.cart = cart;
              return;
            }
          })

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: listOrders
      })
    })
  }

  async.waterfall([
    checkParams,
    getOrderInf,
    getCart
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
