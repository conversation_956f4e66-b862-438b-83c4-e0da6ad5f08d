const _ = require('lodash')
const async = require('async')
const config = require('config')
const ms = require('ms')
const crypto = require('crypto')
const OrderStoreModel = require('../../models/orderStore');
const StoreModel = require('../../models/store');
const ProductModel = require('../../models/product');
const MemberModel = require('../../models/member');
const ShipperAuthenticationModel = require('../../models/shipperauthentication');
const CONSTANTS = require('../../const')
const MESSAGES = require('../../message')

module.exports = (req, res) => {
  const status = _.get(req, 'body.status', []);
  const skip = _.get(req, 'body.skip', 0);
  const limit = _.get(req, 'body.limit', 10);
  const userId = req.user.id;
  let listOrders = [];

  const getOrderInf = (next) => {
    let query = {
      customer: userId
    }

    if (status && status.length) {
      query.status = {$in: status};
    }

    OrderStoreModel
      .find(query)
      .populate('store', 'name image address member service')
      .populate('delivery')
      .populate('orderType', 'resourceFee food')
      .sort('-createdAt')
      .skip(skip)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next();
        }

        listOrders = results;

        next();
      });
  }

  getShipperInfo = (next) => {
    if (!listOrders || !listOrders.length) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data: listOrders
      });
    }
    async.eachSeries(listOrders, (order, done) => {
      if(order.delivery && order.delivery.shipper) {
        MemberModel
          .findOne({
            _id: order.delivery.shipper
          })
          .lean()
          .exec((err, result) => {
            if(result) {
              ShipperAuthenticationModel
              .findOne({
                member: order.delivery.shipper
              })
              .lean()
              .exec((err, resultAuthen) => {
                if(resultAuthen) {
                  let member = result
                  member.shipperAuthentication = resultAuthen;

                  order.delivery.shipper = member
                  done();
                } else {
                  done();
                }
              })
            } else {
              done();
            }
          })
      } else {
        done();
      }
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next(null)
    })
  }

  const getCart = (next) => {
    if (!listOrders || !listOrders.length) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: listOrders
      });
    }

    async.eachSeries(listOrders, (order, done) => {
      if (!order.cart || !order.cart.length) {
        return done();
      }

      if (order.delivery) {
        const codeForEncrypt = `${order.delivery._id}+${0}`;
        const token = crypto.createHash('md5').update(codeForEncrypt).digest("hex");

        order.delivery.linkOrder = `${config.proxyRequestServer.heyuWeb}/track?order=${order.delivery._id}&index=${0}&token=${token}`;
      }

      let cart = order.cart;
      const cartIds = cart.map(cart => cart._id);

      ProductModel
        .find({
          _id: { $in: cartIds }
        }, 'name price pricePromote images description isAvailable')
        .lean()
        .exec((err, results) => {
          if (err) {
            return done(err);
          }

          if (!results || !results.length) {
            return done({
              code: CONSTANTS.CODE.FAIL
            });
          }

          results.some(result => {
            const indexInCart = _.findIndex(cart, item => item._id.toString() === result._id.toString());
            const isAvailable = result.isAvailable.map(item => item.toString());

            result.isAvailable = 0;
            if (isAvailable.includes(order.store._id.toString())) {
              result.isAvailable = 1;
            }

            if (indexInCart !== -1) {
              cart[indexInCart] = _.merge(cart[indexInCart], result);
              order.cart = cart;
              return;
            }
          })

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: listOrders
      })
    })
  }

  async.waterfall([
    getOrderInf,
    getShipperInfo,
    getCart
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
