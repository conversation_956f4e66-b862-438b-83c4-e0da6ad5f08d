const CategoryStrategy = require('../../models/categoryStrategy');
const locationHelper = require('../../utils/location');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message');

module.exports = (req, res) => {
  let regionName = _.get(req, 'body.regionName', '');
  const location = _.get(req, 'body.location', '');
  const platform = _.get(req, 'body.platform', '')
  const nativeVersion = _.get(req, 'body.nativeVersion', '')

  const determineRegion = (next) => {
    if (!location || !location.lat || !location.lng) {
      return next();
    }

    locationHelper
      .getRegionByLatLng(location, 2, (err, region) => {
        if (err) {
          return next(err);
        }

        regionName = region;

        next();
      })
  }

  const listCategories = (next) => {
    CategoryStrategy
      .find({
        status: 1,
        $or: [{
          'region.allow': 'all',
          'region.deny': {
            $ne: regionName
          }
        }, {
          'region.allow': regionName
        }]
      })
      .sort('order')
      .lean()
      .exec((err, categories) => {
        if (err || !categories || !categories.length) {
          return res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: []
          });
        }

        categories.map(category => {
          if (category.showHome && category.showHome[regionName] && category.showHome[regionName].isOpen) {
            category.showHome = category.showHome[regionName].isOpen;
          } else {
            category.showHome = 0;
          }
        })

        if ((platform === 'ios' && nativeVersion < 2700092) || (platform === 'android' && nativeVersion < 170032)) {
          categories = [
            {
              "_id" : "61b9ccd0484ef26fac6a2eff",
               "name" : "✌ Đặt lại quán cũ",
               "subName" : "Hương vị thân quen",
               "status" : 0.0,
               "createdAt" : 1639706579863.0,
               "updatedAt" : 1639706579863.0,
               "region" : {
                   "allow" : [
                       "all"
                   ]
               }
            }
          ]
        }
        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: categories
        });
      })
  }

  async.waterfall([
    determineRegion,
    listCategories,
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}