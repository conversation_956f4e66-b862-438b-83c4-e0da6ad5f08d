const async = require('async');
const _ = require('lodash');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message')
const utils = require('../../utils/utils')
const listStoreNewHandle = require('./listStoreNew');
const listStoreBoughtHandle = require('./listStoreBought');
const listStorePromoteHandle = require('./listStorePromote');
const listStoreFreeShipHandle = require('./listStoreFreeShip');
const listStoreBreakfastHandle = require('./listStoreBreakfast');
const listStoreLunchHandle = require('./listStoreLunch');
const listStoreJuicesHandle = require('./listStoreJuices');
const listStoreDinnerHandle = require('./listStoreDinner');
const ms = require('ms')

module.exports = (req, res) => {
  const categoryId = _.get(req, 'body.categoryStrategy', '');

  const listStore = (next) => {
    let listHandle = listStoreNewHandle;
    if (categoryId === '61b9cccc484ef26fac6a2efe') {
      listHandle = listStorePromoteHandle;
    } else if (categoryId === '61b9ccd0484ef26fac6a2eff') {
      listHandle = listStoreBoughtHandle;
    } else if (categoryId === '61f374ec612ce442715a7d47') {
      listHandle = listStoreFreeShipHandle;
    } else if (categoryId === '621ede2672e3b6076fa64630') {
      listHandle = listStoreBreakfastHandle;
    } else if (categoryId === '621eea3a72e3b6076fa64638') {
      listHandle = listStoreLunchHandle;
    } else if (categoryId === '62203dc572e3b6076fa64657') {
      listHandle = listStoreJuicesHandle;
    } else if (categoryId === '62203dd672e3b6076fa6465a') {
      listHandle = listStoreDinnerHandle;
    }

    listHandle(req, {
      json: (data) => {
        next(null, data);
      }
    })
  }

  async.waterfall([
    listStore
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
