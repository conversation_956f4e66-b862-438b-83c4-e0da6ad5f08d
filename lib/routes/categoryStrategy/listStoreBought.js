const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const ConfigModel = require('../../models/config');
const CONSTANTS = require('../../const');
const MESSAGES = require('../../message');
const ProductTypeModel = require('../../models/productType');
const StoreModel = require('../../models/store');
const SubTypeModel = require('../../models/subType');
const OrderStore = require('../../models/orderStore');
const PromoteStore = require('../../models/promoteStore');
const PromoteCodeModel = require('../../models/promoteCode');
const locationHelper = require('../../utils/location');
const utils = require('../../utils/utils');
const tool = require('../../utils/tool');


module.exports = (req, res) => {
  const location = req.body.location;
  const page = req.body.page || req.body.skip || 0;
  const limit = 200;
  const skip = page * limit;
  const region = req.body.regionName;
  const category = req.body.category;
  const hasProduct = req.body.requireProduct || '';
  const productType = req.body.productType || '';
  const service = req.body.serviceId || '';
  const newDate = new Date();
  const dayNum = newDate.getDay();
  const startDate = newDate.setHours(0, 0, 0, 0);
  const duration = Date.now() - startDate;
  const userId = _.get(req, 'user.id', '');
  const categoryId = _.get(req, 'body.categoryStrategy', '');
  const showAllStore = _.get(req, 'body.showAllStore', 0);

  let query = { status: 1 };
  if (!location) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS
    });
  }
  let regionName = req.body.regionName;
  let stores;
  let storeIds = [];

  query[`timeSettings.${dayNum}`] = {
    $elemMatch: {
      startTime: {
        $lte: duration
      },
      endTime: {
        $gte: duration
      }
    }
  }

  if (category) {
    query.category = category;
  }

  if (hasProduct) {
    query.hasProduct = hasProduct;
  }

  const fields = 'address description name location timeSettings hasProduct type image background storeNote member service status goliveAt';
  const findProductType = (next) => {

    if (!productType) {
      return next();
    }

    let models = ProductTypeModel;
    if (service === '5d4cea5468731c9493253bb9') {
      models = SubTypeModel;
    }

    models
      .findOne({
        _id: productType
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (result && result.storeType) {
          query['$or'] = [
            { type: result.storeType },
            { productTypes: productType }
          ]
        } else {
          if (service === '5d4cea5468731c9493253bb9') {
            query.subType = productType;
          } else {
            query.productTypes = productType
          }
        }

        next();
      })
  }

  const determineRegion = (next) => {
    locationHelper
      .getRegionByLatLng(location, 2, (err, region) => {
        if (err) {
          return next(err);
        }

        regionName = region;

        next();
      })
  }

  const getPromote = (next) => {
    PromoteCodeModel
      .find({coSponsors: 1, status: 1}, 'condition.store.whiteList condition.time')
      .lean()
      .exec((err, results) => {
        if (err || !results || !results.length) {
          return next();
        }

        const currentTime = Date.now();
        results.map(result => {
          if (utils.checkRange(currentTime, result.condition.time.value)) {
            storeIds = _.union(storeIds, _.get(result, 'condition.store.whiteList', []));
          }
        })

        next();
      })
  }

  const listStore = (next) => {
    query.service = service
    query.golive = 1
    let distance = 5000
    if(regionName.includes('vietnam:hungyen')) {
      distance = 20000
    }
    if(regionName === 'vietnam:haiduong' || regionName === 'vietnam:bacninh' || regionName === 'vietnam:nghean') {
      distance = 25000
    }
    const timeTagNewStore = regionName === 'vietnam:nghean' ? ms('7d') : ms('2d');

    StoreModel
      .getNearest(location, distance, query, fields, { limit, skip }, (err, results) => {
        if (err) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: []
          });
        }

        results.map(result => {
          result.isOpen = 0;

          if (result.member) {
            result.isMerchant = 1;
            delete result.member;
          }

          if (result.goliveAt < 1662397200000 && regionName === 'hcm') {
            result.goliveAt = 1662397200000;
          }

          if (Date.now() - result.goliveAt < timeTagNewStore) {
            result.tagImageHeader = 'https://media.heyu.asia/uploads/mobiles/2022-12-13-tagnewstore.png';
          }

          if (storeIds.includes(result._id.toString())) {
            result.imageFrame = 'https://media.heyu.asia/uploads/mobiles/2022-01-29-frame-free-ship.png';
            result.tags = [{
              title: 'FREESHIP',
              description: 'Giảm 15k phí ship đơn hàng'
            }];
          }

          result.timeSettings && result.timeSettings[dayNum] && result.timeSettings[dayNum].map(time => {
            if (duration >= time.startTime && duration <= time.endTime) {
              result.isOpen = 1;
            }
          })

          if (regionName !== 'hn' && regionName !== 'hcm') {
            result.address = tool.short_address(result.address, regionName);
          }

          // delete result.timeSettings;
          // delete result.status;
        })

        stores = results;

        next();
      });
  }

  const filterStores = (next) => {
    if (!stores || !stores.length) {
      return next({
        code: CONSTANTS.CODE.SUCCESS,
        data: []
      });
    }

    let newStores = [];
    let count = 0;
    const length = stores.length;
    for (let i = 0; i < stores.length; i++) {
      OrderStore
        .findOne({
          store: stores[i]._id,
          customer: userId,
          status: CONSTANTS.ORDER_STATUS.DONE
        })
        .lean()
        .exec((err, result) => {
          if (result) {
            newStores.push(stores[i]);
          }

          count++;
          if (count === length) {
            stores = newStores;
            next();
          }
        })
    }
  }

  const checkPromotion = (next) => {
    if (!stores || !stores.length) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: []
      });
    }

    let newStores = [];
    async.mapLimit(stores, stores.length, (store, done) => {
      PromoteStore
        .find({
          store: store._id,
          status: 1,
          showList: 1
        }, 'name code condition')
        .lean()
        .exec((err, results) => {
          if (results && results.length) {
            if (!store.tags) {
              store.tags = [];
            }

            const currentTime = Date.now();
            results.map((result) => {
              if (utils.checkRange(currentTime, result.condition.time.value)) {
                let title = result.code;
                if (result.code.includes('_')) {
                  title = result.code.split('_')[0];
                }

                store.tags.push({
                  title,
                  description: result.name
                })
              }
            })

            if (store.tagImageHeader === 'https://media.heyu.asia/uploads/mobiles/2022-12-13-tagnewstore.png' && store.tags.length) {
              store.tagImageHeader = '';
            }

            newStores.push(store);
          }

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      stores = newStores;
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: (stores.length >= 3 || showAllStore) ? stores : []
      })
    })
  }

  async.waterfall([
    findProductType,
    determineRegion,
    getPromote,
    listStore,
    filterStores,
    checkPromotion
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
