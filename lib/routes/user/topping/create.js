const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserToppingModel = require('../../../models/userTopping');
const ToppingLogModel = require('../../../models/toppingLog');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const store = _.get(req, 'body.store', '');
  const name = _.get(req, 'body.name', '').trim();
  const price = _.get(req, 'body.price', 0);
  const isAvailable = _.get(req, 'body.isAvailable', 1);
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  const checkParams = (next) => {
    if (!store || !name || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const createTopping = (next) => {
    UserToppingModel
      .create({
        store,
        member: merchant,
        name,
        price,
        isAvailable,
        nameAlias: tool.change_alias(name)
      }, (err, result) => {
        if (err) {
          return next(err)
        }

        ToppingLogModel
          .create({
            type: 0,
            topping: result._id,
            member: userId,
            data: result
          })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: _.pick(result, ['_id', 'name', 'price', 'isAvailable', 'nameAlias'])
        })
      })
  }

  async.waterfall([
    checkParams,
    createTopping
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
