const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserToppingModel = require('../../../models/userTopping');
const ToppingModel = require('../../../models/topping');
const ToppingLogModel = require('../../../models/toppingLog');
const PushNotifyManager = require('../../../job/pushNotify');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const id = _.get(req, 'body.id', '');
  const store = _.get(req, 'body.store', '');
  const name = _.get(req, 'body.name', '').trim();
  const price = _.get(req, 'body.price', 0);
  const isAvailable = _.get(req, 'body.isAvailable', 1);
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  let topping;
  let check;

  const checkParams = (next) => {
    if (!id || !name || !store || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getOldData = (next) => {
    UserToppingModel
      .findOne({
        _id: id
      })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        topping = result;

        next();
      })
  }

  const update = (next) => {
    let objUpdate = {
      name,
      store,
      member: merchant,
      level: 2,
      nameAlias: tool.change_alias(name),
      updatedAt: Date.now()
    };
    check = topping && topping.name === name;

    if (check) {
      objUpdate = {
        price,
        isAvailable,
        updatedAt: Date.now()
      }
    }

    UserToppingModel
      .findOneAndUpdate({
        _id: id
      }, objUpdate)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (check) {
          ToppingModel
            .update({
              _id: id
            }, objUpdate, (err, data) => {
              if (err) {
                return next(err);
              }
            })
        }

        ToppingLogModel
          .create({
            type: 1,
            topping: id,
            member: userId,
            data: result
          })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        });
      })
  }

  async.waterfall([
    checkParams,
    getOldData,
    update
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
