const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserToppingModel = require('../../../models/userTopping');
const ToppingModel = require('../../../models/topping');
const ToppingLogModel = require('../../../models/toppingLog');
const PushNotifyManager = require('../../../job/pushNotify');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const id = _.get(req, 'body.id', '');
  const status = _.get(req, 'body.status', 0);
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const storeId = _.get(req, 'store.id', []);

  const checkParams = (next) => {
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const inactive = (next) => {
    let objUpdate = {
      $pull: {
        store: storeId
      },
      updatedAt: Date.now()
    };

    UserToppingModel
      .findOneAndUpdate({
        _id: id,
        store: storeId
      }, objUpdate)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        ToppingModel
          .findOneAndUpdate({
            _id: id,
            store: storeId
          }, objUpdate)
          .lean()
          .exec((err, data) => {
            if (err) {
              return next(err);
            }

            ToppingLogModel
              .create({
                type: 2,
                topping: id,
                member: userId,
                store: storeId,
                data: result
              })

            next(null, {
              code: CONSTANTS.CODE.SUCCESS
            });
          })
      })
  }

  async.waterfall([
    checkParams,
    inactive
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
