const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const PromoteCodeModel = require('../../../models/promoteCode');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const promoteId = _.get(req, 'body.promoteId', '');
  const storeId = _.get(req, 'body.storeId', '');

  const inactive = (next) => {
    PromoteCodeModel
      .update({
        _id: promoteId
      }, {
        $pull: {
          'condition.store.whiteList': storeId
        }
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Huỷ đăng ký tham gia thành công.'
          }
        })
      })
  }

  async.waterfall([
    inactive
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
