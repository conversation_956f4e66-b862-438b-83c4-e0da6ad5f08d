const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const PromoteStoreModel = require('../../../models/promoteStore');

module.exports = (req, res) => {
  const idPromote = _.get(req, 'body.id', '');
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const store = _.get(req, 'body.myStoreId', '');
  let storeInf;

  const checkParams = (next) => {
    if (!idPromote || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getStore = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: merchant
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        storeInf = result;

        next();
      })
  }

  const inactivePromote = (next) => {
    PromoteStoreModel
      .findOneAndUpdate({
        _id: idPromote,
        store: storeInf._id
      }, {status: 0})
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    getStore,
    inactivePromote
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
