const _ = require('lodash')
const config = require('config')
const util = require('util')
const async = require('async')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ConfigModel = require('../../../models/config');
const UserStoreModel = require('../../../models/userStore');
const locationHelper = require('../../../utils/location');

module.exports = (req, res) => {
  let regionName = _.get(req, 'body.regionName', '');
  const modeApp = _.get(req, 'body.modeApp', '');
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const store = _.get(req, 'body.myStoreId', '');
  let storeInf;

  const checkParams = (next) => {
    if (!modeApp || !regionName || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const checkStoreExists = (next) => {
    UserStoreModel
      .findOne({ _id: store, member: merchant }, 'location')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next();
        }

        storeInf = result;

        next();
      })
  }

  const determineRegion = (next) => {
    if (!storeInf) {
      return next();
    }

    const location = {
      lat: storeInf.location.coordinates[1],
      lng: storeInf.location.coordinates[0]
    }

    locationHelper
      .getRegionByLatLng(location, 2, (err, region) => {
        if (err) {
          return next(err);
        }

        regionName = region;

        next();
      })
  }

  const getConfig = (next) => {
    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.CO_SPONSORS, regionName, (err, result) => {
        if (err) {
          return next(err);
        }

        if (!result || !result.config) {
          return next(null, {
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result.config
        })
      })
  }

  async.waterfall([
    checkParams,
    checkStoreExists,
    determineRegion,
    getConfig
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
