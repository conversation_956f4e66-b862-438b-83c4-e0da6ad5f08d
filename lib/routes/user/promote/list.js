const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const PromoteStoreModel = require('../../../models/promoteStore');
const OrderStoreModel = require('../../../models/orderStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const store = _.get(req, 'body.myStoreId', '');
  let storeInf;
  let promotes = [];

  const checkParams = (next) => {
    if (!merchant || !store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getStore = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: merchant
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        storeInf = result;

        next();
      })
  }

  const listPromote = (next) => {
    PromoteStoreModel
      .find({
        store: storeInf._id,
        status: 1
      }, '-status -createdAt -updatedAt')
      .sort('-createdAt')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        promotes = results;

        next()
      })
  }

  const checkUse = (next) => {
    if (!promotes || !promotes.length) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: promotes || [],
        message: {
          head: 'Lưu ý:',
          body: 'Mã khuyến mãi này là do bạn tự tạo để ưu đãi cho khách hàng khi mua hàng tại gian hàng của bạn\nHeyU sẽ không chi trả chi phí khuyến mãi này\nTrân trọng!'
        }
      })
    }

    async.eachSeries(promotes, (promote, done) => {
      OrderStoreModel
        .count({
          promoteStore: promote._id,
          status: {
            $in: [0, 1, 2, 3, 4]
          }
        })
        .exec((err, count) => {
          if (err) {
            promote.amountUsed = 0;
            return done();
          }

          promote.amountUsed = count;

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: promotes,
        message: {
          head: 'Lưu ý:',
          body: 'Mã khuyến mãi này là do bạn tự tạo để ưu đãi cho khách hàng khi mua hàng tại gian hàng của bạn\nHeyU sẽ không chi trả chi phí khuyến mãi này\nTrân trọng!'
        }
      })
    })
  }

  async.waterfall([
    checkParams,
    getStore,
    listPromote,
    checkUse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
