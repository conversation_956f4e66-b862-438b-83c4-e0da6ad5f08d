const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const PromoteCodeModel = require('../../../models/promoteCode');
const OrderStoreModel = require('../../../models/orderStore');
const utils = require('../../../utils/utils')
const promoteUtil = require('../../../utils/promote');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const storeId = _.get(req, 'body.storeId', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let storeInf;
  let promotes = [];
  let promotesRes = [];

  const checkParams = (next) => {
    if (!merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getStore = (next) => {
    UserStoreModel
      .findOne({
        _id: storeId,
        member: merchant
      }, 'region')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        storeInf = result;

        next();
      })
  }

  const listPromote = (next) => {
    PromoteCodeModel
      .find({
        coSponsors: 1,
        status: 1,
        showListMerchant: 1
      }, '-status -createdAt -updatedAt -coSponsors -alwaysShow -notification -service')
      .sort('-createdAt')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        promotes = results;

        const currentTime = Date.now();
        promotes.map(promote => {
          promote.time = promote.condition.time.value;
          promote.currencyPromote = 'salary';
          promote.joined = 0;
          if (promote.condition && promote.condition.store && promote.condition.store.whiteList && promote.condition.store.whiteList.includes(storeId.toString())) {
            promote.joined = 1;
          }

          let conditionJoin = [];
          if (promote.conditionJoin && promote.conditionJoin.money) {
            conditionJoin.push(`Số dư ví TickBox tối thiểu là: ${promote.conditionJoin.money.from.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1.")}₫`)
          }
          promote.conditionJoin = conditionJoin;

          if (promoteUtil.checkRegion(storeInf.region, promote.condition.order) && utils.checkRange(currentTime, promote.time)) {
            promotesRes.push(promote);
          }

          promote.condition = utils.getMessageCondition(promote);
        })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: promotesRes
        });
      })
  }

  async.waterfall([
    checkParams,
    getStore,
    listPromote
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
