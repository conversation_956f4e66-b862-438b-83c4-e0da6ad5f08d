const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const PromoteStoreModel = require('../../../models/promoteStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const idPromote = _.get(req, 'body.id', '');
  const name = _.get(req, 'body.name', '').trim();
  const icon = _.get(req, 'body.icon', '').trim();
  const code = _.get(req, 'body.code', '').trim().toUpperCase();
  const valuePromote = _.get(req, 'body.valuePromote', 0);
  const time = _.get(req, 'body.time', {});
  const type = _.get(req, 'body.type', '').trim();
  const limited = _.get(req, 'body.limited', 0);
  const maximumUse = _.get(req, 'body.maximumUse', 0);
  const maximumPromote = _.get(req, 'body.maximumPromote', 0);
  const minimumPrice = _.get(req, 'body.minimumPrice', 0);
  const showList = _.get(req, 'body.showList', 0);
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const store = _.get(req, 'body.myStoreId', '');
  let storeInf;

  const blockModify = (next) => {
    next({
      code: CONSTANTS.CODE.FAIL,
      message: {
        head: 'Thông báo',
        body: 'Hiện tại chúng tôi chưa hỗ trợ chức năng chỉnh sửa mã khuyến mãi. Bạn vui lòng xoá mã và tạo lại. Xin cảm ơn.'
      }
    })
  }

  const checkParams = (next) => {
    if (!idPromote || !name || !code || !type || !valuePromote || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const checkCode = (next) => {
    PromoteStoreModel
      .findOne({
        _id: {$ne: idPromote},
        code,
        status: 1
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PROMOTE.PROMOTE_EXISTS
          })
        }

        next();
      })
  }

  const getStore = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: merchant
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        storeInf = result;

        next();
      })
  }

  const modifyPromote = (next) => {
    const objUpdate = {
      name,
      code,
      icon,
      strategy: {
        type,
        value: valuePromote,
        minimumPrice
      },
      condition: {
        time: {
          type: 'range',
          value: {
            from: time.from,
            to: time.to
          }
        },
        used: {
          checkUseByDevices: 0,
          maximum: maximumUse
        }
      },
      showList,
      updatedAt: Date.now()
    }

    if (limited) {
      objUpdate.condition.limited = limited;
    }

    if (type === 'percent' && maximumPromote) {
      objUpdate.strategy.maximum = maximumPromote;
    }

    PromoteStoreModel
      .findOneAndUpdate({
        _id: idPromote,
        store: storeInf._id
      }, objUpdate)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.USER.PERMISSION_DENY
          })
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    blockModify,
    checkParams,
    checkCode,
    getStore,
    modifyPromote
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
