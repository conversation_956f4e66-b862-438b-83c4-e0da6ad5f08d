const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const PromoteCodeModel = require('../../../models/promoteCode');
const MemberModel = require('../../../models/member');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');
const promoteUtil = require('../../../utils/promote');
const utils = require('../../../utils/utils')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const promoteId = _.get(req, 'body.promoteId', '');
  const storeId = _.get(req, 'body.storeId', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let minMoney = 0;
  let promote;

  const checkParams = (next) => {
    if (!merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getPromote = (next) => {
    PromoteCodeModel
      .findOne({
        _id: promoteId,
        status: 1
      }, 'condition conditionJoin')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        const currentTime = Date.now();
        if (!result || !utils.checkRange(currentTime, result.condition.time.value)) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Chương trình giảm giá không tồn tại hoặc hết hiệu lực. Bạn vui lòng đăng ký tham gia chương trình khác. Xin cảm ơn.'
            }
          })
        }

        promote = result;
        if (result.conditionJoin && result.conditionJoin.money) {
          minMoney = result.conditionJoin.money.from;
        }

        next();
      })
  }

  const checkMoney = (next) => {
    MemberModel
      .findOne({
        _id: merchant
      }, 'money')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        if (!result.money || (result.money && result.money < minMoney)) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: `Bạn vui lòng nạp thêm tiền vào tài khoản TickBox để đăng ký tham gia chương trình. Số dư ví TickBox tối thiểu là ${minMoney.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1.")}₫. Xin cảm ơn.`
            }
          })
        }

        next();
      })
  }

  const checkRegion = (next) => {
    UserStoreModel
      .findOne({
        _id: storeId
      }, 'region')
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        if (!promoteUtil.checkRegion(result.region, promote.condition.order)) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: `Mã khuyến mãi không áp dụng cho khu vực của bạn. Xin cảm ơn.`
            }
          })
        }

        next();
      })
  }

  const register = (next) => {
    PromoteCodeModel
      .update({
        _id: promoteId
      }, {
        $addToSet: {
          'condition.store.whiteList': storeId
        }
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Đăng ký tham gia thành công.'
          }
        })
      })
  }

  async.waterfall([
    checkParams,
    getPromote,
    checkMoney,
    checkRegion,
    register
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
