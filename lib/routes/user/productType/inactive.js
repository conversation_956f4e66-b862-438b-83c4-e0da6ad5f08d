const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductTypeModel = require('../../../models/userProductType');
const ProductTypeModel = require('../../../models/productType');
const UserProductModel = require('../../../models/userProduct');
const ProductModel = require('../../../models/product');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const id = req.body.id || '';
  const status = req.body.status || 0
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const storeId = _.get(req, 'store.id', []);

  let productType;

  const checkParams = (next) => {
    if(!id || !merchant){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const updateProduct = (next) => {
    UserProductModel
      .find({
        productType: id
      })
      .lean()
      .exec((err, products) => {
        if (err) {
          return next(err);
        }

        if (products && products.length) {
          products.forEach((product) => {
            if (product.productType && product.productType.length && product.store && product.store.length <= 1) {
              let listProductTypes = [];
              product.productType.map(productType => {
                if (productType._id.toHexString() !== id) {
                  listProductTypes.push(productType._id);
                }
              })

              let objUpdate = {
                productType: listProductTypes
              }

              UserProductModel
                .update({
                  _id: product._id
                }, objUpdate)
                .lean()
                .exec((err, product) => {
                  ProductModel
                    .update({
                      _id: product._id
                    }, objUpdate)
                    .lean()
                    .exec((err, product) => { })
                })
            }
          })
        }

        next();
      })
  }

  const deactive = (next) => {
    UserProductTypeModel
      .findOneAndUpdate({
        _id: id,
        store: storeId,
        member: merchant
      },{
        $pull: {
          store: storeId
        },
        updatedAt: Date.now()
      },(err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }

        ProductTypeModel
          .findOneAndUpdate({
            _id: id,
            store: storeId,
            member: merchant
          }, {
            $pull: {
              store: storeId
            },
            updatedAt: Date.now()
          }, (err, result) => {
            next(null, {
              code: CONSTANTS.CODE.SUCCESS
            })
          })
      })
  }

  async.waterfall([
    checkParams,
    updateProduct,
    deactive
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
