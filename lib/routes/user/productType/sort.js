const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductTypeModel = require('../../../models/userProductType');
const ProductTypeModel = require('../../../models/productType');

module.exports = (req, res) => {
  const productTypes = _.get(req, 'body.productTypes', []);

  const checkParams = (next) => {
    if (!productTypes || !productTypes.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const sortProductType = (next) => {
    async.eachSeries(productTypes, (productType, done) => {
      if (!productType._id) {
        return done();
      }

      UserProductTypeModel
        .update({
          _id: productType._id
        }, {
          order: productType.order || 0
        })
        .lean()
        .exec((err, result) => {
          ProductTypeModel
            .update({
              _id: productType._id
            }, {
              order: productType.order || 0
            })
            .lean()
            .exec((err2, resul2t) => {
              done();
            })
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS
      })
    })
  }

  async.waterfall([
    checkParams,
    sortProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
