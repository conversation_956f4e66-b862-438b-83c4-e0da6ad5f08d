const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductTypeModel = require('../../../models/userProductType');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const name = _.get(req,'body.name','').trim();
  const icon = _.get(req, 'body.icon', '').trim();
  const modifyAll = _.get(req, 'body.modifyAll', 0);
  const userId = _.get(req,'user.id', '')
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let store;
  let storeType;
  let storesArr = [];

  const checkParams = (next) => {
    if(!name || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const checkStore = (next) => {

    UserStoreModel
      .findOne({
        member: merchant,
        focus: 1
      }, '_id type')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.STORE_NOT_CREATE
          })
        }
        store = result._id
        storeType = result.type || '';

        next();
      })
  }

  const listStore = (next) => {
    if (!modifyAll || !storeType) {
      return next();
    }

    UserStoreModel
      .find({
        type: storeType
      }, '_id')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.STORE_NOT_CREATE
          })
        }

        storesArr = results.map(result => result._id);

        next();
      })
  }

  const checkName = (next) => {
    UserProductTypeModel
      .count({
        store: store,
        name: name.trim(),
        status: 1
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(count || name.trim() === 'Khác') {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_EXISTS
          })
        }
        next();
      })
  }

  const createProductType = (next) => {
    let objCreate = {
      name,
      store: modifyAll && storesArr.length ? storesArr : store,
      member: merchant,
      nameAlias: tool.change_alias(name.trim())
    }

    if (icon) {
      objCreate.icon = icon;
    }

    UserStoreModel
      .update({
        _id: store,
        member: merchant
      }, {
        lastUpdatedAt: Date.now()
      })
      .lean()
      .exec((err, result) => { })

    UserProductTypeModel
      .create(objCreate,(err,data) => {
        if(err) {
          return next(err)
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: _.pick(data, ['_id', 'name'])
        })
      })
  }

  async.waterfall([
    checkParams,
    checkStore,
    listStore,
    checkName,
    createProductType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
