const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductTypeModel = require('../../../models/userProductType');
const UserProductModel = require('../../../models/userProduct');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const name = req.body.name || '';
  const id = req.body.id || ''
  const store = req.body.store || ''
  const icon = _.get(req, 'body.icon', '').trim();
  const userId = _.get(req,'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  const checkParams = (next) => {
    if(!id || !name.trim() || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const checkStore = (next) => {
    UserStoreModel
      .count({
        _id: store,
        member: merchant
      })
      .lean()
      .exec((err, count) => {
        if (err) {
          return next(err)
        }
        if (!count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }
        next();
      })
  }

  const checkName = (next) => {
    UserProductTypeModel
      .count({
        store: store,
        name: name.trim(),
        status: 1
      })
      .lean()
      .exec((err, count) => {
        if (err) {
          return next(err)
        }

        if (count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_EXISTS
          })
        }

        next();
      })
  }

  const update = (next) => {

    const objUpdate = {
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()),
      updatedAt: Date.now()
    }

    if (store) {
      objUpdate.store = store;
    }

    if (icon) {
      objUpdate.icon = icon;
    }

    UserStoreModel
      .update({
        _id: store,
        member: merchant
      }, {
        lastUpdatedAt: Date.now()
      })
      .lean()
      .exec((err, result) => { })

    UserProductTypeModel
      .findOneAndUpdate({
        _id: id,
        member: merchant
      },objUpdate,(err, result) => {
        if(err) {
          return next(err);
        }

        if (!result) {
          return next(null,{
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }

        next();
      })
  }

  const updateProduct = (next) => {
    UserProductModel
      .update({
        store: store,
        productType: id,
        level: {$in: [-1, -2]}
      }, {
        level: 2,
        updatedAt: Date.now()
      }, { multi: true })
      .lean()
      .exec((err, result) => { })

    next(null, {
      code: CONSTANTS.CODE.SUCCESS
    })
  }

  async.waterfall([
    checkParams,
    checkStore,
    checkName,
    update,
    updateProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
