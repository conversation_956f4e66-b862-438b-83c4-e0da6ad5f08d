const _ = require('lodash')
const config = require('config')
const util = require('util')
const async = require('async')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ConfigModel = require('../../../models/config');
const UserStoreModel = require('../../../models/userStore');

module.exports = (req, res) => {
  const regionName = _.get(req, 'body.regionName', '');
  const modeApp = _.get(req, 'body.modeApp', '');
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const store = _.get(req, 'body.myStoreId', '');
  let businessType = [];

  const checkParams = (next) => {
    if (!modeApp || !regionName || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    next();
  }

  const getBusinessType = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: merchant
      }, 'businessType')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result && result.businessType && result.businessType.length) {
          businessType = result.businessType.map(businessType => businessType.toHexString());
        }

        next();
      })
  }

  const getConfig = (next) => {
    if (businessType.includes('6155193fd5f7a54fb77b4ac7') || businessType.includes('61551929d5f7a54fb77b4ac4')) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          isOpen: 1
        }
      })
    }

    ConfigModel
      .get(CONSTANTS.CONFIG_TYPE.ADD_PRODUCT_TYPE, regionName, (err, result) => {
        if (err) {
          return next(err);
        }

        if (!result || !result.config) {
          return next(null, {
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result.config
        })
      })
  }

  async.waterfall([
    checkParams,
    getBusinessType,
    getConfig
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
