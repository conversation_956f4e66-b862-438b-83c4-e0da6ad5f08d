const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const UserProductModel = require('../../../models/userProduct');
const UserProductTypeModel = require('../../../models/userProductType');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');
const locationHelper = require('../../../utils/location');

module.exports = (req, res) => {

  const limit = _.get(req, 'body.limit', 0);
  const skip = _.get(req, 'body.skip', 0);
  const sort = _.get(req, 'body.sort', 1);
  const name = _.get(req, 'body.name', '');
  const store = _.get(req, 'body.store', '');
  const all = _.get(req, 'body.all', 0);
  const service = _.get(req, 'body.service', '');
  const location = _.get(req, 'body.location', '');
  let region = req.body.regionName;
  let productTypes = [];
  let productTypeParent = [];

  const checkParams = (next) => {
    if (!store || !service) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const findRegion = (next) => {
    if (!location) {
      return next();
    }


    locationHelper
      .getRegionByLatLng(location, 2, (err, regionName) => {
        if (err) {
          return next(err);
        }

        region = regionName;

        next();

      })
  }

  const getStore = (next) => {
    UserStoreModel
      .findOne({
        _id: store
      })
      .populate('businessType')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result || !result.businessType) {
          return next({
            code: CONSTANTS.CODE.FAIL
          });
        }

        if (result.businessType && result.businessType.length) {
          result.businessType.map(businessType => {
            if (businessType.productType) {
              productTypeParent.push(businessType.productType.toHexString());
            }
          })
        }

        next();
      })
  }

  const listProductType = (next) => {
    let objSearch = {
      status: 1,
      $or: [{
        store
      }, {
        default: 1,
        service,
        $or: [{
          'region.allow': 'all',
          'region.deny': {
            $ne: region
          }
        }, {
          'region.allow': region
        }]
      }]
    }

    if (productTypeParent && productTypeParent.length) {
      objSearch.parent = {$in: productTypeParent};
    }

    if(name && name.trim()) {
      objSearch.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
    }

    const options = {
      sort: 'order'
    }

    if (limit) {
      options.limit = limit;
      options.skip = skip;
    }

    UserProductTypeModel
      .find(objSearch,"",options)
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }

        productTypes = results;

        next();
      })
  }

  const countProducts = (next) => {
    async.eachSeries(productTypes, (productType, done) => {
      UserProductModel
        .count({
          productType: productType._id,
          store,
          status: 1
        })
        .lean()
        .exec((err, count) => {
          if (err) {
            return done(err);
          }

          productType.quantityProducts = count;

          done();
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      if (!all) {
        for (let index = 0; index < productTypes.length; index++) {
          const element = productTypes[index];

          if (!element.quantityProducts) {
            productTypes.splice(index, 1);
            index--;
          }
        }
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: productTypes
      })
    })
  }

  async.waterfall([
    checkParams,
    findRegion,
    getStore,
    listProductType,
    countProducts
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
