const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const UserStoreModel = require('../../../models/userStore');
const UserProductModel = require('../../../models/userProduct');
const UserProductTypeModel = require('../../../models/userProductType');
const PhoneTeleModel = require('../../../models/phoneTele');
const ProductLogModel = require('../../../models/productLog');
const tool = require('../../../utils/tool');
const PushNotifyManager = require('../../../job/pushNotify');

module.exports = (req, res) => {

  let name = _.get(req,'body.name','').trim();
  const price = _.get(req,'body.price',0);
  const images = _.get(req,'body.images',[]);
  const productType = _.get(req,'body.productType', []);
  const store = _.get(req,'body.store','');
  const isAvailable = _.get(req, 'body.isAvailable', 0);
  const unit = _.get(req, 'body.unit', '');
  const topping = _.get(req,'body.topping', []);
  const modifyAll = _.get(req, 'body.modifyAll', 0);
  const description = _.get(req,'body.description','');
  const quantitative = _.get(req, 'body.quantitative', '').trim();
  let pricePromote = _.get(req,'body.pricePromote',0);
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let productTypeIds = [];
  let idProduct;
  let service;
  let region;
  let storeType;
  let storesArr = [store];

  const checkParams = (next) => {
    if(!name || !store || !productType.length || !price || !images.length || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    if(pricePromote > price) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.PRICE_PROMOTE
      })
    }

    if(!pricePromote) {
      pricePromote = price
    }

    next();
  }

  const checkStore = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        // status: 1
      })
      .populate('businessType')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }

        service = result.service.toHexString();
        region = result.region;
        storeType = result.type || '';
        if (result.businessType && result.businessType.length) {
          result.businessType.map(businessType => {
            if (businessType.productType) {
              productTypeIds.push(businessType.productType.toHexString());
              // productType.push(result.businessType.productType);
            }
          })
        }

        next();
      })
  }

  const listStore = (next) => {
    if (!modifyAll || !storeType) {
      return next();
    }

    UserStoreModel
      .find({
        type: storeType
      }, '_id')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.STORE_NOT_CREATE
          })
        }

        storesArr = results.map(result => result._id);

        next();
      })
  }

  const checkProductType = (next) => {
    if (productTypeIds.length) {
      return next();
    }

    let models = ProductTypeModel;
    let query = {
      _id: {$in: productType},
      status: 1
    }

    UserProductTypeModel
      .find(query)
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }
        if(!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }

        results.map(result => {
          if (result.default) {
            productTypeIds.push(result._id);
          }
        });

        next();
      })
  }

  const checkName = (next) => {
    UserProductModel
      .count({
        store: store,
        productType: productType,
        name: name,
        status: 1
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_EXISTS
          })
        }
        next();
      })
  }

  const createProduct = (next) => {
    UserProductModel
      .create({
        name,
        price,
        pricePromote,
        images,
        productType,
        isAvailable: isAvailable ? storesArr : [],
        unit,
        description,
        store: modifyAll && storesArr.length ? storesArr : store,
        member: merchant,
        quantitative,
        nameAlias: tool.change_alias(name),
        topping
      },(err,result) => {
        if(err) {
          return next(err)
        }

        idProduct = result._id;

        ProductLogModel
          .create({
            action: 'Tạo mới sản phẩm',
            level: 0,
            reason: '',
            member: merchant,
            product: idProduct,
            region
          })
          PhoneTeleModel.findOneAndUpdate({
            member: userId,
            tickboxStatus: {$ne:2}
          }, {
            schedule:{
              callAt: Date.now()
            },
            statusJob: 1
          })
            .lean()
            .exec((error, phoneTele)=>{})

        PushNotifyManager.sendToMember(merchant, '', '', { link: '', extras: {} }, 'product_update', 'tickbox');
        if (staffs.length) {
          staffs.map(staff => {
            PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: {} }, 'product_update', 'tickbox');
          })
        }

        next()
      })
  }

  const updateStore = (next) => {
    next(null,{
      code: CONSTANTS.CODE.SUCCESS
    })

    UserStoreModel
      .update({
        _id: store
      }, {
        hasProduct: 1,
        $addToSet: {
          productTypes: {
            $each: productTypeIds
          },
          productSearch: {
            _id: idProduct,
            nameAlias: tool.change_alias(name)
          }
        },
        levelStatusProduct: 1,
        updatedAt: Date.now(),
        lastUpdatedAt: Date.now()
      })
      .lean()
      .exec((err, result) => { })
  }

  async.waterfall([
    checkParams,
    checkStore,
    listStore,
    checkProductType,
    checkName,
    createProduct,
    updateStore
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
