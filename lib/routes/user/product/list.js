const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductModel = require('../../../models/userProduct');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const limit = 100;
  const skip = _.get(req, 'body.skip', 0);
  const sort = _.get(req, 'body.sort', 1);
  const name = _.get(req, 'body.name', '');
  const store = _.get(req, 'body.store', '');
  const productType = _.get(req, 'body.productType', []);
  const existsProductType = _.get(req, 'body.existsProductType', []);
  const level = _.get(req, 'body.level', []);

  const listProduct = (next) => {
    if(!store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.NOT_FOUND_STORE
      })
    }

    let objSearch = {
      store,
      status: 1
    }

    if(name && name.trim()) {
      objSearch.nameAlias = new RegExp(`${tool.change_alias(name.trim())}`)
    }

    if(productType && productType.length) {
      objSearch.productType = {
        $in: productType
      };
    }

    if(existsProductType && existsProductType.length) {
      objSearch.productType = {
        $nin: existsProductType
      };
    }

    if (level.length) {
      objSearch.level = {
        $in: level
      };
    }

    const options = {
      limit,
      skip,
      sort: 'order'
    }
    UserProductModel
      .find(objSearch,"",options)
      .populate('productType', '-status -member')
      .populate({
        path: 'topping',
        match: { status: 1 },
        populate: [{
          path: 'topping',
          match: { status: 1 }
        }]
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }

        results.forEach((item, i) => {
          const isAvailable = item.isAvailable.map(value => value.toString());

          item.isAvailable = 0;
          if (isAvailable.includes(store)) {
            item.isAvailable = 1;
          }
        })

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    listProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
