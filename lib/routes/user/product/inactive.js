const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductModel = require('../../../models/userProduct');
const ProductModel = require('../../../models/product');
const ProductLogModel = require('../../../models/productLog');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');
const PushNotifyManager = require('../../../job/pushNotify');

module.exports = (req, res) => {

  const id = req.body.id || '';
  const status = req.body.status || 0;
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const storeId = _.get(req, 'store.id', []);
  const modifyAll = _.get(req, 'body.modifyAll', 0);
  let region = '';
  let objUpdate = {
    $pull: {
      store: storeId
    },
    updatedAt: Date.now()
  }
  if (modifyAll) {
    objUpdate = {
      status: 0,
      updatedAt: Date.now()
    }
  }

  const checkParams = (next) => {
    if(!id || !merchant){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }


  const deactiveUserProduct = (next) => {
    UserProductModel
      .findOneAndUpdate({
        _id: id,
        store: storeId,
        member: merchant
      }, objUpdate,(err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PRODUCT.PRODUCT_NOT_EXISTS
          })
        }

        next()
      })
  }

  const updateStore = (next) => {
    let query = {
      member: merchant,
      store: storeId
    }
    if (modifyAll) {
      query = {
        member: merchant
      }
    }

    UserStoreModel
      .update(query, {
        $pull: {
          productSearch: { _id: id }
        }
      }, {multi: true})
      .exec((err, result) => {
        next();
      })
  }

  const deactiveProduct = (next) => {
    ProductModel
      .findOneAndUpdate({
        _id: id,
        store: storeId,
        member: merchant
      }, objUpdate, (err, result) => {
        if (err) {
          return next(err);
        }

        PushNotifyManager.sendToMember(merchant, '', '', { link: '', extras: {} }, 'product_update', 'tickbox');
        if (staffs.length) {
          staffs.map(staff => {
            PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: {} }, 'product_update', 'tickbox');
          })
        }

        next()
      })
  }

  const updateLevelStatusProduct = next => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS
    })
    if (storeId) {
      UserProductModel.findOne({
        store: storeId,
        level: {$in: [0, 2]},
        status: 1
      })
      .select('store level')
      .lean()
      .exec((err, result)=>{
        UserStoreModel
          .findOneAndUpdate({_id: storeId}, {levelStatusProduct: result ? 1 : 0}, {})
          .lean()
          .exec((err, res)=>{
            ProductLogModel
              .create({
                action: 'Xoá sản phẩm',
                level: 2,
                reason: '',
                member: merchant,
                product: id,
                region: res.region
              })
          })
      })
    };
  }

  async.waterfall([
    checkParams,
    deactiveProduct,
    deactiveUserProduct,
    updateStore,
    updateLevelStatusProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
