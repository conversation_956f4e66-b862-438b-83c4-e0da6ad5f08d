const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const UserStoreModel = require('../../../models/userStore');
const UserProductModel = require('../../../models/userProduct');
const ProductModel = require('../../../models/product');
const UserProductTypeModel = require('../../../models/userProductType');
const PhoneTeleModel = require('../../../models/phoneTele');
const ProductLogModel = require('../../../models/productLog');
const tool = require('../../../utils/tool');
const PushNotifyManager = require('../../../job/pushNotify');

module.exports = (req, res) => {

  const id = _.get(req,'body.id','')
  const name = _.get(req,'body.name','').trim();
  const price = _.get(req,'body.price',0);
  const images = _.get(req,'body.images',[]);
  const productType = _.get(req,'body.productType', []);
  const store = _.get(req,'body.store','');
  const isAvailable = _.get(req, 'body.isAvailable', 0);
  const unit = _.get(req, 'body.unit', '');
  const topping = _.get(req,'body.topping', []);
  const modifyAll = _.get(req, 'body.modifyAll', 0);
  const description = _.get(req,'body.description','');
  const quantitative = _.get(req, 'body.quantitative', '');
  let pricePromote = _.get(req,'body.pricePromote',0);
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  let oldProduct;
  let productTypeIds = [];
  let service;
  let region;
  let product;
  let check;
  let storeType;
  let storesArr = [];

  const checkParams = (next) => {
    if(!id || !name || !store || !productType.length || !price || !images.length || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    if(pricePromote > price) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.PRICE_PROMOTE
      })
    }

    if(!pricePromote) {
      pricePromote = price
    }

    next();
  }

  const findOld = (next) => {
    UserProductModel
      .findOne({
        _id: id,
        member: merchant
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_NOT_EXISTS
          })
        }
        oldProduct = result
        next();
      })
  }

  const checkStore = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: merchant
      })
      .populate('businessType')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }
        if (!result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }

        service = result.service.toHexString();
        region = result.region;
        storeType = result.type || '';
        if (result.businessType && result.businessType.length) {
          result.businessType.map(businessType => {
            if (businessType.productType) {
              productTypeIds.push(businessType.productType.toHexString());
              // productType.push(result.businessType.productType);
            }
          })
        }

        next();
      })
  }

  const listStore = (next) => {
    if (!modifyAll || !storeType) {
      return next();
    }

    UserStoreModel
      .find({
        type: storeType
      }, '_id')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.STORE_NOT_CREATE
          })
        }

        storesArr = results.map(result => result._id);

        next();
      })
  }

  const checkProductType = (next) => {
    if (productTypeIds.length) {
      return next();
    }

    let models = ProductTypeModel;
    let query = {
      _id: {$in: productType},
      status: 1
    }

    UserProductTypeModel
      .find(query)
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }
        if(results.length < productType.length) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_TYPE_NOT_EXISTS
          })
        }

        results.map(result => {
          if (result.default) {
            productTypeIds.push(result._id);
          }
        });

        next();
      })
  }

  const checkNameProduct = (next) => {
    if (oldProduct.name === name) {
      return next();
    }

    UserProductModel
      .count({
        store: store,
        name: name,
        status: 1
      })
      .lean()
      .exec((err, count) => {
        if(err) {
          return next(err)
        }
        if(count) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.PRODUCT_EXISTS
          })
        }
        next();
      })
  }

  const getOldData = (next) => {
    UserProductModel
      .findOne({
        _id: id
      })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next();
        }

        product = result;

        next();
      })
  }

  const update = (next) => {
    let objUpdate = {
      name,
      price,
      pricePromote,
      images,
      productType,
      unit,
      description,
      member: merchant,
      level: 2,
      quantitative,
      nameAlias: tool.change_alias(name),
      updatedAt: Date.now(),
      topping
    };
    const productTypeOld = product.productType.map(productType => productType.toHexString());
    const toppingOld = product.topping && product.topping.map(topping => topping.toHexString()) || [];
    const toppingNew = topping.map(topping => topping._id.toString()) || [];
    check = product && product.name === name && _.isEqual(product.images, images) && product.quantitative === quantitative && product.description === description && _.isEqual(productTypeOld, productType) && (_.isEqual(toppingOld, toppingNew) || !toppingOld.length);

    if (check) {
      objUpdate = {
        price,
        pricePromote,
        unit,
        updatedAt: Date.now(),
        topping
      }
    }

    if (isAvailable) {
      objUpdate['$addToSet'] = {
        isAvailable: modifyAll && storesArr.length ? {$each: storesArr} : store
      }
    } else {
      objUpdate['$pull'] = {
        isAvailable: modifyAll && storesArr.length ? {$in: storesArr} : store
      }
    }

    UserProductModel
      .update({
        _id: id
      }, objUpdate, (err, result) => {
        if (err) {
          return next(err);
        }

        if (check) {
          ProductModel
            .update({
              _id: id
            }, objUpdate, (err, result) => {
              if (err) {
                return next(err);
              }

              next();
            })
        } else {
          ProductLogModel
            .create({
              action: 'Cập nhật sản phẩm',
              level: 2,
              reason: '',
              member: userId,
              product: id,
              region
            })
          PhoneTeleModel.findOneAndUpdate({
            member: userId,
            tickboxStatus: {$ne:2}
          }, {
            schedule:{
              callAt: Date.now()
            },
            statusJob: 1
          })
            .lean()
            .exec((error, phoneTele)=>{})
          next();
        }
      })
  }

  const updateStore = (next) => {
    PushNotifyManager.sendToMember(merchant, '', '', { link: '', extras: {} }, 'product_update', 'tickbox');
    if (staffs.length) {
      staffs.map(staff => {
        PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: {} }, 'product_update', 'tickbox');
      })
    }

    next(null,{
      code: CONSTANTS.CODE.SUCCESS
    })

    let objUpdate1 = {
      $addToSet: {
        productTypes: {
          $each: productTypeIds
        }
      },
      updatedAt: Date.now(),
      lastUpdatedAt: Date.now()
    }
    let objUpdate2 = {
      'productSearch.$.nameAlias': tool.change_alias(name),
      updatedAt: Date.now(),
      lastUpdatedAt: Date.now()
    }

    if (!check) {
      objUpdate1.levelStatusProduct = 1;
      objUpdate2.levelStatusProduct = 1;
    }

    UserStoreModel
      .update({
        _id: store
      }, objUpdate1)
      .lean()
      .exec((err, result) => { })

    UserStoreModel
      .update({
        _id: store,
        'productSearch._id': id
      }, objUpdate2)
      .lean()
      .exec((err, result) => { })
  }

  async.waterfall([
    checkParams,
    findOld,
    checkStore,
    listStore,
    checkProductType,
    checkNameProduct,
    getOldData,
    update,
    updateStore
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
