const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductModel = require('../../../models/userProduct');
const ProductModel = require('../../../models/product');

module.exports = (req, res) => {
  const products = _.get(req, 'body.products', []);

  const checkParams = (next) => {
    if (!products || !products.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const sortProduct = (next) => {
    async.eachSeries(products, (product, done) => {
      if (!product._id) {
        return done();
      }

      UserProductModel
        .update({
          _id: product._id
        }, {
          order: product.order || 0
        })
        .lean()
        .exec((err, result) => {
          ProductModel
            .update({
              _id: product._id
            }, {
              order: product.order || 0
            })
            .lean()
            .exec((err, result) => {
              done();
            })
        })
    }, (err, result) => {
      if (err) {
        return next(err)
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS
      })
    })
  }

  async.waterfall([
    checkParams,
    sortProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
