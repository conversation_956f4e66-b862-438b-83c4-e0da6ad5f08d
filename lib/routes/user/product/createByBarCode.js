const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const UserStoreModel = require('../../../models/userStore');
const UserProductModel = require('../../../models/userProduct');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const name = _.get(req, 'body.name', '').trim();
  const price = _.get(req, 'body.price', 0);
  const images = _.get(req, 'body.images', []);
  const productType = _.get(req, 'body.productType', []);
  const store = _.get(req, 'body.store', '');
  const isAvailable = _.get(req, 'body.isAvailable', 0);
  const unit = _.get(req, 'body.unit', '');
  const barCode = _.get(req, 'body.barCode', {});

  const description = _.get(req, 'body.description', '');
  let pricePromote = _.get(req, 'body.pricePromote', 0);
  const userId = _.get(req, 'user.id', '');
  let productTypeIds = [];
  let idProduct;

  const checkParams = (next) => {
    console.log('haha:barCode', barCode);

    next(null, {
      code: 200,
      data: {
        name: '',
        images: [],
        description: '',
        unit: '',
        price: 0,
        barCode: barCode.data
      }
    })
  }

  async.waterfall([
    checkParams
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
