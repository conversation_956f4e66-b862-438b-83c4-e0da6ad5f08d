const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const SubTypeModel = require('../../../models/subType');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const region = _.get(req, 'body.regionName', '');

  const list = (next) => {
    SubTypeModel
      .find({
        status: 1
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.FAIL
          })
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        });
      })
  }

  async.waterfall([
    list
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
