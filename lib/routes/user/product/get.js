const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductModel = require('../../../models/userProduct');

module.exports = (req, res) => {
  const productId = _.get(req, 'body.id', '');
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const store = _.get(req, 'body.myStoreId', '');

  const checkParams = (next) => {
    if (!productId || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getProduct = (next) => {
    UserProductModel
      .findOne({
        _id: productId,
        status: 1,
        member: merchant
      })
      .populate('productType', '-status -member')
      .populate({
        path: 'topping',
        match: { status: 1 },
        populate: [{
          path: 'topping',
          match: { status: 1 }
        }]
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        const isAvailable = result.isAvailable.map(value => value.toString());

        result.isAvailable = 0;
        if (isAvailable.includes(store)) {
          result.isAvailable = 1;
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }

  async.waterfall([
    checkParams,
    getProduct
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
