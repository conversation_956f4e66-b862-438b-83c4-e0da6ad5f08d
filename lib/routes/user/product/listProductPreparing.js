const rp = require('request-promise');
const ms = require('ms')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserProductModel = require('../../../models/userProduct');
const UserStoreModel = require('../../../models/userStore');
const OrderStore = require('../../../models/orderStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const storeId = _.get(req, 'body.storeId', '');
  let data = []

  const checkParams = (next) => {
    if (!storeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const listOrders = (next) => {
    OrderStore
      .find({
        store: storeId,
        status: CONSTANTS.ORDER_STATUS.PREPARE_PRODUCT,
        updatedAt: {$gte: Date.now() - ms('5h')}
      }, 'cart code updatedAt')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results && !results.length) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: []
          });
        }

        results.map(order => {
          order.cart.map(cart => {
            const indexData = _.findIndex(data, (value) => value._id.toString() === cart._id.toString());
            if (indexData === -1) {
              cart.lastUpdatedAt = order.updatedAt;
              cart.orderList = [order._id.toString()];
              data.push(_.pick(cart, ['_id', 'name', 'quantity', 'images', 'orderList', 'lastUpdatedAt']));
            } else {
              cart.quantity += data[indexData].quantity;
              cart.lastUpdatedAt = order.updatedAt;
              cart.orderList = data[indexData].orderList;
              cart.orderList.push(order._id.toString());
              data[indexData] = _.pick(cart, ['_id', 'name', 'quantity', 'images', 'orderList', 'lastUpdatedAt']);
            }
          })
        })

        data.sort((a, b) => (a.lastUpdatedAt > b.lastUpdatedAt) ? 1 : ((b.lastUpdatedAt > a.lastUpdatedAt) ? -1 : 0));

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: data
        });
      })
  }

  async.waterfall([
    checkParams,
    listOrders
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
