const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const Member = require('../../../models/member');
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const code = _.get(req, 'body.code', '');
  let userId;

  const checkParams = (next) => {
    if (!code) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    next();
  }

  const getMember = (next) => {
    Member
      .findOne({
        code
      }, '_id')
      .lean()
      .exec((err, member) => {
        if (err || !member) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        userId = member._id;

        next();
      })
  }

  const getStore = (next) => {
    UserStoreModel
      .findOne({
        member: userId
      })
      .populate('businessType', 'name icon')
      .populate('subType', 'name icon')
      .select('-nameAlias -member')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      })
  }

  async.waterfall([
    checkParams,
    getMember,
    getStore
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
