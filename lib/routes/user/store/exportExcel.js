// const rp = require('request-promise');
// const CONSTANTS = require('../../../const')
// const MESSAGES = require('../../../message')
// const config = require('config')
// const _ = require('lodash')
// const async = require('async')
// const UserStoreModel = require('../../../models/userStore');
// const BusinessTypeModel = require('../../../models/businessType');
// const StoreLogModel = require('../../../models/storeLog');
// const PhoneTeleModel = require('../../../models/phoneTele');
// const PhoneTeleLogModel = require('../../../models/phoneTeleLog');
// const MemberModel = require('../models/member');
// const tool = require('../../../utils/tool');
// const locationHelper = require('../../../utils/location');
// const validator = require('validator')
// const ms = require('ms')

// module.exports = (req, res) => {
//   const from = _.get(req, 'body.from', Date.now() - ms('30d'));
//   const to = _.get(req, 'body.to', Date.now());
//   const branchMode = _.get(req, 'body.branchMode', 0);
//   const email = _.get(req, 'body.email', '');
//   const userId = _.get(req, 'user.id', '');

//   let region
//   let service;

//   const checkParams = (next) => {
//     if (!email) {
//       return next({
//         code: CONSTANTS.CODE.WRONG_PARAMS,
//         message: MESSAGES.SYSTEM.WRONG_PARAMS
//       })
//     }

//     next();
//   }

//   const checkStoreExists = (next) => {
//     UserStoreModel
//       .findOne({
//         member: userId
//       })
//       .lean()
//       .exec((err, result) => {
//         if (result) {
//           return next({
//             code: CONSTANTS.CODE.SYSTEM_ERROR
//           });
//         }

//         next();
//       })
//   }

//   const determineRegion = (next) => {
//     const location = {
//       lat: req.body.location.coordinates[1],
//       lng: req.body.location.coordinates[0]
//     }

//     locationHelper
//       .getRegionByLatLng(location, 2, (err, regionName) => {
//         if (err) {
//           return next(err);
//         }

//         region = regionName;

//         next();

//       })
//   }

//   const getService = (next) => {
//     BusinessTypeModel
//       .findOne({
//         _id: businessType,
//         status: 1
//       })
//       .lean()
//       .exec((err, result) => {
//         if (err) {
//           return next(err);
//         }

//         if (!result) {
//           return next({
//             code: CONSTANTS.CODE.SYSTEM_ERROR,
//             message: MESSAGES.SYSTEM.ERROR
//           })
//         }

//         service = result.service;
//         next();
//       })
//   }

//   const createStore = (next) => {
//     UserStoreModel
//       .create({
//         name: name.trim(),
//         nameAlias: tool.change_alias(name.trim()),
//         phone,
//         member: userId,
//         image,
//         background,
//         address,
//         subAddress,
//         description,
//         location,
//         workingTime,
//         businessType,
//         subType,
//         service,
//         region,
//         messageGoLive: 'HeyU sẽ duyệt toàn bộ thông tin sản phẩm, thông tin cửa hàng bạn cung cấp. Vì thế hãy cập nhật hết sản phẩm của Gian Hàng để đạt yêu cầu đưa lên các mục mua sắm của HeyU.'
//       }, (err, result) => {
//         if (err) {
//           return next(err)
//         }

//         StoreLogModel
//           .create({
//             action: 'Tạo mới cửa hàng',
//             level: 0,
//             reason: '',
//             member: userId,
//             store: result._id,
//             region
//           })

//         PhoneTeleModel.findOneAndUpdate({
//           member: userId
//         }, {
//           member: userId,
//           tickboxStatus: 1,
//           schedule: {
//             callAt: Date.now()
//           },
//           updatedAt: Date.now(),
//           statusJob: 1
//         }, { upsert: true, setDefaultsOnInsert: true, new: true })
//           .populate('member', 'phone', MemberModel)
//           .lean()
//           .exec((error, phoneTele) => {
//             PhoneTeleLogModel.create({
//               phone: _.get(phoneTele, 'member.phone'),
//               supporter: _.get(phoneTele, 'supporter'),
//               currentActivityStatus: _.get(phoneTele, 'currentActivityStatus'),
//               lastActivityStatus: _.get(phoneTele, 'lastActivityStatus'),
//               departments: _.get(phoneTele, 'departments'),
//               createdAt: Date.now(),
//               region: _.get(phoneTele, 'region'),
//               tickboxStatus: 1,
//               type: 21,
//             })
//           })


//         next(null, {
//           code: CONSTANTS.CODE.SUCCESS
//         })
//       })
//   }

//   async.waterfall([
//     checkParams,
//     checkStoreExists,
//     determineRegion,
//     getService,
//     createStore
//   ], (err, data) => {
//     err && _.isError(err) && (data = {
//       code: CONSTANTS.CODE.SYSTEM_ERROR,
//       message: MESSAGES.SYSTEM.ERROR
//     });

//     res.json(data || err);
//   })
// }
