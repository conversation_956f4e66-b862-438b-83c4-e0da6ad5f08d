const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const BusinessTypeModel = require('../../../models/businessType');
const StoreLogModel = require('../../../models/storeLog');
const PhoneTeleModel = require('../../../models/phoneTele');
const PhoneTeleLogModel = require('../../../models/phoneTeleLog');
const MemberModel = require('../../../models/member');
const tool = require('../../../utils/tool');
const locationHelper = require('../../../utils/location');
const validator = require('validator')

module.exports = (req, res) => {

  const name = _.get(req,'body.name','');
  const phone = _.get(req,'body.phone',[]);
  const userId = _.get(req,'user.id','');

  const address = _.get(req,'body.address','');
  const subAddress = _.get(req, 'body.subAddress', '');
  const location = _.get(req,'body.location',{});
  const workingTime = _.get(req,'body.workingTime',{});

  const image = _.get(req,'body.image','');
  const background = _.get(req,'body.background','');
  const description = _.get(req,'body.description','');
  const businessType = _.get(req, 'body.businessType', []);
  const subType = _.get(req, 'body.subType', []);

  let region
  let service;

  const checkParams = (next) => {
    if(!name || !phone.length || !userId || !address || !location || !_.has(location,'coordinates') || !workingTime || !businessType.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    if(!phone[0].startsWith('0') && phone[0].length < 10) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.PHONE_NOT_VALID
      })
    }
    // if(workingTime.from > workingTime.to) {
    //   return next({
    //     code: CONSTANTS.CODE.WRONG_PARAMS,
    //     message: MESSAGES.PRODUCT.WORKING_TIME
    //   })
    // }

    if (!image || image === 'https://media.heyu.asia/uploads/new-img-service/2021-10-01-defaultAvatarStore.png' || !background || background === 'https://media.heyu.asia/uploads/new-img-service/2021-09-30-backgroundStore.jpg') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.ADD_IMAGE
      })
    }

    next();
  }

  const checkStoreExists = (next) => {
    UserStoreModel
      .findOne({
        member: userId
      })
      .lean()
      .exec((err, result) => {
        if (result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }

        next();
      })
  }

  const determineRegion = (next) => {
    const location = {
      lat: req.body.location.coordinates[1],
      lng: req.body.location.coordinates[0]
    }

    locationHelper
      .getRegionByLatLng(location, 2, (err, regionName) => {
        if(err) {
          return next(err);
        }

        region = regionName;

        next();

      })
  }

  const getService = (next) => {
    BusinessTypeModel
      .findOne({
        _id: businessType,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        service = result.service;
        next();
      })
  }

  const createStore = (next) => {
    UserStoreModel
      .create({
        name: name.trim(),
        nameAlias: tool.change_alias(name.trim()),
        phone,
        member: userId,
        image,
        background,
        address,
        subAddress,
        description,
        location,
        workingTime,
        businessType,
        subType,
        service,
        region,
        focus: 1,
        messageGoLive: 'HeyU sẽ duyệt toàn bộ thông tin sản phẩm, thông tin cửa hàng bạn cung cấp. Vì thế hãy cập nhật hết sản phẩm của Gian Hàng để đạt yêu cầu đưa lên các mục mua sắm của HeyU.'
      },(err,result) => {
        if(err) {
          return next(err)
        }

        StoreLogModel
          .create({
            action: 'Tạo mới cửa hàng',
            level: 0,
            reason: '',
            member: userId,
            store: result._id,
            region
          })

        PhoneTeleModel.findOneAndUpdate({
          member: userId
        }, {
          member: userId,
          tickboxStatus: 1,
          schedule:{
            callAt: Date.now()
          },
          updatedAt: Date.now(),
          statusJob: 1
        }, {upsert: true, setDefaultsOnInsert: true, new: true})
          .populate('member', 'phone', MemberModel)
          .lean()
          .exec((error, phoneTele)=>{
            PhoneTeleLogModel.create({
              phone: _.get(phoneTele,'member.phone'),
              supporter: _.get(phoneTele,'supporter'),
              currentActivityStatus: _.get(phoneTele,'currentActivityStatus'),
              lastActivityStatus: _.get(phoneTele,'lastActivityStatus'),
              departments: _.get(phoneTele,'departments'),
              createdAt: Date.now(),
              region: _.get(phoneTele,'region'),
              tickboxStatus: 1,
              type: 21,
            })
          })


        next(null,{
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    checkStoreExists,
    determineRegion,
    getService,
    createStore
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
