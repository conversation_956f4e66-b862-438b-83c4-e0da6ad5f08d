const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const storeType = _.get(req, 'body.type', '');
  const storeId = _.get(req, 'body.id', '');

  const checkParams = (next) => {
    if (!storeType || !storeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.ERROR
      })
    }

    next();
  }

  const updateFocus = (next) => {
    UserStoreModel
      .update({
        member: userId,
        type: storeType,
        focus: 1
      }, {
        focus: 0
      }, { multi: true })
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        UserStoreModel
          .update({
            _id: storeId
          }, {
            focus: 1
          }, { multi: true })
          .exec((err, result) => {
            if (err) {
              return next(err);
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS
            });
          })
      })
  }

  async.waterfall([
    checkParams,
    updateFocus
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
