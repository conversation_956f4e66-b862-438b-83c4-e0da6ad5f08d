const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const StoreModel = require('../../../models/store');
const StoreLogModel = require('../../../models/storeLog');
const tool = require('../../../utils/tool');
const PushNotifyManager = require('../../../job/pushNotify');

module.exports = (req, res) => {

  const id = req.body.id || '';
  const status = req.body.status || 0
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  const checkParams = (next) => {
    if(!id || !merchant){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const checkPermissions = (next) => {
    if (userId.toString() !== merchant.toString()) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: MESSAGES.USER.PERMISSION_DENY
      })
    }

    next();
  }

  const deactiveUserStore = (next) => {
    UserStoreModel
      .findOneAndUpdate({
        _id: id,
        status:!status,
        member: merchant
      },{
        status: status,
        updatedAt: Date.now()
      },(err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }
        next()
      })
  }

  const deactiveStore = (next) => {
    StoreModel
      .findOneAndUpdate({
        _id: id,
        status: !status,
        member: merchant
      }, {
        status: status,
        updatedAt: Date.now()
      }, (err, result) => {
        if (err) {
          return next(err);
        }

        PushNotifyManager.sendToMember(merchant, '', '', { link: '', extras: {} }, 'store_update', 'tickbox');
        if (staffs.length) {
          staffs.map(staff => {
            PushNotifyManager.sendToMember(staff.id.toString(), '', '', { link: '', extras: {} }, 'store_update', 'tickbox');
          })
        }
        if(result && result.region) {
          StoreLogModel
          .create({
            action: status ? 'Mở cửa hàng' : 'Đóng cửa hàng',
            level: 2,
            reason: '',
            member: userId,
            store: id,
            region: result.region
          })
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    checkPermissions,
    deactiveUserStore,
    deactiveStore
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
