const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const ProductTypeModel = require('../../../models/productType');
const UserStoreModel = require('../../../models/userStore');
const Member = require('../../../models/member');
const ConfigModel = require('../../../models/config');
const StaffModel = require('../../../models/staff');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {

  const userId = _.get(req,'user.id','');
  const platform = _.get(req, 'body.platform', '');
  const merchant = _.get(req, 'store.member', '');
  let memberCode;
  let link = config.proxyRequestServer.tickBoxWeb;
  let storeInf;
  let staff;

  const checkParams = (next) => {
    if(!userId || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const getStaffStore = (next) => {
    if (userId.toString() === merchant.toString()) {
      return next();
    }

    StaffModel
      .findOne({
        status: 1,
        member: userId
      })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(err || {
            code: CONSTANTS.CODE.FAIL
          });
        }

        staff = result;

        next();
      })
  }

  const getStore = (next) => {
    let query = {
      member: merchant,
      focus: 1,
      level: { $ne: CONSTANTS.STORE_LEVEL.DELETED }
    }

    if (userId.toString() !== merchant.toString() && staff) {
      query = {
        _id: staff.store,
        member: merchant,
        level: { $ne: CONSTANTS.STORE_LEVEL.DELETED }
      }
    }

    UserStoreModel
      .findOne(query)
      .populate('businessType', 'name icon')
      .populate('subType', 'name icon')
      .populate('member', 'money code')
      .select('-nameAlias')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }

        if (!result) {
          result = {
            description: ''
          }
        }

        storeInf = result;
        if (result.member && result.member.code) {
          memberCode = result.member.code;
        }

        if (merchant !== userId && staff) {
          storeInf.staff = {
            isStaff: 1,
            level: staff.level,
            canModifyProduct: 1,
            online: staff.online
          };
        }

        if (storeInf && storeInf.member && storeInf.member.money && storeInf.member.money < -40000) {
          storeInf.messageGoLive = 'Số dư ví TickBox của bạn đang âm. Bạn vui lòng nạp thêm tiền vào ví TickBox để có thể tiếp tục nhận đơn hàng. Xin cảm ơn.';
        }

        if (result.workingTime && result.workingTime.from && result.workingTime.to) {
          result.workingTime = [
            [
              {
                "startTime": result.workingTime.from,
                "endTime": result.workingTime.to
              }
            ],
            [
              {
                "startTime": result.workingTime.from,
                "endTime": result.workingTime.to
              }
            ],
            [
              {
                "startTime": result.workingTime.from,
                "endTime": result.workingTime.to
              }
            ],
            [
              {
                "startTime": result.workingTime.from,
                "endTime": result.workingTime.to
              }
            ],
            [
              {
                "startTime": result.workingTime.from,
                "endTime": result.workingTime.to
              }
            ],
            [
              {
                "startTime": result.workingTime.from,
                "endTime": result.workingTime.to
              }
            ],
            [
              {
                "startTime": result.workingTime.from,
                "endTime": result.workingTime.to
              }
            ]
          ]
        }

        next();
      })
  }

  const getLinkOrder = (next) => {
    let query = {
      status: 1,
      type: CONSTANTS.CONFIG_TYPE.LINK_ORDER,
      $or: [
        {
          'region.allow': storeInf.region
        },
        {
          'region.allow': 'all',
          'region.deny': {
            $ne: storeInf.region
          }
        }
      ]
    }

    ConfigModel
      .findOne(query, 'config')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next();
        }

        if (result && result.config && result.config.link) {
          link = result.config.link;
        }

        if (memberCode) {
          storeInf.urlStore = `${link}/${memberCode.toLowerCase()}`;
          storeInf.linkTitle = 'Order trực tiếp qua Link Gian hàng.';
          storeInf.linkContent = 'Hãy đăng lên trang cá nhân, hội nhóm Facebook, Zalo, Instagram để tăng đơn bạn nhé.';
          storeInf.messageShareLink = `Đây là trang web bán hàng của tôi ${storeInf.urlStore}`;
        }

        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: storeInf
        });
      })
  }

  async.waterfall([
    getStaffStore,
    checkParams,
    getStore,
    getLinkOrder
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
