const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const StoreModel = require('../../../models/store');
const BusinessTypeModel = require('../../../models/businessType');
const StoreLogModel = require('../../../models/storeLog');
const PhoneTeleModel = require('../../../models/phoneTele');
const tool = require('../../../utils/tool');
const locationHelper = require('../../../utils/location');

module.exports = (req, res) => {
  const id = _.get(req, 'body._id', '');
  const name = _.get(req, 'body.name', '');
  const phone = _.get(req, 'body.phone', []);
  const userId = _.get(req,'user.id','');

  const address = _.get(req, 'body.address', '');
  const subAddress = _.get(req, 'body.subAddress', '');
  const location = _.get(req, 'body.location', {});
  const workingTime = _.get(req, 'body.workingTime', {});

  const image = _.get(req, 'body.image', '');
  const background = _.get(req, 'body.background', '');
  const description = _.get(req,'body.description','');
  const businessType = _.get(req, 'body.businessType', []);
  const subType = _.get(req, 'body.subType', []);

  let service;
  let store;
  let region;

  const checkParams = (next) => {
    if (!id || !name || !phone.length || !userId || !address || !location || !_.has(location, 'coordinates') || !workingTime || !businessType.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if (!phone[0].startsWith('0') && phone[0].length < 10) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PRODUCT.PHONE_NOT_VALID
      })
    }

    // if (workingTime.from > workingTime.to) {
    //   return next({
    //     code: CONSTANTS.CODE.WRONG_PARAMS,
    //     message: MESSAGES.PRODUCT.WORKING_TIME
    //   })
    // }

    next();
  }

  const getService = (next) => {
    BusinessTypeModel
      .findOne({
        _id: businessType,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }

        service = result.service;
        next();
      })
  }

  const getOldData = (next) => {
    UserStoreModel
      .findOne({
        _id: id,
        member: userId
      })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next();
        }

        store = result;

        next();
      })
  }

  const determineRegion = (next) => {
    if (_.isEqual(store.location, location)) {
      return next();
    }

    const locationUpdate = {
      lat: req.body.location.coordinates[1],
      lng: req.body.location.coordinates[0]
    }

    locationHelper
      .getRegionByLatLng(locationUpdate, 2, (err, regionName) => {
        if(err) {
          return next(err);
        }

        region = regionName;

        next();

      })
  }

  const update = (next) => {
    const check = store && store.name === name && store.image === image && store.background === background && store.address === address && store.subAddress === subAddress && store.description === description;
    let objUpdate = {
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()),
      phone,
      image,
      background,
      address,
      subAddress,
      description,
      location,
      workingTime,
      businessType,
      subType,
      level: 2,
      service,
      updatedAt: Date.now(),
      lastUpdatedAt: Date.now()
    }

    if (region) {
      objUpdate.region = region;
    }

    if (check) {
      let timeSettings = workingTime;
      if (workingTime && workingTime.from && workingTime.to) {
        timeSettings = {
          0: [
            {
              startTime: workingTime.from,
              endTime: workingTime.to
            }
          ],
          1: [
            {
              startTime: workingTime.from,
              endTime: workingTime.to
            }
          ],
          2: [
            {
              startTime: workingTime.from,
              endTime: workingTime.to
            }
          ],
          3: [
            {
              startTime: workingTime.from,
              endTime: workingTime.to
            }
          ],
          4: [
            {
              startTime: workingTime.from,
              endTime: workingTime.to
            }
          ],
          5: [
            {
              startTime: workingTime.from,
              endTime: workingTime.to
            }
          ],
          6: [
            {
              startTime: workingTime.from,
              endTime: workingTime.to
            }
          ],
        }
      }

      objUpdate = {
        phone,
        workingTime,
        timeSettings,
        businessType,
        service,
        updatedAt: Date.now(),
        lastUpdatedAt: Date.now()
      }
    }

    UserStoreModel
      .findOneAndUpdate({
        _id: id,
        member: userId
      },objUpdate,(err, result) => {
        if(err) {
          return next(err);
        }

        if (!result) {
          return next(null,{
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.PRODUCT.NOT_FOUND_STORE
          })
        }

        StoreLogModel
          .create({
            action: 'Cập nhật cửa hàng',
            level: 2,
            reason: '',
            member: userId,
            store: id,
            region: region || result.region
          })

        if (check) {
          StoreModel
            .update({
              _id: id,
              member: userId
            }, objUpdate, (err, result) => {
              if (err) {
                return next(err);
              }

              if (!result) {
                return next(null, {
                  code: CONSTANTS.CODE.FAIL,
                  message: MESSAGES.PRODUCT.NOT_FOUND_STORE
                })
              }

              next(null, {
                code: CONSTANTS.CODE.SUCCESS
              })
            })
        } else {
          PhoneTeleModel.findOneAndUpdate({
            member: userId,
            tickboxStatus: {$ne:2}
          }, {
            schedule:{
              callAt: Date.now()
            },
            statusJob: 1
          })
            .lean()
            .exec((error, phoneTele)=>{})
          next(null,{
            code: CONSTANTS.CODE.SUCCESS
          })
        }
      })
  }

  async.waterfall([
    checkParams,
    getService,
    getOldData,
    determineRegion,
    update
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
