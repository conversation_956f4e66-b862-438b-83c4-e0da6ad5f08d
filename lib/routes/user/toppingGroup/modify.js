const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserToppingGroupModel = require('../../../models/userToppingGroup');
const ToppingGroupModel = require('../../../models/toppingGroup');
const ToppingGroupLogModel = require('../../../models/toppingGroupLog');
const PushNotifyManager = require('../../../job/pushNotify');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const id = _.get(req, 'body.id', '')
  const store = _.get(req, 'body.store', '');
  const name = _.get(req, 'body.name', '').trim();
  const topping = _.get(req, 'body.topping', []);
  const isRequire = _.get(req, 'body.isRequire', 0);
  const minSelect = _.get(req, 'body.minSelect', 1);
  const maxSelect = _.get(req, 'body.maxSelect', 1);
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  let region;
  let toppingGroup;
  let check;

  const checkParams = (next) => {
    if (!id || !name || !store || !topping.length || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const getOldData = (next) => {
    UserToppingGroupModel
      .findOne({
        _id: id
      })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next();
        }

        toppingGroup = result;

        next();
      })
  }

  const update = (next) => {
    let objUpdate = {
      name,
      store,
      member: merchant,
      level: 2,
      topping,
      nameAlias: tool.change_alias(name),
      updatedAt: Date.now()
    };
    check = toppingGroup && toppingGroup.name === name;

    if (check) {
      objUpdate = {
        isRequire,
        minSelect,
        maxSelect,
        topping,
        updatedAt: Date.now()
      }
    }

    UserToppingGroupModel
      .findOneAndUpdate({
        _id: id
      }, objUpdate)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (check) {
          ToppingGroupModel
            .update({
              _id: id
            }, objUpdate, (err, data) => {
              if (err) {
                return next(err);
              }
            })
        }

        ToppingGroupLogModel
          .create({
            type: 1,
            member: userId,
            toppingGroup: id,
            data: result
          })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        });
      })
  }

  async.waterfall([
    checkParams,
    getOldData,
    update
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
