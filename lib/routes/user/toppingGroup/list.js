const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserToppingGroupModel = require('../../../models/userToppingGroup');
const PushNotifyManager = require('../../../job/pushNotify');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const store = _.get(req, 'body.store', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const skip = _.get(req, 'body.skip', 0);
  const limit = _.get(req, 'body.limit', 10);

  const checkParams = (next) => {
    if (!store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const listTopping = (next) => {
    UserToppingGroupModel
      .find({
        store,
        status: 1
      })
      .populate('topping', null, { status: 1 })
      .skip(skip)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        });
      })
  }

  async.waterfall([
    checkParams,
    listTopping
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
