const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserToppingGroupModel = require('../../../models/userToppingGroup');
const ToppingGroupModel = require('../../../models/toppingGroup');
const ToppingGroupLogModel = require('../../../models/toppingGroupLog');
const PushNotifyManager = require('../../../job/pushNotify');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const id = _.get(req, 'body.id', '')
  const status = _.get(req, 'body.status', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  const storeId = _.get(req, 'store.id', []);

  const checkParams = (next) => {
    if (!id || !status) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    next();
  }

  const inactive = (next) => {
    let objUpdate = {
      $pull: {
        store: storeId
      },
      updatedAt: Date.now()
    };

    UserToppingGroupModel
      .findOneAndUpdate({
        _id: id,
        store: storeId
      }, objUpdate)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        ToppingGroupModel
          .findOneAndUpdate({
            _id: id,
            store: storeId
          }, objUpdate)
          .lean()
          .exec((err, data) => {
            if (err) {
              return next(err);
            }

            ToppingGroupLogModel
              .create({
                type: 2,
                member: userId,
                toppingGroup: id,
                store: storeId,
                data: result
              })

            next(null, {
              code: CONSTANTS.CODE.SUCCESS
            });
          })
      })
  }

  async.waterfall([
    checkParams,
    inactive
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
