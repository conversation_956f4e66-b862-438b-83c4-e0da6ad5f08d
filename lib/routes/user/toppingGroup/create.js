const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserToppingGroupModel = require('../../../models/userToppingGroup');
const ToppingGroupLogModel = require('../../../models/toppingGroupLog');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const store = _.get(req, 'body.store', '');
  const name = _.get(req, 'body.name', '').trim();
  const topping = _.get(req, 'body.topping', []);
  const isRequire = _.get(req, 'body.isRequire', 0);
  const minSelect = _.get(req, 'body.minSelect', 0);
  const maxSelect = _.get(req, 'body.maxSelect', 1);
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);

  const checkParams = (next) => {
    if (!store || !name || !topping || !topping.length || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const createTopping = (next) => {
    UserToppingGroupModel
      .create({
        store,
        member: merchant,
        name,
        topping,
        isRequire,
        minSelect: isRequire && minSelect === 0 ? 1 : minSelect,
        maxSelect,
        nameAlias: tool.change_alias(name)
      }, (err, result) => {
        if (err) {
          return next(err)
        }

        ToppingGroupLogModel
          .create({
            type: 0,
            toppingGroup: result._id,
            member: userId,
            data: result
          })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    createTopping
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
