const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const MemberModel = require('../../../models/member');
const StaffModel = require('../../../models/staff');
const StaffLogModel = require('../../../models/staffLog');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const phone = _.get(req, 'body.phone', '');
  const level = _.get(req, 'body.level', 0);
  const userId = _.get(req, 'user.id', '');
  const store = _.get(req, 'body.myStoreId', '');

  let storeInf;
  let memberId;

  const checkParams = (next) => {
    if (!phone || !store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getStoreInf = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: userId
      }, 'region')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.PERMISSION_DENY
          });
        }

        storeInf = result;

        next();
      })
  }

  const getMemberInf = (next) => {
    MemberModel
      .findOne({ phone })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          MemberModel
            .create({
              phone,
              status: 1,
              region: storeInf.region,
              // fromTickBox: 1
            }, (error, data) => {
              if (error) {
                return next(err);
              }

              if (!data) {
                return next({
                  code: CONSTANTS.CODE.FAIL,
                  message: MESSAGES.SYSTEM.ERROR
                })
              }

              memberId = data._id;

              next();
            })

          return;
        }

        memberId = result._id;

        next();
      })
  }

  const checkMember = (next) => {
    if (userId === memberId.toString()) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Thông báo',
          body: 'Bạn không thể tự thêm chính mình làm nhân viên. Xin cảm ơn.'
        }
      });
    }

    UserStoreModel
      .findOne({
        member: memberId
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.STAFF.STAFF_OWNED_STORE
          });
        }

        next();
      })
  }

  const checkStaffOtherStore = (next) => {
    StaffModel
      .findOne({
        member: memberId,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.STAFF.STAFF_EXISTS
          })
        }

        next();
      })
  }

  const checkStaffExists = (next) => {
    StaffModel
      .findOne({
        store: storeInf._id,
        member: memberId
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.STAFF.STAFF_EXISTS
          })
        }

        next();
      })
  }

  const createStaff = (next) => {
    StaffModel
      .create({
        store: storeInf._id,
        phone,
        level,
        member: memberId,
        role: level === 1 ? 'manager' : 'staff'
      }, (err, result) => {
        if (err) {
          return next(err)
        }

        StaffLogModel
          .create({
            type: 0,
            staff: result._id,
            member: userId,
            data: result
          })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    getMemberInf,
    checkMember,
    checkStaffOtherStore,
    checkStaffExists,
    createStaff
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
