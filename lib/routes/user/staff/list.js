const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const StaffModel = require('../../../models/staff');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id', '');
  const store = _.get(req, 'body.myStoreId', '');

  let storeInf;

  const checkParams = (next) => {
    if (!store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getStoreInf = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: userId
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.PERMISSION_DENY
          });
        }

        storeInf = result;

        next();
      })
  }

  const listStaff = (next) => {
    StaffModel
      .find({
        store: storeInf._id
      }, '-store -phone')
      .populate('member', 'facebook.picture name code')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    listStaff
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
