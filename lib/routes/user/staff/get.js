const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const StaffModel = require('../../../models/staff');
const StaffLogModel = require('../../../models/staffLog');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const staffId = _.get(req, 'body.id', '');
  const userId = _.get(req, 'user.id', '');
  const store = _.get(req, 'body.myStoreId', '');

  let storeInf;

  const checkParams = (next) => {
    if (!staffId || !store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getStoreInf = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: userId
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.PERMISSION_DENY
          });
        }

        storeInf = result;

        next();
      })
  }

  const getStaff = (next) => {
    StaffModel
      .findOne({
        _id: staffId,
        store: storeInf._id,
        status: 1
      }, '-status -store -phone')
      .populate('member', 'name code')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {}
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    getStaff
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
