const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const StaffModel = require('../../../models/staff');
const StaffLogModel = require('../../../models/staffLog');
const tool = require('../../../utils/tool');

module.exports = (req, res) => {
  const online = _.get(req, 'body.online', 0);
  const userId = _.get(req, 'user.id', '');

  const updateStaff = (next) => {
    StaffModel
      .findOneAndUpdate({
        member: userId,
        status: 1
      }, {
        online
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.PERMISSION_DENY
          });
        }

        StaffLogModel
          .create({
            type: 1,
            staff: userId,
            member: userId,
            data: result
          })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    updateStaff
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
