const rp = require('request-promise');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('../../../models/userStore');
const StaffModel = require('../../../models/staff');
const StaffLogModel = require('../../../models/staffLog');
const tool = require('../../../utils/tool');
const PushNotifyManager = require('../../../job/pushNotify');

module.exports = (req, res) => {
  const staffId = _.get(req, 'body.id', '');
  const status = _.get(req, 'body.status', 0);
  const userId = _.get(req, 'user.id', '');
  const store = _.get(req, 'body.myStoreId', '');

  let storeInf;
  let memberId;

  const checkParams = (next) => {
    if (!staffId || !store) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getStoreInf = (next) => {
    UserStoreModel
      .findOne({
        _id: store,
        member: userId
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.PERMISSION_DENY
          });
        }

        storeInf = result;

        next();
      })
  }

  const getStaffInf = (next) => {
    if (!status) {
      return next();
    }

    StaffModel
      .findOne({
        _id: staffId
      }, 'member')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (result) {
          memberId = result.member;
        }

        next();
      })
  }

  const checkOwnedStore = (next) => {
    if (!status || !memberId) {
      return next();
    }

    UserStoreModel
      .findOne({
        member: memberId
      }, '_id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.STAFF.STAFF_OWNED_STORE
          });
        }

        next();
      })
  }

  const checkStaffExists = (next) => {
    if (!status || !memberId) {
      return next();
    }

    StaffModel
      .findOne({
        member: memberId,
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.STAFF.STAFF_EXISTS
          })
        }

        next();
      })
  }

  const updateStaff = (next) => {
    StaffModel
      .findOneAndUpdate({
        _id: staffId,
        store: storeInf._id
      }, {
        status
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.PERMISSION_DENY
          });
        }

        StaffLogModel
          .create({
            type: 2,
            staff: staffId,
            member: userId,
            data: result
          })

        PushNotifyManager.sendToMember(result.member.toString(), '', '', { link: '', extras: {} }, 'store_update', 'tickbox');

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      })
  }

  async.waterfall([
    checkParams,
    getStoreInf,
    getStaffInf,
    checkOwnedStore,
    checkStaffExists,
    updateStaff
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
