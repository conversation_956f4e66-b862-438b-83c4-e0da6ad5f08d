const _ = require('lodash')
const async = require('async')
const OrderStoreModel = require('../../../models/orderStore');
const OrderStoreLogModel = require('../../../models/orderStoreLog');
const moment = require('moment');
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const ms = require('ms');
const mongoose = require('mongoose')

module.exports = (req, res) => {
  const d = new Date();
  const from = _.get(req, 'body.from', d.setHours(0, 0, 0, 0)) || d.setHours(0, 0, 0, 0);
  const to = _.get(req, 'body.to', Date.now()) || Date.now();
  const userId = _.get(req, 'user.id', '');
  const merchant = _.get(req, 'store.member', '');
  const staffs = _.get(req, 'store.staffs', []);
  let orderIds = [];

  const checkParams = (next) => {
    if (!from || !to || from > to || !merchant) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const getOrderDone = (next) => {
    OrderStoreLogModel
      .find({
        type: CONSTANTS.ORDER_LOG.DONE,
        merchant: merchant,
        $and: [
          {createdAt: {$gte: from}},
          {createdAt: {$lte: to}}
        ]
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next();
        }

        results.map(item => {
          orderIds.push(item.order);
        });

        next();
      })
  }

  const incomeStatistic = (next) => {
    if (!orderIds.length) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          order: 0,
          income: 0,
          serviceChargeMerchant: 0,
          inappDeposit: 0
        }
      });
    }

    OrderStoreModel
      .find({
        _id: { $in: orderIds },
        status: CONSTANTS.ORDER_STATUS.DONE
      }, 'money serviceChargeMerchant inappDeposit')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (!results || !results.length) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              order: 0,
              income: 0,
              serviceChargeMerchant: 0,
              inappDeposit: 0
            }
          })
        }

        let income = 0;
        let serviceChargeMerchant = 0;
        let inappDeposit = 0;
        results.map(result => {
          income += result.money;
          if(result.serviceChargeMerchant) {
            serviceChargeMerchant += result.serviceChargeMerchant;
          }
          if(result.inappDeposit) {
            inappDeposit += result.inappDeposit
          }
        })
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            order: results.length,
            income,
            serviceChargeMerchant,
            inappDeposit
          }
        })
      })
  }

  async.waterfall([
    checkParams,
    getOrderDone,
    incomeStatistic
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
    res.json(data || err);
  })
}
