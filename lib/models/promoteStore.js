const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const PromoteStore = new mongoose.Schema({
  name: {
    type: String
  },
  code: {
    type: String
  },
  icon: {
    type: String
  },
  status: {
    type: Number,
    default: 1
  },
  strategy: {
    type: Schema.Types.Mixed
  },
  condition: {
    type: Schema.Types.Mixed
  },
  store: {
    type: Schema.Types.ObjectId,
    ref: 'Store'
  },
  service: {
    type: Schema.Types.ObjectId
  },
  showList: {
    type: Number,
    default: 0
  },
  limited: {
    type: Number
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});


PromoteStore.statics.get = function (query, cb) {
  query = query || {};
  query.status = 1;

  this
    .findOne(query)
    .sort("-createdAt")
    .lean()
    .exec(cb)
}

module.exports = mongoConnections('master').model('PromoteStore', PromoteStore);
