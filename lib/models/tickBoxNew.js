const mongoose = require('mongoose');
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const TickBoxNew = new mongoose.Schema({
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'NewCategory'
  },
  title: {
    type: String,
    default: ''
  },
  image: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  active: {
    type: Number,
    default: 1
  },
  region: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
});


TickBoxNew.statics.list = function (category, region, skip = 0, limit = 5, appName = 'customer', cb) {
  this
    .find({
      // category,
      active: 1,
      appName,
      $or: [
        {
          'region.allow': region
        },
        {
          'region.allow': 'all',
          'region.deny': {
            $ne: region
          }
        }
      ]
    }, "-createdAt -active -region")
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip)
    .lean()
    .exec(cb)
}

module.exports = mongoConnections('master').model('TickBoxNew', TickBoxNew);
