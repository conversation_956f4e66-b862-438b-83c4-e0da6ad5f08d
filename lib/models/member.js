const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const generate = require('nanoid/generate')

var MemberSchema = new mongoose.Schema({
    email : {type: String, default: ''},
    password: {type: String, default: ''},
    phone : {type: String},
    name: {type: String, default: ''},
    address: {type: String, default: ''},
    birthday: {
        day: { type: Number },
        month: { type: Number },
        year: {type : Number}
    },
    facebook: {
        id: {type: String},
        realId: {type: String},
        name: {type: String, default: ''},
        email: {type: String, default: ''},
        birthday: {type: String, default: ''},
        token: {type: String, default: ''},
        locale: {type: String},
        timezone: {type: String},
        picture: {type: String, default: ''}
    },
    status: {type: Number, default: 0},
    likes: {
      type: 'Number',
      default: 0
    },
    dislikes: {
      type: 'Number',
      default: 0
    },
    shop:{
      totalPost: {type: 'Number',default: 0},
      isAuthen: {
        type: 'Number',
        default: 0
      },
      totalPostOS: {type: 'Number', default: 0},
      rateStar: {
        type: 'Number',
        default: 5
      }
    },
    ship:{
      isAuthen: {
        type: 'Number',
        default: 0
      },
      totalRides: {
        type: 'Number',
        default: 0
      },
      totalRejects:{
        type: 'Number',
        default: 0
      },
      rateStar: {
        type: 'Number',
        default: 5
      }
    },
    coints: {
      type: 'Number',
      default: 0
    },
    deposit: {
      type: 'Number',
      default: 0
    },
    money: {
      type: 'Number',
      default: 0
    },
    realMoney: {
      type: 'Number',
      default: 0
    },
    realMoneyShop: {
      type: 'Number',
      default: 0
    },
    expireTime: {
      type: 'Number',
      default: 0
    },
    blockUtil: {
      type: 'Number',
      default: 0
    },
    blockOrderUtil: {
      type: 'Number',
      default: 0
    },
    receivePushOrder: {
      type: 'Number',
      default: 0
    },
    regionTransaction: {
      type: String
    },
    isBlockStore: {
      type: 'Number'
    },
    memberToken: {type: String},
    granted: Boolean,
    createdAt: { type: Number, default: Date.now },
    location: { type: mongoose.Schema.Types.Mixed },
    appName: { type: String, default: '' },
    training: {
      type: 'Number',
      default: 0
    },
    point: {
      type: 'Number',
      default: 0
    },
    fromTickBox: {
      type: 'Number'
    },
    code: { type: String, unique: true },
    type: { type: Number }, // 1 for admin, 0 is shipper, 2 is shop
    region: { type: String },
    regionDistrict: {type: String, default: ''},
    updatedAt: {type: Number, default: Date.now }
}, {id: false, versionKey: 'v'})

MemberSchema.virtual('mapScopeId', {
  ref: 'MapScopeId', // The model to use
  localField: '_id', // Find people where `localField`
  foreignField: 'member', // is equal to `foreignField`
  justOne: true
});

MemberSchema.virtual('userStore', {
  ref: 'UserStore', // The model to use
  localField: '_id', // Find people where `localField`
  foreignField: 'member', // is equal to `foreignField`
  justOne: true
});
MemberSchema.virtual('store', {
  ref: 'Store', // The model to use
  localField: '_id', // Find people where `localField`
  foreignField: 'member', // is equal to `foreignField`
});

MemberSchema.pre('save', function (next) {
  let model = this
  attempToGenerate(model, next)
})

const attempToGenerate = (model, callback) => {
  let newCode = generate('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', 5)
  model.constructor.findOne({
    'code': newCode
  }).then((course) => {
    if (course) {
      attempToGenerate(model, callback)
    } else {
      model.code = newCode
      callback();
    }
  }, (err) => {
    callback(err)
  })
}

MemberSchema.statics.getNearest = function (location, distance, query, fields, options, cb) {
  distance = _.isFinite(distance) ? distance : 500

  this
    .find(query, fields, options)
    .near('location', {
      center: {
        coordinates: [location.lng, location.lat],
        type: 'Point'
      },
      maxDistance: distance
    })
    .lean()
    .exec(cb)
}

MemberSchema.statics.increaseTotalOrder = function (userId, cb) {
  this
    .update({_id: userId}, {
      $inc: {
        "shop.totalPostOS": 1
      }
    }).exec(cb)
}

MemberSchema.statics.increaseRealMoney = function (userId, money, cb) {
  this
    .findOneAndUpdate({_id: userId}, {
      $inc: {
        realMoney: money
      }
    }, {
      'new': true
    }).exec(cb)
}

MemberSchema.statics.decreaseCoint = function (userId, coints, cb) {
  this
    .findOneAndUpdate({_id: userId}, {
      $inc: {
        coints: -coints
      }
    }, {
      'new': true
    }).exec(cb)
}

MemberSchema.statics.increaseCoint = function (userId, coints, cb) {
  this
    .findOneAndUpdate({_id: userId}, {
      $inc: {
        coints: coints
      }
    }, {
      'new': true
    }).exec(cb)
}

MemberSchema.statics.increaseMoney = function (userId, money, cb) {
  this
    .findOneAndUpdate({_id: userId}, {
      $inc: {
        money: money
      }
    }, {
      'new': true
    })
    .lean()
    .exec(cb)
}


module.exports = mongoConnections('master').model('Member', MemberSchema);
