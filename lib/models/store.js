const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Store = new mongoose.Schema({
  name: {
    type: String
  },
  region: {
    type: String
  },
  phone: [
    {
      type: String
    }
  ],
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  image:{
    type: String
  },
  background:{
    type: String
  },
  address: {
    type: String
  },
  subAddress: {
    type: String
  },
  description: {
    type: String
  },
  note : {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  nameAlias: {
    type: String
  },
  location: {
    type: Schema.Types.Mixed,
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'CategoryStore'
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  goliveAt: {
    type: Number
  },
  paymentMethod: {
    type: Schema.Types.Mixed
  },
  timeSettings: {
    type: Schema.Types.Mixed
  },
  ref: {
    type: Schema.Types.Mixed
  },
  service: {
    type: Schema.Types.ObjectId
  },
  productSearch: [{
    _id: {
      type:Schema.Types.ObjectId
    },
    nameAlias: {
      type: String
    }
  }],
  hasProduct: {
    type: Number
  },
  golive: {
    type: Number,
    default: 0
  },
  messageGoLive: {
    type: String
  },
  golink: {
    type: Number,
    default: 0
  },
  messageGoLink: {
    type: String
  },
  type: {
    type: Schema.Types.ObjectId,
    ref: 'StoreTypes'
  },
  productTypes: [{
    type: Schema.Types.ObjectId,
    ref: 'ProductType'
  }],
  businessType: [{
    type: Schema.Types.ObjectId,
    ref: 'BusinessType'
  }],
  subType: [{
    type: Schema.Types.ObjectId,
    ref: 'SubType'
  }],
  totalOrders: {
    type: Number,
    default: 0
  }
}, { id: false, versionKey: false});

Store.statics.getNearest = function (location, distance, query, fields, options, cb) {
  distance = _.isFinite(distance) ? distance : 3000

  this
    .find(query, fields, options)
    .near('location', {
      center: {
        coordinates: [location.lng, location.lat],
        type: 'Point'
      },
      maxDistance: distance
    })
    .populate('businessType')
    .populate('subType', 'name')
    .lean()
    .exec(cb)
}

module.exports = mongoConnections('master').model('Store', Store);
