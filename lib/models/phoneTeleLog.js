const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const PhoneTeleLog = new mongoose.Schema({
  phone: {
    type: String,
    default: ""
  },
  supporter: {
    type: Schema.Types.ObjectId,
    default: null,
    ref: "User"
  },
  user: {
    type: Schema.Types.ObjectId,
    default: null,
    ref: "User"
  },
  lastActivityStatus: {
    type: Number,
    default: -1
  },
  currentActivityStatus: {
    type: Number,
    default: -1
  },
  tickboxStatus: {
    type: Number
  },
  store: {
    type: Schema.Types.ObjectId,
    ref: "Store",
  },
  userStore: {
    type: Schema.Types.ObjectId,
    ref: "UserStore",
  },
  type: {
    type: Number,
    default: 0
  },
  newRole: {
    type: Number
  },
  data: {
    type: Schema.Types.Mixed
  },
  distanceTime: {
    type: Number
  },
  departments: {
    type: Schema.Types.Mixed,
    default: []
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  region: {
    type: String,
    default: null
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('cms').model('PhoneTeleLog', PhoneTeleLog);
