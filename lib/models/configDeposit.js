const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

const ConfigDeposit = new mongoose.Schema({
  region: {
    type: Schema.Types.Mixed
  },
  maxDeposit: {
    type: Number
  },
  maxDepositShipperAuthened: {
    type: Number
  },
  maxDepositShopNotAuthened: {
    type: Number
  },
  totalDeposit: {
    type: Number
  },
  totalDepositShipperAuthened: {
    type: Number
  },
  totalDepositShopNotAuthened: {
    type: Number
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});


module.exports = mongoConnections('master').model('ConfigDeposit', ConfigDeposit);
