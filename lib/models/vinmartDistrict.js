const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const VinmartDistrict = new mongoose.Schema({
  code: {
    type: String
  },
  description: {
    type: String
  },
  province: {
    type: Schema.Types.ObjectId,
    ref: "VinmartProvince"
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('VinmartDistrict', VinmartDistrict);
