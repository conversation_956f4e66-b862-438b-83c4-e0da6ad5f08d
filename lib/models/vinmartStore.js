const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const VinmartStore = new mongoose.Schema({
  id: {type: String},
  provinceCode: {type: String},
  districtCode: {type: String},
  wardCode: {type: String},
  storeCode: {type: String},
  storeName: {type: String},
  chainId: {type: String},
  businessStatus: {type: Boolean},
  province: {
    type: Schema.Types.ObjectId,
    ref: "VinmartProvince"
  },
  district: {
    type: Schema.Types.ObjectId,
    ref: "VinmartDistrict"
  },
  ward: {
    type: Schema.Types.ObjectId,
    ref: "VinmartWard"
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('VinmartStore', VinmartStore);
