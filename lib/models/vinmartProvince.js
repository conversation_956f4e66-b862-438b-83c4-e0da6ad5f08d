const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const VinmartProvince = new mongoose.Schema({
  id: {type: String},
  code: {
    type: String
  },
  description: {
    type: String
  },
  isActive: {
    type: Boolean
  },
  priority: {type: Number},
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('VinmartProvince', VinmartProvince);
