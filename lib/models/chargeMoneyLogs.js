const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

var ChargeMoneyLogs = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    amount: {type: Number},
    initialMoney: {type: Number},
    finalMoney: {type: Number},
    createdAt: {
      type: Number,
      default: Date.now
    },
    supporter: {type: mongoose.Schema.Types.ObjectId, ref: 'TickboxUser'},
    region: {type: String}
});


// module.exports = mongoose.model('ChargeMoneyLogs', ChargeMoneyLogs);
module.exports = mongoConnections('master').model('ChargeMoneyLogs', ChargeMoneyLogs);
