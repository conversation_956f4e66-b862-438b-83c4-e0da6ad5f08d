const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const PhoneTele = require('./phoneTele');

const UserStore = new mongoose.Schema({
  name: {
    type: String
  },
  region: {
    type: String
  },
  adminNote: {
    type: String
  },
  needAdminContat: {
    type: Number
  },
  messageGoLive: {
    type: String
  },
  golink: {
    type: Number
  },
  messageGoLink: {
    type: String
  },
  golive: {
    type: Number,
    default: 0
  },
  golink: {
    type: Number,
    default: 0
  },
  approveReason: {
    type: String
  },
  phone: [
    {
      type: String
    }
  ],
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  businessType: [{
    type: Schema.Types.ObjectId,
    ref: 'BusinessType'
  }],
  subType: [{
    type: Schema.Types.ObjectId,
    ref: 'SubType'
  }],
  type: {
    type: Schema.Types.ObjectId,
    ref: 'StoreType'
  },
  focus: {
    type: Number,
    default: 0
  },
  image:{
    type: String,
    default: 'https://media.heyu.asia/uploads/new-img-service/2021-10-01-defaultAvatarStore.png'
  },
  background:{
    type: String,
    default: 'https://media.heyu.asia/uploads/new-img-service/2021-09-30-backgroundStore.jpg'
  },
  address: {
    type: String
  },
  subAddress: {
    type: String
  },
  description: {
    type: String
  },
  level: {
    type: Number,
    default: 0
  },
  status: {
    type: Number,
    default: 1
  },
  nameAlias: {
    type: String
  },
  location: {
    type: Schema.Types.Mixed,
  },
  service: {
    type: Schema.Types.ObjectId
  },
  hasProduct: {
    type: Number,
    default: 0
  },
  productTypes: {
    type: Schema.Types.Mixed,
    default: []
  },
  productSearch: [{
    _id: {
      type:Schema.Types.ObjectId
    },
    nameAlias: {
      type: String
    }
  }],
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  goliveAt: {
    type: Number
  },
  lastUpdatedAt: {
    type: Number,
    default: Date.now
  },
  workingTime: {
    type: Schema.Types.Mixed
  },
  levelStatusProduct: {
    type: Number,
  }
}, { id: false, versionKey: false});

UserStore.virtual('products', {
  ref: 'UserProduct',
  localField: '_id',
  foreignField: 'store'
});

UserStore.virtual('toppingGroups', {
  ref: 'UserToppingGroup',
  localField: '_id',
  foreignField: 'store'
});

UserStore.virtual('toppings', {
  ref: 'UserTopping',
  localField: '_id',
  foreignField: 'store'
});

UserStore.virtual('phoneInf', {
  ref: 'PotentialPhone',
  localField: 'phone',
  foreignField: 'phone',
  justOne: true
});

UserStore.virtual('lastLogs', {
  ref: 'StoreLog',
  localField: '_id',
  foreignField: 'store',
  justOne: true
});

UserStore.virtual('store', {
  ref: 'Store',
  localField: '_id',
  foreignField: '_id',
  justOne: true
});

UserStore.virtual('phoneTele', {
  ref: PhoneTele,
  localField: 'member',
  foreignField: 'member',
  justOne: true
});

UserStore.virtual('staffs', {
  ref: 'Staff',
  localField: '_id',
  foreignField: 'store'
});

module.exports = mongoConnections('master').model('UserStore', UserStore);
