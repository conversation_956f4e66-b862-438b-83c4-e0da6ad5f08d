const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var StoreLog = new mongoose.Schema({
  phone : {type: mongoose.Schema.Types.Mixed},
  action: { type: String },
  author : {type: String},
  level: { type: Number },
  reason: { type: String },
  createdAt: {
    type: Number,
    default: Date.now
  },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'TickboxUser' },
  member: { type: mongoose.Schema.Types.ObjectId, ref: 'Member' },
  store: { type: mongoose.Schema.Types.ObjectId, ref: 'UserStore' },
  region: { type: String },
});

StoreLog.virtual('storeApproved', {
  ref: 'Store',
  localField: 'store',
  foreignField: '_id'
});

module.exports = mongoConnections('master').model('StoreLog', StoreLog);
