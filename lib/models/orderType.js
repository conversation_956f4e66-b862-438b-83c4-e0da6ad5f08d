const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo');
const async = require('async');

var OrderType = new mongoose.Schema({
  name: {
    type: String
  },
  message: {
    type: String
  },
  icon: {
    type: String
  },
  showTip: {
    type: Number
  },
  hideList: {
    type: Number
  },
  forceMoney: {
    type: Number
  },
  maxDeposit: {
    type: Number
  },
  depositEnsure: {
    type: Number
  },
  moneyStrategy: {
    type: Schema.Types.Mixed
  },
  forceAuthen: {
    type: Number
  },
  multipleDes: {
    type: Number
  },
  region: {
    type: Schema.Types.Mixed
  },
  order: {
    type: Number,
    default: 0
  },
  focus: {
    type: Number,
    default: 0
  },
  status: {
    type: Number
  },
  service: {
    type: [Schema.Types.ObjectId]
  }
}, {versionKey: false})

OrderType.statics.get = function (service, region, fields, cb) {
  let orderType;
  const getOrderTypeExactRegion = (next) => {
    if(!region) {
      return next();
    }

    const query = {
      $or: [{
        service
      }, {
        _id: service
      }],
      'region.allow': region,
      status: 1
    }

    let func = this.findOne(query, fields).lean();

    // if(config.enviroment === "production") {
    //   func = func.cache(300, `ordertype:get:${query._id}`)
    // }

    func.exec((err, result) => {
      if(err) {
        return next(err);
      }

      orderType = result;

      next();
    })
  }

  const getDefaultOrderType = (next) => {
    if(orderType) {
      return next();
    }

    const query = {
      $or: [{
        service
      }, {
        _id: service
      }],
      'region.allow': 'all',
      status: 1
    }

    if(region) {
      query['region.deny'] = {
        $ne: region
      }
    }

    let func = this.findOne(query, fields).lean();

    // if(config.enviroment === "production") {
    //   func = func.cache(300, `ordertype:get:${query._id}`)
    // }

    func.exec((err, result) => {
      if(err) {
        return next(err);
      }

      orderType = result;

      next();
    })
  }

  async.waterfall([
    getOrderTypeExactRegion,
    getDefaultOrderType
  ], (err) => {
    if(err) {
      return cb(err);
    }

    cb(null, orderType);
  })
}

module.exports = mongoConnections('master').model('OrderType', OrderType);
