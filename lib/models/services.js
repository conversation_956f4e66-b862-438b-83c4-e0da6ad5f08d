const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var Services = new mongoose.Schema({
    active : {
      type: Number,
      default: 0
    },

    status : {
        type: Number,
        default: 0
      },
    region: {
        type: Schema.Types.Object
      }
}, {id: false, versionKey: 'v'});

module.exports = mongoConnections('master').model('Services', Services);
