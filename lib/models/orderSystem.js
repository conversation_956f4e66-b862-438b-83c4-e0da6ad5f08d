const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var OrderSystem = new mongoose.Schema({
  shipper: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  shop: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  current_place: { type: mongoose.Schema.Types.Mixed },
  origin_place: { type: mongoose.Schema.Types.Mixed },
  destination_places: { type: mongoose.Schema.Types.Mixed },
  other: {
    type: mongoose.Schema.Types.Mixed
  },
  cart: {
    type: mongoose.Schema.Types.Mixed
  },
  deposit: {
    type: Number,
    default: 0
  },
  promote: {
    type: Schema.Types.ObjectId
  },
  salary: {
    type: Number,
    default: 0
  },
  salaryStrategy: {
    type: Schema.Types.Mixed
  },
  serviceCharge: {
    type: Number,
    default: 0
  },
  resourceFee: {
    type: Number,
    default: 0
  },
  distance: {
    type: Number,
    default: 0
  },
  note: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  bill: {
    type: String
  },
  bills: {
    type: Schema.Types.Mixed
  },
  region: {
    type: String,
    default: ""
  },
  regionDistrict: {
    type: String,
    default: ""
  },
  status: {
    type: Number,
    default: 0
  },
  rejects: {
    type: [String],
    default: []
  },
  orderType: {
    type: Schema.Types.ObjectId,
    ref: 'OrderType'
  },
  momoRef: {
    type: Schema.Types.ObjectId,
    ref: 'MoMoLog'
  },
  tip: {
    type: Number,
    default: 0
  },
  takeOrderInf: {
    type: Schema.Types.Mixed
  },
  doneOrderInf: {
    type: Schema.Types.Mixed
  },
  cantTakeOrderInf: {
    type: Schema.Types.Mixed
  },
  startReturningOrderInf: {
    type: Schema.Types.Mixed
  },
  returnDoneOrderInf: {
    type: Schema.Types.Mixed
  },
  startReturnOrderInf: {
    type: Schema.Types.Mixed
  },
  finishReturnOrderInf: {
    type: Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  acceptedAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  hasCalled: {
    type: Number,
    default: 0
  },
  shopHasCalled: {
    type: Number,
    default: 0
  },
  hasMessage: {
    type: Number,
    default: 0
  },
  ensure: {
    type: Number,
    default: 0
  },
  shopHasMessage: {
    type: Number,
    default: 0
  },
  hideShipper: {
    type: Number,
    default: 0
  },
  needRating:{
    type: Number,
    default: 1
  },
  needRatingUniform:{
    type: Number,
    default: 1
  },
  merchantInf:{
    type: Schema.Types.Mixed
  },
  partnerInf:{
    type: Schema.Types.Mixed
  },
  images: {
    type: Schema.Types.Mixed
  },
  tags: {
    type: Schema.Types.Mixed
  },
  vnPayStatus: {
    type: Number
  },
  point: {
    type: Number,
    default: 0
  },
  service: {
    type: Schema.Types.ObjectId
  },
  return: {
    type: Number,
    default: 0
  },
  salaryReturn: {
    type: Number,
    default: 0
  },
  needStretcher: {
    type: Number,
    default: 0
  },
  idSource: {
    type: Schema.Types.ObjectId,
    ref: 'HubOrder'
  },
  cashBackPointRate: {
    type: Number
  },
  platform: {
    type: String
  },
  paymentMethod: {
    type: String,
    default: 'cash'
  },
  cardNumber: {
    type: String
  },
  code: {type: String},
  cartStore: {
    type: Schema.Types.Mixed
  },
  errandInfo: {
    type: Schema.Types.Mixed
  },
  pending: {
    type: Number,
    default: 0
  },
  pickUpTime: {
    type: Number,
    default: 0
  }
}, {versionKey: false})

OrderSystem.virtual('mapScopeId', {
  ref: 'MapScopeId', // The model to use
  localField: 'shop', // Find people where `localField`
  foreignField: 'member', // is equal to `foreignField`
  justOne: true
});

OrderSystem.virtual('ratings', {
  ref: 'Rating',
  localField: '_id',
  foreignField: 'order',
  justOne: true
});

OrderSystem.virtual('ratinguniforms', {
  ref: 'RatingUniform',
  localField: '_id',
  foreignField: 'order',
  justOne: true
});

OrderSystem.virtual('pointtransactionlogs', {
  ref: 'PointTransactionLog',
  localField: '_id',
  foreignField: 'order',
  justOne: true
});


module.exports = mongoConnections('master').model('OrderSystem', OrderSystem);
