const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const VinmartMenu = new mongoose.Schema({
  id: {type: String},
  isBlue: {type: Boolean},
  remainingQuota: {type: Number},
  isFavourite: {type: Boolean},
  itemNo: {type: String},
  mediaUrl: {type: String},
  name: {type: String},
  price: {type: Number},
  salePrice: {type: Number},
  quantity: {type: Number},
  uom: {type: String},
  uomName: {type: String},
  brand: {type: String},
  seoName: {type: String},
  uoms: {type: Schema.Types.Mixed},
  attributes: {type: Schema.Types.Mixed},
  content: {type: String},
  mediaItems: {type: Schema.Types.Mixed},
  shortDesc: {type: String},
  transport: {type: Schema.Types.Mixed},
  // province: {
  //   type: Schema.Types.ObjectId,
  //   ref: "VinmartProvince"
  // },
  // district: {
  //   type: Schema.Types.ObjectId,
  //   ref: "VinmartDistrict"
  // },
  // ward: {
  //   type: Schema.Types.ObjectId,
  //   ref: "VinmartWard"
  // },
  store: {
    type: Schema.Types.ObjectId,
    ref: "VinmartStore"
  },
  slug: {
    type: Schema.Types.ObjectId,
    ref: "VinmartCategorie"
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('VinmartMenu', VinmartMenu);
