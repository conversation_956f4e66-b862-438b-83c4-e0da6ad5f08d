const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const OrderStore = new mongoose.Schema({
  store: {
    type: Schema.Types.ObjectId,
    ref: 'Store'
  },
  cart: {
    type: Schema.Types.Mixed
  },
  customer: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  receiver: {
    type: Schema.Types.Mixed
  },
  money: {
    type: Number
  },
  inappDeposit: {
    type: Number
  },
  promote: {
    type: Schema.Types.ObjectId
  },
  discountMoney: {
    type: Number
  },
  promoteStore: {
    type: Schema.Types.ObjectId
  },
  discountMoneyDeposit: {
    type: Number
  },
  paymentMethod: {
    type: String
  },
  cardNumber: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  deliveryType: {
    type: String
  },
  delivery: {
    type: Schema.Types.ObjectId,
    ref: 'OrderSystem'
  },
  note: {
    type: String
  },
  noteStore: {
    type: String
  },
  salary: {
    type: Number,
    default: 0
  },
  serviceChargeMerchant: {
    type: Number,
    default: 0
  },
  merchantPaySalary: {
    type: Number,
    default: 0
  },
  salaryStrategy: {
    type: Schema.Types.Mixed
  },
  orderType: {
    type: Schema.Types.ObjectId,
    ref: 'OrderType'
  },
  distance: {
    type: Number,
    default: 0
  },
  code: {type: String},
  polylines: {type: String},
  platform: {type: String},
  nativeVersion: {type: String},
  regionDistrict: {type: String},
  region: {type: String},
  messageWaitStore: {type: String},
  messageDelivery: {type: String},
  timeWait: {type: Schema.Types.Mixed},
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  momoRef: {
    type: Schema.Types.ObjectId,
    ref: 'MoMoLog'
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('OrderStore', OrderStore);
