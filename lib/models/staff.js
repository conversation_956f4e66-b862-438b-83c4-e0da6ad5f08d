const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Staff = new mongoose.Schema({
  store: {
    type: Schema.Types.ObjectId,
    ref: 'UserStore'
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  phone: {
    type: String
  },
  role: {
    type: String
  },
  status: {
    type: Number,
    default: 1
  },
  online: {
    type: Number,
    default: 0
  },
  level: {
    type: Number,
    default: 1 // 0: manager, 1: staff
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('Staff', Staff);
