const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const UserSchema = new mongoose.Schema({
  email: {
    type: String,
    default: null
   },
  password: {
    type: String,
    default: "$2a$08$kyWnU8E7t7VBqfp2yZSyd.9U52B.qD7GG.stLSpKGHYtqbOTyITpu" //1234@abcd
   },
  name: {
    first: { type: String,
      default: null
     },
    last: { type: String,
      default: null
     },
  },
  dateOfBirth: {
    type: Number,
    default: null
  },
  phone: {
    type: String,
    default: null
  },
  sex: {
    type: String,
    default: null
  },
  facebook: {
    id: { type: String },
    name: { type: String },
    email: { type: String },
    phone: { type: String },
    token: { type: String },
  },
  access_level: { type: Number , default: 0},
  access_token: { type: String , default: null},
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  type: {
    type: Number,
    default: 3
   },
  active: {
    type: Number,
    default: 0
   },
  level: {
    type: mongoose.Schema.Types.Mixed,
    default: [0]
   },
  ip: { type: Array },
  region: {
    type: String,
    default: "hn"
   },
  saleType: {
   type: String,
   default: "offline"
  },
  gender: {
    type: String,
    default: null
   },
  dob: {
    type: String,
    default: null
   },
  idCard: {
    type: String,
    default: null
   },
  idCardDate: {
    type: String,
    default: null
   },
  idCardPlace: {
    type: String,
    default: null
   },
  address: {
    type: String,
    default: null
   },
  risk: { type: Number },
  departments:{
    type: mongoose.Schema.Types.Mixed,
    default: []
  },
  readPhone: {
    type: Number,
    default: 0
  },
  roles: {
    type: mongoose.Schema.Types.Object,
  }
}, { id: false, versionKey: 'v' });

// module.exports = mongoose.model('User', UserSchema);
module.exports = mongoConnections('master').model('User', UserSchema);
