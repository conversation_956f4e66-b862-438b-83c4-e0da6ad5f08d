const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var StoreTypes = new mongoose.Schema({
    name : {
      type: String,
      default: ''
    },
    nameAlias : {
      type: String
    },
    updatedAt : {
        type: Number,
        default: Date.now
      },
    createdAt : {
        type: Number,
        default: Date.now
      },
    status : {
        type: Number,
        default: 0
      },
    region: {
        type: Schema.Types.Object
      }
}, {id: false, versionKey: 'v'});

module.exports = mongoConnections('master').model('StoreTypes', StoreTypes);
