const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const UserProductType = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  name: {
    type: String
  },
  parent: {
    type: Schema.Types.ObjectId,
    ref: 'UserProductType'
  },
  children: {
    type: Schema.Types.ObjectId,
    ref: 'UserProductType'
  },
  ref: {
    type: Schema.Types.Mixed
  },
  level: {
    type: Number
  },
  approveReason: {
    type: String
  },
  type: {
    type: String
  },
  icon: {
    type: String,
    default: 'https://media.heyu.asia/uploads/new-img-service/2021-10-01-defaultCategory.png'
  },
  default: {
    type: Number,
    default: 0
  },
  nameAlias: {
    type: String
  },
  region: {
    type: Schema.Types.Mixed
  },
  store: [{
    type: Schema.Types.ObjectId,
    ref: 'UserStore'
  }],
  storeType: {
    type: Schema.Types.ObjectId
  },
  service: {
    type: Schema.Types.ObjectId
  },
  status: {
    type: Number,
    default: 1
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  order: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('UserProductType', UserProductType);
