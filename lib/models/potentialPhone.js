const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var PotentialPhone = new mongoose.Schema({

  phone : {
    type: String,
    unique: true
  },
  member: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member'
  },
  webSite: {
    type: String
  },
  active: {
    type: Number,
    default: 1
  },
  status: {
    type: Number,
    default: 0
  },
  webSite: {
    type: String,
    default: ''
  },
  heed: {
    type: String
  },
  logAuthen: {
    type: Array
  },
  note: {
    type: Array
  },
  totalCall: {
    type: Number,
    default: 0
  },
  fbLink: {
    type: String,
    default: ''
  },
  gfbLink: {
    type: String,
    default: ''
  },
  totalPostFB: {
    type: Number,
    default: 0
  },
  otherPhone: {
    type: String,
    default: ''
  },
  businessItems: {
    type: mongoose.Schema.Types.Mixed
  },
  infImages: {
    type: mongoose.Schema.Types.Mixed
  },
  rate: {
    type: Number,
    default: 0
  },
  updatedAt:{
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  supporter: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Users'
  }
}, {id: false, versionKey: 'v'});

module.exports = mongoConnections('master').model('PotentialPhone', PotentialPhone);
