const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

var Role = new mongoose.Schema({
    name: {
      type: String,
      unique: true
    },
    group: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'GroupRole'
    },
    status:{
      type: Number,
      default: 1,
    },
    hidden: {
      type: Number,
      default: 0
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: false})


module.exports = mongoConnections('cms').model('Role', Role);
