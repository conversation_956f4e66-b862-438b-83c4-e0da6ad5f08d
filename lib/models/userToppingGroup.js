const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const UserToppingGroup = new mongoose.Schema({
  nameAlias: {
    type: String
  },
  name: {
    type: String
  },
  isRequire: {
    type: Number,
    default: 0
  },
  minSelect: {
    type: Number
  },
  maxSelect: {
    type: Number
  },
  status: {
    type: Number,
    default: 1
  },
  level: {
    type: Number,
    default: 0
  },
  approveReason: {
    type: String
  },
  store: [{
    type: Schema.Types.ObjectId,
    ref: 'UserStore'
  }],
  storeType: {
    type: Schema.Types.ObjectId
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  topping: [{
    type: Schema.Types.ObjectId,
    ref: 'UserTopping'
  }],
  order: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });


module.exports = mongoConnections('master').model('UserToppingGroup', UserToppingGroup);
