const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const UserTopping = new mongoose.Schema({
  name: {
    type: String
  },
  nameAlias: {
    type: String
  },
  price: {
    type: Number,
    default: 0
  },
  status: {
    type: Number,
    default: 1
  },
  isAvailable: {
    type: Number,
    default: 1
  },
  level: {
    type: Number,
    default: 0
  },
  approveReason: {
    type: String
  },
  // toppingGroup: [{
  //   type: Schema.Types.ObjectId,
  //   ref: 'UserToppingGroup'
  // }],
  store: [{
    type: Schema.Types.ObjectId,
    ref: 'UserStore'
  }],
  storeType: {
    type: Schema.Types.ObjectId
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  order: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });


module.exports = mongoConnections('master').model('UserTopping', UserTopping);
