const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

var Department = new mongoose.Schema({
    name: {type: String},
    roles: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Role'
    }],
    idParent: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Department'
    },
    level:{
      type: Number
    },
    status:{
      type: Number,
      default: 1
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: false})


module.exports = mongoConnections('cms').model('Department', Department);
