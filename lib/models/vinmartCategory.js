const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const VinmartCategory = new mongoose.Schema({
  parent: {type: Schema.Types.Mixed},
  lstChild: {type: Schema.Types.Mixed},
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('VinmartCategory', VinmartCategory);
