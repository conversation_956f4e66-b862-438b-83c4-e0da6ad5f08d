const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var ActivityLog = new mongoose.Schema({
    author : {type: String},
    phone: {type: String},
    action : {type: String},
    reason : {type: String},
    createdAt: {
        type: Number,
        default: Date.now
    },
    userId: {type: mongoose.Schema.Types.ObjectId, ref: 'User'},
    member: {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    region: {type: String}
});

module.exports = mongoConnections('master').model('ActivityLog', ActivityLog);
