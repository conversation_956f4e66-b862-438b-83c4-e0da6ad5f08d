const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

var GroupRole = new mongoose.Schema({
    name: {
      type: String,
      unique: true
    },
    status:{
      type: Number,
      default: 1,
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: false})


module.exports = mongoConnections('cms').model('GroupRole', GroupRole);
