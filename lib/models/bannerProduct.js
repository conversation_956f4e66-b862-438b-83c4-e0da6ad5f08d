const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var BannerProduct = new mongoose.Schema({
  region: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
      type: Number,
      default: Date.now
  }
}, {versionKey: false})


//module.exports = mongoose.model('banner', Banner);
module.exports = mongoConnections('master').model('BannerProduct', BannerProduct);
