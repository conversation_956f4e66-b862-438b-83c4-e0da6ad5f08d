const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Topping = new mongoose.Schema({
  name: {
    type: String
  },
  nameAlias: {
    type: String
  },
  price: {
    type: Number,
    default: 0
  },
  status: {
    type: Number,
    default: 1
  },
  isAvailable: {
    type: Number,
    default: 1
  },
  // toppingGroup: [{
  //   type: Schema.Types.ObjectId,
  //   ref: 'ToppingGroup'
  // }],
  store: [{
    type: Schema.Types.ObjectId,
    ref: 'Store'
  }],
  storeType: {
    type: Schema.Types.ObjectId
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  order: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });


module.exports = mongoConnections('master').model('Topping', Topping);
