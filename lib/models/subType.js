const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const SubType = new mongoose.Schema({
  name: {
    type: String
  },
  icon: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  businessType: [{
    type: Schema.Types.ObjectId,
    ref: 'BusinessType'
  }],
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('SubType', SubType);
