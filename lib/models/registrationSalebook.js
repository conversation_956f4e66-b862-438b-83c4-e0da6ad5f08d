const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const RegistrationSalebook = new mongoose.Schema({
  name: {
    type: String
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  numberOfStore: {
    type: Number,
    default: 0
  },
  phone:{
    type: Schema.Types.Mixed
  },
  images:{
    type: Schema.Types.Mixed
  },
  status: {
    type: Number,
    default: 0
  },
  name:{
    type: String
  },
  storeName:{
    type: String
  },
  note:{
    type: String
  },
  subAddress:{
    type: String
  },
  address:{
    type: String
  },
  region:{
    type: String
  },
  district:{
    type: String
  },
  ward:{
    type: String
  },
  businessTypes:{
    type: Schema.Types.Mixed
  },
  supporter: {
    type: Schema.Types.ObjectId,
    ref: 'TickBoxUser'
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('cms').model('RegistrationSalebook', RegistrationSalebook);
