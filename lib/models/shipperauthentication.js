const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var ShipperAuthentication = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  licensePlate : {
    type: String
  },
  brand : {
    type: String
  },
  color : {
    type: String
  },
  licensePlate : {
    type: String
  },
  bike : {
    type: Number
  },
  carType : {
    type: String
  },
  identitySimCardImg: {
    type: String
  },
  identityCard: {
    type: String
  },
  photo: {
    type: String
  },
  name: {
    type: String
  },
  hasUniform: {
    type: Number,
    default: 0
  },
  birthday:{
    type: Number
  },
  identityNumber: {
    type: String
  },
  registrationMotor: {
    type: String
  },
  supporter: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  regionDistrict: {
    type: String
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  receivedUniformAt: {
    type: Number
  },
  onlineAuthen: {
    type: Number,
    default: 0
  },
  region:{
    type: String
  },
  regionTransaction:{
    type: String
  },
  media: {
    type: String
  },
  moneyBack: {type: 'Number'},
  phonesRelation: {
    type: Schema.Types.Mixed
  },
  identityCardInf: {
    type: Schema.Types.Mixed
  },
  licenseCardInf: {
    type: Schema.Types.Mixed
  },
  registrationMotorInf: {
    type: Schema.Types.Mixed
  },
  registrationCarInf: {
    type: Schema.Types.Mixed
  },
  signatureImg: {
    type: String
  },
}, {id: false, versionKey: 'v'})

module.exports = mongoConnections('master').model('ShipperAuthentication', ShipperAuthentication);
