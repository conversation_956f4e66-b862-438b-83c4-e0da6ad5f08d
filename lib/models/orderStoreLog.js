const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const OrderStoreLog = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId
  },
  order: {
    type: Schema.Types.ObjectId
  },
  store: {
    type: Schema.Types.ObjectId
  },
  customer: {
    type: Schema.Types.ObjectId
  },
  merchant: {
    type: Schema.Types.ObjectId
  },
  supporter: {
    type: Schema.Types.ObjectId
  },
  reason: {
    type: String
  },
  type: {
    type: Number
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('OrderStoreLog', OrderStoreLog);
