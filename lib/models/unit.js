const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Unit = new mongoose.Schema({
  name: {
    type: String
  },
  icon: {
    type: String
  },
  unit: {
    type: Schema.Types.Mixed
  },
  status: {
    type: Number,
    default: 1
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('Unit', Unit);
