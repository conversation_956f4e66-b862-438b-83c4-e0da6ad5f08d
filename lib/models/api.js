const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var Api = new mongoose.Schema({
    name: {type: String},
    url: {
      type: String,
      unique: true
    },
    roles: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Role'
    }],
    status:{
      type: Number,
      default: 1
    },
    latestVersion : {
      type: String,
      default: '1.0'
    },
    minVersion: {
      type:String,
      default: '1.0'
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: false})


module.exports = mongoConnections('cms').model('Api', Api);
