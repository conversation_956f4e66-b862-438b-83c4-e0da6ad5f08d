const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const UserProduct = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  name: {
    type: String
  },
  nameAlias: {
    type: String
  },
  description: {
    type: String
  },
  price: {
    type: Number
  },
  pricePromote: {
    type: Number
  },
  images: {
    type: Schema.Types.Mixed,
    default: ['https://media.heyu.asia/uploads/new-img-service/2021-10-01-defaultProduct.png']
  },
  store: [{
    type: Schema.Types.ObjectId,
    ref: 'UserStore'
  }],
  productType: [{
    type: Schema.Types.ObjectId,
    ref: 'UserProductType'
  }],
  topping: [{
    type: Schema.Types.ObjectId,
    ref: 'UserToppingGroup'
  }],
  isAvailable: [{
    type: Schema.Types.ObjectId,
    ref: 'UserStore'
  }],
  unit: {
    type: String
  },
  status: {
    type: Number,
    default: 1
  },
  level: {
    type: Number,
    default: 0
  },
  quantitative: {type: String},
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  approveReason: {
    type: String
  },
  order: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});

UserProduct.virtual('product', {
  ref: 'Product',
  localField: '_id',
  foreignField: '_id',
  justOne: true
});

module.exports = mongoConnections('master').model('UserProduct', UserProduct);
