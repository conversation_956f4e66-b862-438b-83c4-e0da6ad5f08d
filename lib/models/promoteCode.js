const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const PromoteCode = new mongoose.Schema({
  code: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  vnpay: {
    type: Number
  },
  strategy: {
    type: Schema.Types.Mixed
  },
  condition: {
    type: Schema.Types.Mixed
  },
  service: {
    type: Schema.Types.ObjectId
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});


PromoteCode.statics.get = function (query, cb) {
  query = query || {};
  query.status = 1;

  this
    .findOne(query)
    .sort("-createdAt")
    .lean()
    .exec(cb)
}

module.exports = mongoConnections('master').model('PromoteCode', PromoteCode);
