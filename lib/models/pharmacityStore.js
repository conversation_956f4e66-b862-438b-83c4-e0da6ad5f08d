const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const PharmacityStore = new mongoose.Schema({
  id:{
    type: Number
  },
  name: {
    type: String
  },
  latitude:{
    type: String
  },
  longitude: {
    type: String
  },
  address:{
    type: String
  },
  province: {
    type: String
  },
  district:{
    type: String
  },
  ward: {
    type: String
  },
  province_id:{
    type: Number
  },
  district_id: {
    type: Number
  },
  ward_id: {
    type: Number
  },
  code:{
    type: String
  },
  open_time: {
    type: String
  },
  close_time:{
    type: String
  },
  phone: {
    type: String
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('PharmacityStore', PharmacityStore);
