const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const ProductType = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  name: {
    type: String
  },
  parent: {
    type: Schema.Types.ObjectId,
    ref: 'ProductType'
  },
  children: {
    type: Schema.Types.ObjectId,
    ref: 'ProductType'
  },
  ref: {
    type: Schema.Types.Mixed
  },
  level: {
    type: Number
  },
  type: {
    type: String
  },
  icon: {
    type: String
  },
  default: {
    type: Number,
    default: 0
  },
  nameAlias: {
    type: String
  },
  region: {
    type: Schema.Types.Mixed
  },
  store: [{
    type: Schema.Types.ObjectId,
    ref: 'Store'
  }],
  storeType: {
    type: Schema.Types.ObjectId
  },
  service: {
    type: Schema.Types.ObjectId
  },
  status: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  order: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('ProductType', ProductType);
