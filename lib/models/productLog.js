const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var ProductLog = new mongoose.Schema({
  action: { type: String },
  author : {type: String},
  level: { type: Number },
  reason: { type: String },
  createdAt: {
    type: Number,
    default: Date.now
  },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'TickboxUser' },
  member: { type: mongoose.Schema.Types.ObjectId, ref: 'Member' },
  product: { type: mongoose.Schema.Types.ObjectId, ref: 'UserProduct' },
  region: { type: String },
  store: {
    type: Schema.Types.ObjectId,
    ref: 'Store'
  },
});

module.exports = mongoConnections('master').model('ProductLog', ProductLog);
