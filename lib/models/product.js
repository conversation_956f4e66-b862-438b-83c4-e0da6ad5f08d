const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Product = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  name: {
    type: String
  },
  nameAlias: {
    type: String
  },
  description: {
    type: String
  },
  price: {
    type: Number
  },
  isAvailable: [{
    type: Schema.Types.ObjectId,
    ref: 'Store'
  }],
  ref: {
    type: Schema.Types.Mixed
  },
  type: {
    type: String
  },
  storeType: {
    type: Schema.Types.ObjectId,
    ref: 'StoreTypes'
  },
  pricePromote: {
    type: Number
  },
  images: {
    type: Schema.Types.Mixed
  },
  store: [{
    type: Schema.Types.ObjectId,
    ref: 'Store'
  }],
  productType: [{
    type: Schema.Types.ObjectId,
    ref: 'ProductType'
  }],
  topping: [{
    type: Schema.Types.ObjectId,
    ref: 'ToppingGroup'
  }],
  status: {
    type: Number,
    default: 0
  },
  totalSold: {
    type: Number,
    default: 0
  },
  unit: {
    type: String
  },
  quantitative: {type: String},
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  order: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('Product', Product);
