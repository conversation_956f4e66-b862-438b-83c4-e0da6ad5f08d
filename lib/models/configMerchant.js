const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

const ConfigMerchantModel = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  config: { type: mongoose.Schema.Types.Mixed },
  status: {
    type: Number,
    default: 1
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false, strict: false });


module.exports = mongoConnections('master').model('ConfigMerchantOrder', ConfigMerchantModel);
