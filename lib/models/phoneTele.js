const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const PhoneTele = new mongoose.Schema({
  phone: {
    type: String
  },
  info: {
    type: Schema.Types.Mixed
  },
  departments: {
    type: Schema.Types.Mixed,
    default: ['telesale']
  },
  type: {
    type: Number,
    default: 2
  },
  supporter: {
    type: Schema.Types.ObjectId,
    default: null,
    ref: "User"
  },
  lastSupporter: {
    type: Schema.Types.ObjectId,
    default: null,
    ref: "User"
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: "Member",
    default: null
  },
  lastActivityStatus: {
    type: Number,
    default: -1
  },
  currentActivityStatus: {
    type: Number,
    default: -1
  },
  tickboxStatus: {
    type: Number
  },
  statusJob: { // 0 , 1 , 2, 3
    type: Number,
    default: 0
  },
  level: {
    type: Number,
    default: 0
  },
  schedule: {
    type: Schema.Types.Mixed,
    default: {callAt: Date.now}
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  region: {
    type: String
  },
  fakePhone:{
    type: Number,
  }
}, {id: false, versionKey: 'v'});

module.exports = mongoConnections('cms').model('PhoneTele', PhoneTele);
