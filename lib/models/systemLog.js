const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

var SystemLog = new mongoose.Schema({
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'TickBoxUser'
    },
    action: {type: String},
    data: {
      type: mongoose.Schema.Types.Mixed
    },
    type: {
      type: Number
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: 'v'})


module.exports = mongoConnections('master').model('SystemLog', SystemLog);
