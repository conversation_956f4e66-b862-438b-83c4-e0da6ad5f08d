const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const BusinessType = new mongoose.Schema({
  name: {
    type: String
  },
  icon: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  service: {
    type: Schema.Types.ObjectId
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('BusinessType', BusinessType);
