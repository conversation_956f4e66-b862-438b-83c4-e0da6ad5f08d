const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

var UserSchema = new mongoose.Schema({
    username : {
      type: String,
      unique: true
    },
    email : {
      type: String,
      unique: true
    },
    code: {type: String, unique: true},
    password: {type: String},
    name: {
      type: mongoose.Schema.Types.Object,
    },
    fullName: {type: String},
    phone: {type: String},
    region: {type: String},
    department: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Department'
    }],
    roles: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Role'
    }],
    status: {
      type: Number,
      default: 1
    },
    active: {
      type: Number,
      default: 0
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: false})

module.exports = mongoConnections('cms').model('TickboxUser', UserSchema);
