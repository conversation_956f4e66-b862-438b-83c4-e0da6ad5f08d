const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const ToppingLog = new mongoose.Schema({
  topping: {
    type: Schema.Types.ObjectId,
    ref: 'UserTopping'
  },
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  type: { // 0: tạo mới, 1: chỉnh sửa, 2: inactive
    type: Number
  },
  data: {
    type: Schema.Types.Mixed
  },
  store: {
    type: Schema.Types.ObjectId,
    ref: 'Store'
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('ToppingLog', ToppingLog);
