const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const ProductPhamacy = new mongoose.Schema({
  name:{
    type: String
  },
  id: {
    type: String
  },
  categories: {
    type: Schema.Types.Mixed
  },
}, {id: false, versionKey: false});

ProductPhamacy.virtual('productTypes', {
  ref: 'ProductType',
  localField: 'categories',
  foreignField: 'ref.id'
});

module.exports = mongoConnections('master').model('ProductPhamacy', ProductPhamacy);
