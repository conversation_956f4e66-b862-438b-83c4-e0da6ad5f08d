{"name": "ss-business-service", "version": "1.0.0", "description": "HeyU service for topup partners", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "babel-node index.js --presets es2015"}, "repository": {"type": "git", "url": "git+https://gitlab.com/tuanhm/sanship-handle-order.git"}, "author": "tuanhm", "license": "ISC", "bugs": {"url": "https://gitlab.com/tuanhm/sanship-handle-order/issues"}, "homepage": "https://gitlab.com/tuanhm/sanship-handle-order#README", "dependencies": {"async": "2.6.3", "bcrypt": "5.0.1", "config": "1.30.0", "cors": "2.8.5", "crypto": "1.0.1", "exceljs": "4.3.0", "express": "4.17.1", "express-validation": "3.0.8", "fs": "0.0.1-security", "ioredis": "4.28.5", "joi": "10.6.0", "joi-objectid": "2.0.0", "lodash": "4.17.21", "mongoose": "5.13.9", "morgan": "1.10.0", "ms": "2.1.3", "nanoid": "1.3.4", "node-schedule": "2.1.0", "nodemailer": "6.4.10", "path": "0.12.7", "redis": "2.8.0", "request": "2.88.2", "request-promise": "4.2.6", "validator": "github:LightKnight3r/validator.js", "winston": "2.4.5", "winston-daily-rotate-file": "3.10.0"}}