const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const ProductModel = require('./lib/models/product');
const UserProductModel = require('./lib/models/userProduct');
const ProductTypeModel = require('./lib/models/productType');
const UserProductTypeModel = require('./lib/models/userProductType');
const ToppingModel = require('./lib/models/topping');
const UserToppingModel = require('./lib/models/userTopping');
const ToppingGroupModel = require('./lib/models/toppingGroup');
const UserToppingGroupModel = require('./lib/models/userToppingGroup');
const StoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const rp = require('request-promise')
const tool = require('./lib/utils/tool');
const locationHelper = require('./lib/utils/location');
const limit = 1000;

const isAvailable = () => {
  UserProductModel
    .find({ store: { $exists: true }, isAvailable: {$type: 16} }, 'store')
    .limit(limit)
    .lean()
    .exec((err, results) => {
      if (err) {
        return console.log('haha:err', err);
      }

      if (!results || !results.length) {
        return console.log('haha:done');
      }

      async.mapLimit(results, limit, (result, done) => {
        UserProductModel
          .update({
            _id: result._id
          }, {
            isAvailable: result.store
          })
          .exec((error, res) => {
            console.log('haha:error', error, res)

            done();
          })
      }, (err, result) => {
        if (err) {
          return console.log('haha:err', err)
        }

        setTimeout(() => {
          isAvailable();
        }, 5000);
      })
    })
}

isAvailable();

return;

// StoreModel
//   .update({ member: { $exists: true } }, { focus: 1 }, { multi: true })
//   .exec((err, result) => {
//     if (err) {
//       // done(err);
//       return console.log('haha:err1', i, err);
//     }

//     console.log('haha:StoreModel', result);

//     // done();
//   })

// return;

const sync = () => {
  ToppingGroupModel
    .find({ store: { $type: 'objectId' }, 'store.0': { $exists: false } }, 'store')
    .limit(limit)
    .lean()
    .exec((err, results) => {
      console.log('start', err, results);
      if (err) {
        return console.log('haha:err', err);
      }

      if (!results || !results.length) {
        return console.log('haha:done');
      }

      async.mapLimit(results, limit, (result, done) => {
        ToppingGroupModel
          .update({ _id: result._id }, { store: [result.store] }, { multi: true })
          .exec((err, result) => {
            if (err) {
              done(err);
              return console.log('haha:err1', i, err);
            }

            console.log('haha:ToppingGroupModel', result);

            done();
          })
      }, (err, result) => {
        if (err) {
          return console.log('haha:err', err)
        }

        setTimeout(() => {
          sync();
        }, 5000);
      })
    })
}

sync();

return;

StoreModel
  .update({ store: { $exists: true } }, { focus: 1 }, { multi: true })
  .exec((err, result) => {
    if (err) {
      done(err);
      return console.log('haha:err1', i, err);
    }

    console.log('haha:StoreModel', result);

    done();
  })

// UserProductModel
//   .find({$and: [{store: { $exists: true }}, {store: { $type: 'objectId' }}]})
//   .lean()
//   .exec((err, results) => {
//     if (err) {
//       return console.log('haha:err', err);
//     }

//     if (!results || !results.length) {
//       return console.log('haha:no product');
//     }

//     results.map((result, index) => {
//       UserProductModel
//         .update({ _id: result._id, 'store.0': { $exists: false } }, { store: [result.store] }, { multi: true })
//         .exec((err, result) => {
//           console.log('haha:err:UserProductModel', index, err, result);
//         })
//     })
//   })

// ProductTypeModel
//   .find({$and: [{store: { $exists: true }}, {store: { $type: 'objectId' }}]})
//   .lean()
//   .exec((err, results) => {
//     if (err) {
//       return console.log('haha:err', err);
//     }

//     if (!results || !results.length) {
//       return console.log('haha:no product');
//     }

//     results.map((result, index) => {
//       ProductTypeModel
//         .update({ _id: result._id, 'store.0': { $exists: false } }, { store: [result.store] }, { multi: true })
//         .exec((err, result) => {
//           console.log('haha:err:ProductTypeModel', index, err, result);
//         })
//     })
//   })

// UserProductTypeModel
//   .find({$and: [{store: { $exists: true }}, {store: { $type: 'objectId' }}]})
//   .lean()
//   .exec((err, results) => {
//     if (err) {
//       return console.log('haha:err', err);
//     }

//     if (!results || !results.length) {
//       return console.log('haha:no product');
//     }

//     results.map((result, index) => {
//       UserProductTypeModel
//         .update({ _id: result._id, 'store.0': { $exists: false } }, { store: [result.store] }, { multi: true })
//         .exec((err, result) => {
//           console.log('haha:err:UserProductTypeModel', index, err, result);
//         })
//     })
//   })
