const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const OrderStoreModel = require('./lib/models/orderStore');
const ProductModel = require('./lib/models/product');
const UserStoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const tool = require('./lib/utils/tool');
const locationHelper = require('./lib/utils/location');
const limit = 10000;

const sync = () => {
  ProductModel
    .find({ store: { $exists: true }, totalSold: { $exists: false } }, '_id', { limit })
    .lean()
    .exec((err, results) => {
      if (err) {
        return console.log('haha:err', err);
      }

      if (!results || !results.length) {
        // setTimeout(() => {
        //   sync();
        // }, 1000);

        return console.log('haha:Product not found');
      }

      async.mapLimit(results, 1000, (item, done) => {
        OrderStoreModel
          .count({ 'cart._id': item._id })
          .lean()
          .exec((err, count) => {
            if (err) {
              done(err);
              return console.log('haha:err1', i, err);
            }

            ProductModel
              .update({
                _id: item._id
              }, {
                totalSold: count
              })
              .lean()
              .exec((err, result) => {
                if (err) {
                  done(err);
                  return console.log('haha:err2', err);
                }

                console.log('haha:result', result);

                done();
              })
          })
      }, (err, result) => {
        if (err) {
          console.log(err)
        }

        setTimeout(() => {
          sync();
        }, 1000);
      })
    })
}

sync();
