// get store
// get address five star
// create branch

const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const UserProductTypeModel = require('./lib/models/userProductType');
const ProductTypeModel = require('./lib/models/productType');
const UserToppingModel = require('./lib/models/userTopping');
const UserToppingGroupModel = require('./lib/models/userToppingGroup');
const ToppingGroupModel = require('./lib/models/toppingGroup');
const UserProductModel = require('./lib/models/userProduct');
const ProductModel = require('./lib/models/product');
const StoreLogModel = require('./lib/models/storeLog');
const StoreTypeModel = require('./lib/models/storeTypes');
const MemberModel = require('./lib/models/member');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const moment = require('moment')
const ms = require('ms')
const tool = require('./lib/utils/tool');
const inactiveUserProductType = require('./lib/routes/admin/userProductType/inactive');
const confirmProductType = require('./lib/routes/admin/userProductType/confirm');
const confirmTopping = require('./lib/routes/admin/userTopping/confirmTopping');
const confirmToppingGroup = require('./lib/routes/admin/userTopping/confirm');
const inactiveProduct = require('./lib/routes/admin/product/inactive');

var workbook = new Excel.Workbook();
let index = 0;
let region = '';
const storeAddr = 'https://stores.heyu.asia';

const storeArr = [
  "64b5efab0c02150d1b4f7f51",
  "64b5efac0c02150d1b4f81a8",
  "64b5efab0c02150d1b4f7ed0",
  "64b5efac0c02150d1b4f7fa4",
  "64b5efab0c02150d1b4f7f55",
  "64b5efab0c02150d1b4f7eed",
  "64b5efac0c02150d1b4f8194",
  "64b5efac0c02150d1b4f7fa2",
  "64b5efab0c02150d1b4f7ece",
  "64b5efab0c02150d1b4f7f59",
  "64b5efab0c02150d1b4f7eca",
  "64b5efac0c02150d1b4f819c",
  "64b5efab0c02150d1b4f7ec8",
  "64b5efac0c02150d1b4f8020",
  "64b5efac0c02150d1b4f8b4d",
  "64b5efac0c02150d1b4f8196",
  "64b5efab0c02150d1b4f7f53",
  "64b5efad0c02150d1b4f8b5f",
  "64b5efab0c02150d1b4f7f24",
  "64b5efac0c02150d1b4f801c",
  "64b5efac0c02150d1b4f7fae",
  "64b5efac0c02150d1b4f7f9e",
  "64b5efad0c02150d1b4f96ba",
  "64b5efac0c02150d1b4f8014",
  "64b5efab0c02150d1b4f7f5b",
  "64b5efad0c02150d1b4f8b59",
  "64b5efab0c02150d1b4f7f26",
  "64b5efac0c02150d1b4f81a6",
  "64b5efab0c02150d1b4f7f4f",
  "64b5efac0c02150d1b4f7fa0",
  "64b5efac0c02150d1b4f802a",
  "64b5efac0c02150d1b4f81ae",
  "64b5efab0c02150d1b4f7f22",
  "64b5efac0c02150d1b4f8026",
  "64b5efac0c02150d1b4f7fa8",
  "64b5efab0c02150d1b4f7f57",
  "64b5efac0c02150d1b4f8024",
  "64b5efac0c02150d1b4f801e",
  "64b5efac0c02150d1b4f7fac",
  "64b5efac0c02150d1b4f81ac",
  "64b5efac0c02150d1b4f8b4f",
  "64b5efad0c02150d1b4f8b61",
  "64b5efac0c02150d1b4f819a",
  "64b5efac0c02150d1b4f81a0",
  "64b5efac0c02150d1b4f8012",
  "64b5efac0c02150d1b4f8b53",
  "64b5efac0c02150d1b4f8018",
  "64b5efab0c02150d1b4f7ecc",
  "64b5efab0c02150d1b4f7f4d",
  "64b5efac0c02150d1b4f819e",
  "64b5efab0c02150d1b4f7f20",
  "64b5efad0c02150d1b4f8b63",
  "64b5efac0c02150d1b4f81a4",
  "64b5efad0c02150d1b4f8b57",
  "64b5efad0c02150d1b4f96bc",
  "64b5efac0c02150d1b4f81a2",
  "64b5efad0c02150d1b4f8b5b",
  "64b5efab0c02150d1b4f7f1c",
  "64b5efab0c02150d1b4f7f5d",
  "64b5efac0c02150d1b4f8198",
  "64b5efad0c02150d1b4f8b5d",
  "64b5efac0c02150d1b4f801a",
  "64b5efab0c02150d1b4f7f1e",
  "64b5efab0c02150d1b4f7f1a",
  "64b5efac0c02150d1b4f81aa",
  "64b5efac0c02150d1b4f7faa",
  "64b5efab0c02150d1b4f7f4b",
  "64b5efac0c02150d1b4f8b55",
  "64b5efac0c02150d1b4f8028",
  "64b5efac0c02150d1b4f7fa6",
  "64b5efac0c02150d1b4f8016",
  "64b5efac0c02150d1b4f8b51",
  "64b5efac0c02150d1b4f8022"
]

// const stores = [
//   "63ddd12c1eb1a77a6d2d2497",
//   "64b5efac0c02150d1b4f8b55",
//   "64b5efac0c02150d1b4f8014",
//   "64b5efad0c02150d1b4f8b57",
//   "64b5efad0c02150d1b4f8b61",
//   "64b5efac0c02150d1b4f8198",
//   "64b5efac0c02150d1b4f8028",
//   "64b5efac0c02150d1b4f8018",
//   "64b5efac0c02150d1b4f819a",
//   "64b5efab0c02150d1b4f7f5b",
//   "64b5efab0c02150d1b4f7f57",
//   "64b5efac0c02150d1b4f7fa6",
//   "64b5efab0c02150d1b4f7f51",
//   "64b5efac0c02150d1b4f7fa4",
//   "64b5efab0c02150d1b4f7f55",
//   "64b5efab0c02150d1b4f7eca",
//   "64b5efac0c02150d1b4f8194",
//   "64b5efab0c02150d1b4f7f1e",
//   "64b5efac0c02150d1b4f8b51",
//   "64b5efab0c02150d1b4f7ecc",
//   "64b5efac0c02150d1b4f819e",
//   "64b5efac0c02150d1b4f8022",
//   "64b5efac0c02150d1b4f81a0",
//   "64b5efab0c02150d1b4f7f1c",
//   "64b5efac0c02150d1b4f8b53",
//   "64b5efab0c02150d1b4f7f1a",
//   "64b5efac0c02150d1b4f801a",
//   "64b5efac0c02150d1b4f8016",
//   "64b5efab0c02150d1b4f7f4d",
//   "64b5efab0c02150d1b4f7ec8",
//   "64b5efac0c02150d1b4f802a",
//   "64b5efac0c02150d1b4f8196",
//   "64b5efac0c02150d1b4f819c",
//   "64b5efab0c02150d1b4f7f4f",
//   "64b5efac0c02150d1b4f8b4d",
//   "64b5efac0c02150d1b4f7fa0",
//   "64b5efac0c02150d1b4f8024",
//   "64b5efac0c02150d1b4f801e",
//   "64b5efab0c02150d1b4f7f53",
//   "64b5efab0c02150d1b4f7f26",
//   "64b5efac0c02150d1b4f8020",
//   "64b5efac0c02150d1b4f81ae",
//   "64b5efac0c02150d1b4f81aa",
//   "64b5efad0c02150d1b4f8b5f",
//   "64b5efab0c02150d1b4f7f22",
//   "64b5efac0c02150d1b4f7fae",
//   "64b5efab0c02150d1b4f7f20",
//   "64b5efac0c02150d1b4f81ac",
//   "64b5efac0c02150d1b4f7faa",
//   "64b5efac0c02150d1b4f8026",
//   "64b5efac0c02150d1b4f7f9e",
//   "64b5efad0c02150d1b4f8b63",
//   "64b5efab0c02150d1b4f7f24",
//   "64b5efab0c02150d1b4f7f4b",
//   "64b5efad0c02150d1b4f96ba",
//   "64b5efac0c02150d1b4f8012",
//   "64b5efac0c02150d1b4f81a4",
//   "64b5efad0c02150d1b4f8b59",
//   "64b5efac0c02150d1b4f81a6",
//   "64b5efac0c02150d1b4f8b4f",
//   "64b5efac0c02150d1b4f81a8",
//   "64b5efac0c02150d1b4f7fa8",
//   "64b5efac0c02150d1b4f81a2"
// ]

const stores = [
  '61698440ff5ce47ef9ef3fc2',
  '635f4940b3b921e9a4ce7572',
  '635f49d1504467e9cb094a7d',
  '635f4b68a0d5a6ea89ecc20f',
  '635f4ba52771abeade6ec9f9',
  '636b5c09d4c49e4845e1dbfe',
  '636b60189d7290517f9f5b7a',
  '636c5eacc2ef977f3e9873bf',
  '636c5fbfc2ef977f3e9873fe',
  '636cb152991893bc15503f91',
  '636f06ad15a7ae534b273d36'
];

// UserProductModel
//   .find({ store: { $in: storeArr } })
//   // .find({ store: '637891494543934e37870fef' })
//   .lean()
//   .exec((err, results) => {
//     if (err) {
//       return console.log('haha:err', err)
//     }

//     ProductModel
//       .create(results, (err, result) => {
//         console.log('haha:err', err, result)
//       })

//     return;

//     results.map(resu => {
//       // console.log('haha', resu._id, resu.store)
//       if (resu.store.length) {
//         UserProductModel
//           .update({ _id: resu._id }, { member: resu.store[0].member })
//           .exec((err, result) => {
//             ProductModel
//               .update({ _id: resu._id }, { member: resu.store[0].member })
//               .exec((err, result) => {
//                 console.log('haha:err', err, result)
//               })
//           })
//       }
//     })

//     return;
//     // 0355917638


//     storeArr.map(store => {
//       UserStoreModel
//         .findOne({ _id: store }, 'member')
//         .lean()
//         .exec((error, data) => {
//           const productNewArr = results.map(result => {
//             delete result._id;
//             result.member = data.member;
//             result.store = [store];
//             result.isAvailable = [store];

//             return result;
//           })

//           // return console.log('productNewArr', productNewArr)

//           UserProductModel
//             .create(productNewArr, (err, result) => {
//               console.log('haha:err', err, result)
//             })
//         })
//     })
//   })

// return;

workbook.xlsx.readFile(`${ABSPATH}/menu.xlsx`)
  .then(function () {
    workbook.eachSheet(function(worksheet, sheetId) {
      console.log('haha:worksheet', worksheet.name, sheetId)
    });

    const removeOldProduct = (next) => {
      async.mapLimit(stores, 5, (item, done) => {
        let count = 0;

        UserProductModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveUserProduct', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })

        ProductModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveProduct', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })


        UserProductTypeModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveUserProductType', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })

        ProductTypeModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveProductType', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })

        UserToppingGroupModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveUserToppingGroup', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })

        ToppingGroupModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveToppingGroup', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })

        UserToppingModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveUserTopping', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })

        ToppingModel
          .update({
            store: item,
            status: 1
          }, {
            status: 0,
            updatedAt: Date.now()
          }, { multi: true })
          .exec((err, result) => {
            console.log('haha inactiveTopping', err, result);

            count++;
            if (count >= 8) {
              done();
            }
          })
      }, (err, result) => {
        console.log('haha removeOldProduct', err, result)

        next();
      })
    }

    const addNewProductType = (next) => {
      const worksheet = workbook.getWorksheet(5);
      worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
        if (rowNumber >= 4 && rowNumber <= 11 && row.values && row.values.length) {
          stores.map(store => {
            UserStoreModel
              .findOne({
                _id: store
              }, 'member')
              .lean()
              .exec((errStore, resultStore) => {
                console.log('haha err get store', errStore, resultStore);

                if (resultStore && resultStore.member) {
                  let objCreate = {
                    name: row.values[2].trim(),
                    store: [store],
                    member: resultStore.member,
                    nameAlias: tool.change_alias(row.values[2].trim())
                  }

                  UserProductTypeModel
                    .create(objCreate, (err, data) => {
                      console.log('haha create user product type', err, data)

                      if (data && data._id) {
                        confirmProductType({
                          body: {
                            id: data._id,
                            level: 1,
                            store,
                            approveReason: 'Duyệt danh mục'
                          }
                        }, {
                          json: (data2) => {
                            console.log('haha create product type', data2)
                          }
                        })
                      }
                    })
                }
              })
          })
        }
      })

      next();
    }

    const addNewTopping = (next) => {
      const worksheet = workbook.getWorksheet(3);
      worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
        if (rowNumber >= 2 && rowNumber <= 13 && row.values && row.values.length) {
          stores.map(store => {
            UserStoreModel
              .findOne({
                _id: store
              }, 'member')
              .lean()
              .exec((errStore, resultStore) => {
                console.log('haha err get store', errStore, resultStore);

                if (resultStore && resultStore.member) {
                  let objCreate = {
                    name: row.values[5].trim(),
                    store: [store],
                    member: resultStore.member,
                    price: Number(row.values[6]),
                    isAvailable: 1,
                    nameAlias: tool.change_alias(row.values[5].trim())
                  }

                  UserToppingModel
                    .create(objCreate, (err, result) => {
                      console.log('haha create user topping', err, data)

                      ToppingLogModel
                        .create({
                          type: 0,
                          topping: result._id,
                          member: resultStore.member,
                          data: result
                        })

                      if (result && result._id) {
                        confirmTopping({
                          body: {
                            id: result._id,
                            level: 1,
                            store,
                            approveReason: 'Duyệt Topping'
                          }
                        }, {
                          json: (data2) => {
                            console.log('haha create topping', data2)
                          }
                        })
                      }
                    })
                }
              })
          })
        }
      })

      next();
    }

    const addNewToppingGroup = (next) => {
      const worksheet = workbook.getWorksheet(3);
      worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
        if (rowNumber >= 2 && rowNumber <= 13 && row.values && row.values.length && row.values[2]) {
          stores.map(store => {
            UserStoreModel
              .findOne({
                _id: store
              }, 'member')
              .lean()
              .exec((errStore, resultStore) => {
                console.log('haha err get store', errStore, resultStore);

                if (resultStore && resultStore.member) {
                  let objCreate = {
                    name: row.values[2].trim(),
                    store: [store],
                    member: resultStore.member,
                    // price: Number(row.values[6]),
                    topping: [],
                    isRequire: Number(row.values[3].trim()),
                    minSelect: Number(row.values[3].trim()),
                    maxSelect: Number(row.values[4].trim()),
                    isAvailable: 1,
                    nameAlias: tool.change_alias(row.values[5].trim())
                  }

                  UserToppingGroupModel
                    .create(objCreate, (err, result) => {
                      console.log('haha create user topping Group', err, result)

                      ToppingGroupLogModel
                        .create({
                          type: 0,
                          toppingGroup: result._id,
                          member: resultStore.member,
                          data: result
                        })

                      if (result && result._id) {
                        confirmToppingGroup({
                          body: {
                            id: result._id,
                            level: 1,
                            store,
                            approveReason: 'Duyệt Topping Group'
                          }
                        }, {
                          json: (data2) => {
                            console.log('haha create topping Group', data2)
                          }
                        })
                      }
                    })
                }
              })
          })
        }
      })

      next();
    }

    // return;
    const worksheet = workbook.getWorksheet(3);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      console.log('haha:row', rowNumber, row.values)
      return;

      if (rowNumber >= 2 && rowNumber < 75 && row.values && row.values.length) {
        let storeInf = {};
        let storeId;
        let objUpdate = {}
        let productTypes = [];
        let toppings = [];
        let toppingGroups = [];
        const phone = row.values[4].trim().replace(/ /g, '');

        const getStoreInf = (next) => {
          const date = new Date().setHours(0, 0, 0, 0);

          UserStoreModel
            .findOne({
              phone: '0777818411',
              name: 'Gà Rán Five Star - Tên Đường'
            })
            .lean()
            .exec((err, result) => {
              if (err || !result) {
                return next(err || new Error('Store not found'));
              }

              storeInf = result;
              objUpdate = {
                image: result.image,
                background: result.background,
              }

              next();
            })
        }

        const updateStore = (next) => {
          UserStoreModel
            .findOne({
              phone, level: 1
            })
            .sort({ createdAt: -1 })
            .lean()
            .exec((err, result) => {
              if (err) {
                return next(err)
              }

              storeId = result._id;

              next();
            })
        }

        const getTopping = (next) => {
          UserToppingModel
            .find({
              store: storeId
            })
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err)
              }

              toppings = results;

              next();
            })
        }

        const getToppingGroup = (next) => {
          UserToppingGroupModel
            .find({
              store: storeId
            })
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err)
              }

              toppingGroups = results;

              next();
            })
        }

        const getProductType = (next) => {
          UserProductTypeModel
            .find({
              store: storeId
            })
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err)
              }

              productTypes = results;

              next();
            })
        }

        const updateToppingGroup = (next) => {
          UserToppingGroupModel
            .find({
              store: storeInf._id
            }, 'topping nameAlias')
            .populate('topping')
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err);
              }

              results.map(store => {
                let topping = [];

                UserToppingGroupModel
                  .findOne({
                    store: storeId,
                    nameAlias: store.nameAlias
                  })
                  .lean()
                  .exec((err, tg) => {
                    if (err) {
                      return next(err)
                    }

                    store.topping.map(top => {
                      const index = _.findIndex(toppings, (value) => value.nameAlias === top.nameAlias);

                      if (index > -1) {
                        topping.push(top._id);
                      }
                    })

                    store.topping = topping;

                    UserToppingGroupModel
                      .update({
                        _id: tg._id
                      }, {
                        topping
                      })
                      .exec((err, result) => {
                        ToppingGroupModel
                          .update({
                            _id: tg._id
                          }, {
                            topping
                          })
                          .exec((err, result) => {

                          })
                      })
                  })
              })

              next();
            })
        }

        const updateProduct = (next) => {
          UserProductModel
            .find({
              store: storeInf._id
            }, 'productType, topping nameAlias')
            .populate('productType')
            .populate('topping')
            .lean()
            .exec((err, results) => {
              if (err) {
                return next(err);
              }

              results.map(store => {
                let topping = [];
                let productType = [];

                UserProductModel
                  .findOne({
                    store: storeId,
                    nameAlias: store.nameAlias
                  })
                  .lean()
                  .exec((err, tg) => {
                    if (err) {
                      return next(err);
                    }

                    store.topping.map(top => {
                      const index = _.findIndex(toppingGroups, (value) => value.nameAlias === top.nameAlias);

                      if (index > -1) {
                        topping.push(top._id);
                      }
                    })
                    store.productType.map(top => {
                      const index = _.findIndex(productTypes, (value) => value.nameAlias === top.nameAlias);

                      if (index > -1) {
                        productType.push(top._id);
                      }
                    })

                    store.topping = topping;
                    store.productType = productType;
                    UserProductModel
                      .update({
                        _id: tg._id
                      }, {
                        topping,
                        productType
                      })
                      .exec((err, result) => {
                        ProductModel
                          .update({
                            _id: tg._id
                          }, {
                            topping,
                            productType
                          })
                          .exec((err, result) => {

                          })
                      })
                  })
              })

              next();
            })
        }

        async.waterfall([
          getStoreInf,
          updateStore,
          getTopping,
          getToppingGroup,
          getProductType,
          updateToppingGroup,
          updateProduct
        ], (err, data) => {
          index++;
          console.log('haha:result', rowNumber, index);
          console.log('haha:err', err, data);
        })
      }
    });
  });
