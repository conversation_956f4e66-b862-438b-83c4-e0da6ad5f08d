// get store
// get address five star
// create branch

const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const UserProductTypeModel = require('./lib/models/userProductType');
const UserToppingModel = require('./lib/models/userTopping');
const UserToppingGroupModel = require('./lib/models/userToppingGroup');
const UserProductModel = require('./lib/models/userProduct');
const StoreLogModel = require('./lib/models/storeLog');
const StoreTypeModel = require('./lib/models/storeTypes');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const moment = require('moment')
const ms = require('ms')
const tool = require('./lib/utils/tool');

var workbook = new Excel.Workbook();
let index = 0;
let region = 'hcm';

workbook.xlsx.readFile(`${ABSPATH}/DSHeyU.xlsx`)
  .then(function () {
    var worksheet = workbook.getWorksheet(3);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber >= 5) {
        // if (row.values && row.values.length) {
        //   if (row.values[5].trim() === 'CẦN THƠ') {
        //     region = 'vietnam:cantho';
        //   } else if (row.values[5].trim() === 'Đà Nẵng') {
        //     region = 'vietnam:danang';
        //   } else if (row.values[5].trim() === 'HẢI PHÒNG') {
        //     region = 'vietnam:haiphong';
        //   } else if (row.values[5].trim() === 'Hải Dương') {
        //     region = 'vietnam:haiduong';
        //   } else if (row.values[5].trim() === 'NGHỆ AN') {
        //     region = 'vietnam:nghean';
        //   } else if (row.values[5].trim() === 'THANH HÓA') {
        //     region = 'vietnam:thanhhoa';
        //   } else if (row.values[5].trim() === 'Hà Tĩnh') {
        //     region = 'vietnam:hatinh';
        //   } else if (row.values[5].trim() === 'Hà Nội') {
        //     region = 'hn';
        //   } else if (row.values[5].trim() === 'HCM') {
        //     region = 'hcm';
        //   } else if (row.values[5].trim() === 'THÁI NGUYÊN') {
        //     region = 'vietnam:thainguyen';
        //   }
        // }

        // rowArr.push(row);
        const from = row.values[7].trim().split('-')[0].trim();
        const to = row.values[7].trim().split('-')[1].trim();

        // console.log('haha', moment(from, 'hh:mm').valueOf() - new Date().setHours(0, 0, 0, 0))
        // console.log('haha:row', row.values[3], region, rowNumber, from, to);

        let storeInf = {};
        let storeType;
        let storeId;
        let objCreate = {}

        const getStoreInf = (next) => {
          const date = new Date().setHours(0, 0, 0, 0);

          UserStoreModel
            .findOne({
              phone: '0966717874'
            })
            .lean()
            .exec((err, result) => {
              if (err || !result) {
                return next(err || new Error('Store not found'));
              }

              storeInf = result;
              objCreate = {
                name: row.values[2].trim(),
                nameAlias: tool.change_alias(row.values[2].trim()),
                phone: [row.values[6].trim().replace(/ /g, '')],
                member: result.member,
                image: result.image,
                background: result.background,
                address: row.values[3].trim(),
                subAddress: '',
                description: result.description,
                location: {},
                workingTime: {
                  "0": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "1": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "2": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "3": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "4": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "5": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ],
                  "6": [
                    {
                      "startTime": moment(from, 'hh:mm').valueOf() - date,
                      "endTime": moment(to, 'hh:mm').valueOf() - date
                    }
                  ]
                },
                businessType: result.businessType,
                subType: result.subType,
                service: result.service,
                productSearch: result.productSearch,
                region,
                hasProduct: 1,
                levelStatusProduct: 0,
                messageGoLive: 'HeyU sẽ duyệt toàn bộ thông tin sản phẩm, thông tin cửa hàng bạn cung cấp. Vì thế hãy cập nhật hết sản phẩm của Gian Hàng để đạt yêu cầu đưa lên các mục mua sắm của HeyU.'
              }

              if (result.type) {
                objCreate.type = result.type;
              }

              next();
            })
        }

        checkWorkingTime = (next) => {

        }

        const getLocation = (next) => {
          const options = {
            method: 'POST',
            uri: `${config.proxyRequestServer.google}/api/v1.0/google/name-to-location`,
            body: {
              text: row.values[4].trim()
            },
            json: true // Automatically stringifies the body to JSON
          };

          rp(options)
            .then((result) => {
              if (result.code === 200) {
                objCreate[location.coordinates] = 'Point';
                objCreate[location.type] = [result.data.lng, result.data.lat];
              } else {
                objCreate.location = storeInf.location;
              }

              next();
            })
            .catch((err) => {
              objCreate.location = storeInf.location;

              next();
            });
        }

        const determineRegion = (next) => {
          const location = {
            lat: req.body.location.coordinates[1],
            lng: req.body.location.coordinates[0]
          }

          locationHelper
            .getRegionByLatLng(location, 2, (err, regionName) => {
              if (err) {
                return next(err);
              }

              objCreate.region = regionName;

              next();

            })
        }

        const createStoreType = (next) => {
          if (storeInf.type) {
            return next();
          }

          StoreTypeModel
            .create({
              name: 'Rau Má Mix',
              nameAlias: tool.change_alias('Rau Má Mix'),
              status: 1
            }, (err, result) => {
              objCreate.type = result._id;

              next();
            })
        }

        const updateStore = (next) => {
          if (storeInf.type) {
            return next();
          }

          UserStoreModel
            .update({
              member: storeInf.member,
              type: { $exists: false }
            }, {
              type: objCreate.type
            }, { multi: true })
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next();
            })
        }

        const createStore = (next) => {
          UserStoreModel
            .create(objCreate, (err, result) => {
              if (err) {
                return next(err)
              }

              storeId = result._id;

              StoreLogModel
                .create({
                  action: 'Tạo chi nhánh mới',
                  level: 0,
                  reason: '',
                  member: storeInf.member,
                  store: result._id,
                  region
                })

              next();
            })
        }

        const updateProductType = (next) => {
          UserProductTypeModel
            .update({
              member: storeInf.member
            }, {
              storeType,
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next();
            })
        }

        const updateTopping = (next) => {
          UserToppingModel
            .update({
              member: storeInf.member
            }, {
              storeType,
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next();
            })
        }

        const updateToppingGroup = (next) => {
          UserToppingGroupModel
            .update({
              member: storeInf.member
            }, {
              storeType,
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next();
            })
        }

        const updateProduct = (next) => {
          UserProductModel
            .update({
              member: storeInf.member
            }, {
              storeType,
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((err, result) => {
              if (err) {
                return next(err);
              }

              next()
            })
        }

        async.waterfall([
          getStoreInf,
          getLocation,
          // determineRegion,
          createStoreType,
          updateStore,
          createStore,
          updateProductType,
          updateTopping,
          updateToppingGroup,
          updateProduct
        ], (err, data) => {
          // err && _.isError(err) && (data = {
          //   code: CONSTANTS.CODE.SYSTEM_ERROR,
          //   message: MESSAGES.SYSTEM.ERROR
          // });
          index++;
          console.log('haha:result', rowNumber, index);
          console.log(data, err);
        })
      }
    });
  });
