version: "3.4"

services:
  ss-product:
    build: ./
    volumes:
      - ./logs:/app/logs # mount từ môi trường gốc vào trong để nếu các bạn thay đổi code thì bên trong sẽ tự động cập nhật
    #  - -/app/node_modules
    environment:
      - TZ=Asia/Ho_Chi_Minh
      - MONGODB_MASTER_INSTANCES=${MONGODB_MASTER_INSTANCES}
      - MONGODB_MASTER_HOST=${MONGODB_MASTER_HOST}
      - MONGODB_MASTER_PORT=${MONGODB_MASTER_PORT}
      - MONGODB_MASTER_DATABASE=${MONGODB_MASTER_DATABASE}
      - MONGODB_MASTER_OPTIONS=${MONGODB_MASTER_OPTIONS}
      - MONGODB_MASTER_MODE=${MONGODB_MASTER_MODE}
      - SENTINEL_INSTANCES=${SENTINEL_INSTANCES}
      - SENTINEL_PASSWORD=${SENTINEL_PASSWORD}
      - REDIS_MASTER_NAME=${REDIS_MASTER_NAME}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_DATABASE=${REDIS_DATABASE}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_OPTIONS=${REDIS_OPTIONS}
      - REDIS_MODE=${REDIS_MODE}
      - PROXY_REQUEST_SERVER=${PROXY_REQUEST_SERVER}
      - EMAIL_INFO=${EMAIL_INFO}
      - LIST_EMAIL_ALERT=${LIST_EMAIL_ALERT}
      - LOG_LEVEL=${LOG_LEVEL}
      - ENVIRONMENT=${ENVIRONMENT}
      - PORT_PRODUCT=${PORT_PRODUCT}
    ports:
      - "${PORT_PRODUCT}:${PORT_PRODUCT}" # phần này ta định nghĩa ở file .env nhé
    restart: unless-stopped
