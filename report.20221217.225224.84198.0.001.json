{"header": {"reportVersion": 2, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20221217.225224.84198.0.001.json", "dumpEventTime": "2022-12-17T22:52:24Z", "dumpEventTimeStamp": "1671292344103", "processId": 84198, "threadId": null, "cwd": "/Users/<USER>/Work/HeyU/ss-product-service", "commandLine": ["node", "syncStore.js"], "nodejsVersion": "v13.9.0", "wordSize": 64, "arch": "x64", "platform": "darwin", "componentVersions": {"node": "13.9.0", "v8": "7.9.317.25-node.28", "uv": "1.34.2", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "79", "nghttp2": "1.40.0", "napi": "5", "llhttp": "2.0.4", "openssl": "1.1.1d", "cldr": "36.0", "icu": "65.1", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "headersUrl": "https://nodejs.org/download/release/v13.9.0/node-v13.9.0-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v13.9.0/node-v13.9.0.tar.gz"}, "osName": "<PERSON>", "osRelease": "22.1.0", "osVersion": "Darwin Kernel Version 22.1.0: Sun Oct  9 20:14:54 PDT 2022; root:xnu-8792.41.9~2/RELEASE_X86_64", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 127110680, "nice": 0, "sys": 149708300, "idle": 232625850, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 14339400, "nice": 0, "sys": 19800410, "idle": 479062560, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 123701000, "nice": 0, "sys": 136243910, "idle": 249702810, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 13222280, "nice": 0, "sys": 17192410, "idle": 483479910, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 110152870, "nice": 0, "sys": 134819740, "idle": 264983550, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 13189370, "nice": 0, "sys": 16329070, "idle": 484804880, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 102265630, "nice": 0, "sys": 134191780, "idle": 273965810, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 13296090, "nice": 0, "sys": 15601100, "idle": 485692570, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 94906700, "nice": 0, "sys": 128169660, "idle": 287636910, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 13581710, "nice": 0, "sys": 15203520, "idle": 485970930, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 90135050, "nice": 0, "sys": 121499600, "idle": 299743120, "irq": 0}, {"model": "Intel(R) Core(TM) i7-8750H CPU @ 2.20GHz", "speed": 2200, "user": 13721170, "nice": 0, "sys": 14936830, "idle": 486208570, "irq": 0}], "networkInterfaces": [{"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}, {"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "fe80::1", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 1}, {"name": "en5", "internal": false, "mac": "ac:de:48:00:11:22", "address": "fe80::aede:48ff:fe00:1122", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 4}, {"name": "en0", "internal": false, "mac": "f0:18:98:0a:16:b8", "address": "fe80::147e:7258:5a2d:c199", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 6}, {"name": "en0", "internal": false, "mac": "f0:18:98:0a:16:b8", "address": "*************", "netmask": "*************", "family": "IPv4"}, {"name": "awdl0", "internal": false, "mac": "0e:a5:c3:1a:a4:9a", "address": "fe80::ca5:c3ff:fe1a:a49a", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 7}, {"name": "llw0", "internal": false, "mac": "0e:a5:c3:1a:a4:9a", "address": "fe80::ca5:c3ff:fe1a:a49a", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 9}, {"name": "utun0", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::6c82:b126:c48c:ad27", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 14}, {"name": "utun1", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::404d:8344:c178:c9ac", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 15}, {"name": "utun2", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::ce81:b1c:bd2c:69e", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 16}, {"name": "utun3", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::40d1:1b6a:a22a:1172", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 17}, {"name": "utun4", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::5516:cc8:9:d1aa", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 18}, {"name": "en8", "internal": false, "mac": "5e:87:30:b3:39:89", "address": "fe80::802:49c:c3d8:d4", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 21}, {"name": "en8", "internal": false, "mac": "5e:87:30:b3:39:89", "address": "***************", "netmask": "***********", "family": "IPv4"}, {"name": "en9", "internal": false, "mac": "a2:8e:e3:07:c9:05", "address": "fe80::a08e:e3ff:fe07:c905", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 22}], "host": "Thai-Son-2.local"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00000001001a038c", "symbol": "report::TriggerNodeReport(v8::Isolate*, node::Environment*, char const*, char const*, std::__1::basic_string<char, std::__1::char_traits<char>, std::__1::allocator<char> > const&, v8::Local<v8::String>) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100093d77", "symbol": "node::OnFatalError(char const*, char const*) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x00000001001d40f7", "symbol": "v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, bool) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x00000001001d4093", "symbol": "v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, bool) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100356b65", "symbol": "v8::internal::Heap::FatalProcessOutOfMemory(char const*) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100358244", "symbol": "v8::internal::Heap::RecomputeLimits(v8::internal::GarbageCollector) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100354fde", "symbol": "v8::internal::Heap::PerformGarbageCollection(v8::internal::GarbageCollector, v8::GCCallbackFlags) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x0000000100352fc6", "symbol": "v8::internal::Heap::CollectGarbage(v8::internal::AllocationSpace, v8::internal::GarbageCollectionReason, v8::GCCallbackFlags) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010035e94a", "symbol": "v8::internal::Heap::AllocateRawWithLightRetrySlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010035e9d1", "symbol": "v8::internal::Heap::AllocateRawWithRetryOrFailSlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010032bd16", "symbol": "v8::internal::Factory::NewFillerObject(int, bool, v8::internal::AllocationType, v8::internal::AllocationOrigin) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x000000010066bb68", "symbol": "v8::internal::Runtime_AllocateInYoungGeneration(int, unsigned long*, v8::internal::Isolate*) [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}, {"pc": "0x00000001009a7dd9", "symbol": "Builtins_CEntry_Return1_DontSaveFPRegs_ArgvOnStack_NoBuiltinExit [/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node]"}], "javascriptHeap": {"totalMemory": 2157420544, "totalCommittedMemory": 2154347232, "usedMemory": 2148419592, "availableMemory": 45849384, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 33328, "capacity": 33040, "used": 33040, "available": 0}, "new_space": {"memorySize": 6291456, "committedMemory": 4060088, "capacity": 3142272, "used": 1203800, "available": 1938472}, "old_space": {"memorySize": 2135830528, "committedMemory": 2135529896, "capacity": 2133337888, "used": 2132964000, "available": 373888}, "code_space": {"memorySize": 692224, "committedMemory": 548896, "capacity": 450720, "used": 450720, "available": 0}, "map_space": {"memorySize": 1314816, "committedMemory": 1145648, "capacity": 833360, "used": 833360, "available": 0}, "large_object_space": {"memorySize": 12980224, "committedMemory": 12980224, "capacity": 12931888, "used": 12931888, "available": 0}, "code_large_object_space": {"memorySize": 49152, "committedMemory": 49152, "capacity": 2784, "used": 2784, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 3142272, "used": 0, "available": 3142272}}}, "resourceUsage": {"userCpuSeconds": 71.3887, "kernelCpuSeconds": 8.35611, "cpuConsumptionPercent": 59.5111, "maxRss": 2329684213760, "pageFaults": {"IORequired": 26, "IONotRequired": 620737}, "fsActivity": {"reads": 0, "writes": 0}}, "libuv": [], "workers": [], "environmentVariables": {"NVM_INC": "/Users/<USER>/.nvm/versions/node/v13.9.0/include/node", "rvm_bin_path": "/Users/<USER>/.rvm/bin", "TERM_PROGRAM": "Apple_Terminal", "GEM_HOME": "/Users/<USER>/.rvm/gems/ruby-2.7.4", "NVM_CD_FLAGS": "", "ANDROID_HOME": "/Users/<USER>/Library/Android/sdk", "TERM": "xterm-256color", "SHELL": "/bin/bash", "IRBRC": "/Users/<USER>/.rvm/rubies/ruby-2.7.4/.irbrc", "TMPDIR": "/var/folders/c7/lydhxyxx28l1yf7js90g4bw00000gn/T/", "TERM_PROGRAM_VERSION": "447", "OLDPWD": "/Users/<USER>/Work/HeyU", "MY_RUBY_HOME": "/Users/<USER>/.rvm/rubies/ruby-2.7.4", "TERM_SESSION_ID": "39759190-8FC4-4C0A-BED3-F64766F4013D", "NVM_DIR": "/Users/<USER>/.nvm", "USER": "ownere", "rvm_path": "/Users/<USER>/.rvm", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.ufLNmYlcC6/Listeners", "rvm_prefix": "/Users/<USER>", "PATH": "/Users/<USER>/.rvm/gems/ruby-2.7.4/bin:/Users/<USER>/.rvm/gems/ruby-2.7.4@global/bin:/Users/<USER>/.rvm/rubies/ruby-2.7.4/bin:/Users/<USER>/.nvm/versions/node/v13.9.0/bin:/Library/Java/JavaVirtualMachines/adoptopenjdk-8.jdk/Contents/Home/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Library/Apple/usr/bin:/Users/<USER>/Library/Android/sdk/emulator:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/tools/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/.rvm/bin", "__CFBundleIdentifier": "com.apple.Terminal", "PWD": "/Users/<USER>/Work/HeyU/ss-product-service", "JAVA_HOME": "/Library/Java/JavaVirtualMachines/adoptopenjdk-8.jdk/Contents/Home", "XPC_FLAGS": "0x0", "XPC_SERVICE_NAME": "0", "rvm_version": "1.29.12-next (master)", "HOME": "/Users/<USER>", "SHLVL": "1", "LOGNAME": "ownere", "GEM_PATH": "/Users/<USER>/.rvm/gems/ruby-2.7.4:/Users/<USER>/.rvm/gems/ruby-2.7.4@global", "LC_CTYPE": "UTF-8", "NVM_BIN": "/Users/<USER>/.nvm/versions/node/v13.9.0/bin", "ANDROID_NDK_HOME": "/usr/local/share/android-ndk", "RUBY_VERSION": "ruby-2.7.4", "_": "/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node", "__CF_USER_TEXT_ENCODING": "0x1F5:0x0:0x0"}, "userLimits": {"core_file_size_blocks": {"soft": 0, "hard": "unlimited"}, "data_seg_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "file_size_blocks": {"soft": "unlimited", "hard": "unlimited"}, "max_locked_memory_bytes": {"soft": "unlimited", "hard": "unlimited"}, "max_memory_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "open_files": {"soft": 1048575, "hard": "unlimited"}, "stack_size_bytes": {"soft": 8388608, "hard": 67104768}, "cpu_time_seconds": {"soft": "unlimited", "hard": "unlimited"}, "max_user_processes": {"soft": 5568, "hard": 8352}, "virtual_memory_kbytes": {"soft": "unlimited", "hard": "unlimited"}}, "sharedObjects": ["/Users/<USER>/.nvm/versions/node/v13.9.0/bin/node", "/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation", "/usr/lib/libobjc.A.dylib", "/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal", "/usr/lib/liboah.dylib", "/usr/lib/libfakelink.dylib", "/usr/lib/libicucore.A.dylib", "/usr/lib/libSystem.B.dylib", "/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking", "/usr/lib/libc++abi.dylib", "/usr/lib/libc++.1.dylib", "/usr/lib/system/libcache.dylib", "/usr/lib/system/libcommonCrypto.dylib", "/usr/lib/system/libcompiler_rt.dylib", "/usr/lib/system/libcopyfile.dylib", "/usr/lib/system/libcorecrypto.dylib", "/usr/lib/system/libdispatch.dylib", "/usr/lib/system/libdyld.dylib", "/usr/lib/system/libkeymgr.dylib", "/usr/lib/system/libmacho.dylib", "/usr/lib/system/libquarantine.dylib", "/usr/lib/system/libremovefile.dylib", "/usr/lib/system/libsystem_asl.dylib", "/usr/lib/system/libsystem_blocks.dylib", "/usr/lib/system/libsystem_c.dylib", "/usr/lib/system/libsystem_collections.dylib", "/usr/lib/system/libsystem_configuration.dylib", "/usr/lib/system/libsystem_containermanager.dylib", "/usr/lib/system/libsystem_coreservices.dylib", "/usr/lib/system/libsystem_darwin.dylib", "/usr/lib/system/libsystem_dnssd.dylib", "/usr/lib/system/libsystem_featureflags.dylib", "/usr/lib/system/libsystem_info.dylib", "/usr/lib/system/libsystem_m.dylib", "/usr/lib/system/libsystem_malloc.dylib", "/usr/lib/system/libsystem_networkextension.dylib", "/usr/lib/system/libsystem_notify.dylib", "/usr/lib/system/libsystem_sandbox.dylib", "/usr/lib/system/libsystem_secinit.dylib", "/usr/lib/system/libsystem_kernel.dylib", "/usr/lib/system/libsystem_platform.dylib", "/usr/lib/system/libsystem_pthread.dylib", "/usr/lib/system/libsystem_symptoms.dylib", "/usr/lib/system/libsystem_trace.dylib", "/usr/lib/system/libunwind.dylib", "/usr/lib/system/libxpc.dylib", "/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit", "/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices", "/usr/lib/libDiagnosticMessagesClient.dylib", "/usr/lib/libenergytrace.dylib", "/usr/lib/libbsm.0.dylib", "/usr/lib/libz.1.dylib", "/usr/lib/system/libkxld.dylib", "/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList", "/System/Library/Frameworks/Security.framework/Versions/A/Security", "/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration", "/usr/lib/libapple_nghttp2.dylib", "/usr/lib/libcompression.dylib", "/usr/lib/libnetwork.dylib", "/usr/lib/libsqlite3.dylib", "/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation", "/System/Library/Frameworks/Network.framework/Versions/A/Network", "/usr/lib/libCoreEntitlements.dylib", "/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity", "/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer", "/usr/lib/libMobileGestalt.dylib", "/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression", "/usr/lib/libcoretls.dylib", "/usr/lib/libcoretls_cfhelpers.dylib", "/usr/lib/libpam.2.dylib", "/usr/lib/libxar.1.dylib", "/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout", "/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration", "/usr/lib/libarchive.2.dylib", "/usr/lib/libxml2.2.dylib", "/usr/lib/liblangid.dylib", "/System/Library/Frameworks/Combine.framework/Versions/A/Combine", "/usr/lib/swift/libswiftCore.dylib", "/usr/lib/swift/libswiftCoreFoundation.dylib", "/usr/lib/swift/libswiftDarwin.dylib", "/usr/lib/swift/libswiftDispatch.dylib", "/usr/lib/swift/libswiftIOKit.dylib", "/usr/lib/swift/libswiftObjectiveC.dylib", "/usr/lib/swift/libswiftXPC.dylib", "/usr/lib/swift/libswift_Concurrency.dylib", "/usr/lib/swift/libswift_StringProcessing.dylib", "/usr/lib/swift/libswiftos.dylib", "/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo", "/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer", "/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface", "/usr/lib/libpcap.A.dylib", "/usr/lib/libdns_services.dylib", "/usr/lib/liblzma.5.dylib", "/usr/lib/libbz2.1.0.dylib", "/usr/lib/libiconv.2.dylib", "/usr/lib/libcharset.1.dylib", "/usr/lib/swift/libswift_RegexParser.dylib", "/usr/lib/libheimdal-asn1.dylib", "/usr/lib/libCheckFix.dylib", "/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC", "/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP", "/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate", "/usr/lib/libmecab.dylib", "/usr/lib/libCRFSuite.dylib", "/usr/lib/libgermantok.dylib", "/usr/lib/libThaiTokenizer.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib", "/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL", "/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory", "/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory", "/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS", "/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation", "/usr/lib/libutil.dylib", "/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary", "/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore", "/usr/lib/libapp_launch_measurement.dylib", "/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity", "/usr/lib/libmis.dylib", "/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices", "/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper", "/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics", "/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce", "/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling", "/usr/lib/libxslt.1.dylib", "/usr/lib/libcmph.dylib", "/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji", "/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData", "/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon", "/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement", "/usr/lib/libTLE.dylib"]}