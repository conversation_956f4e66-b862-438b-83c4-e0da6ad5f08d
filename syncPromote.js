const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const PromoteStore = require('./lib/models/promoteStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const tool = require('./lib/utils/tool');
const locationHelper = require('./lib/utils/location');

PromoteStore
  .find({})
  .lean()
  .exec((err, results) => {
    if (err) {
      return console.log('haha:err', err);
    }

    if (!results || !results.length) {
      return console.log('haha:no promote');
    }

    results.forEach((item, i) => {
      let value = '';
      if (item.strategy.type === 'percent') {
        value = `${item.strategy.value}%`;
      } else {
        value = `${Math.round(item.strategy.value / 1000)}K`;
      }
      let codeName = `GIAM${value}`;

      if (item.strategy.minimumPrice) {
        codeName += `_${Math.round(item.strategy.minimumPrice / 1000)}`;
      }

      if (item.strategy.type === 'percent' && item.strategy.maximum) {
        codeName += `_${Math.round(item.strategy.maximum / 1000)}`;
      }

      code = codeName.toUpperCase();

      let content = '';
      if (item.strategy.type === 'direct') {
        if (item.strategy.minimumPrice) {
          content = `Giảm ${item.strategy.value / 1000}k cho đơn từ ${item.strategy.minimumPrice / 1000}k`
        } else {
          content = `Giảm ${item.strategy.value / 1000}k `
        }
      } else if (item.strategy.type === 'percent') {
        if (item.strategy.minimumPrice > 0) {
          if (item.strategy.maximum > 0) {
            content = `Giảm ${item.strategy.value}% tối đa ${item.strategy.maximum / 1000}k cho đơn từ ${item.strategy.minimumPrice / 1000}k`
          } else {
            content = `Giảm ${item.strategy.value}% cho đơn từ ${item.strategy.minimumPrice / 1000}k `
          }
        } else {
          if (item.strategy.maximum > 0) {
            content = `Giảm ${item.strategy.value}% tối đa ${item.strategy.maximum / 1000}k `
          } else {
            content = `Giảm ${item.strategy.value}% `
          }
        }
      }

      PromoteStore
        .update({_id: item._id}, {code, name: content, updatedAt: Date.now()})
        .lean()
        .exec((err, result) => {
          console.log('haha:err', err, result);
        })
      // console.log('haha:code', item.code, item.name)
    })
  })
