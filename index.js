const express = require('express')
const Logger = require('./lib/logger')
const cors = require('cors');

// Global variables
global.logger = Logger(`${__dirname}/logs`);
global.moment = require('moment');
global.config = require('config');
global._ = require('lodash');
global.async = require('async');
global.rp = require('request-promise');
global.bcrypt = require('bcrypt');


// Middleware
const bodyParser = require('body-parser')
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const tokenToUserTickbox = require('./lib/middleware/tokenToUserTickbox');
const getStaffInfMiddleware = require('./lib/middleware/getStaffInf');

// Clon job
const BlockStoreManager = require('./lib/job/blockStoreManager')
const FiveStarJob = require('./lib/job/fiveStarJob');

// Handle routes
const ProductHandle = require('./lib/routes/product')
const ProductTypeHandle = require('./lib/routes/productType')
const OrderTypeHandle = require('./lib/routes/orderType')
const StoreHandle = require('./lib/routes/store')

const AdminProductTypeHandle =  require('./lib/routes/admin/productType')
const AdminProductHandle =  require('./lib/routes/admin/product')
const AdminUserProductTypeHandle =  require('./lib/routes/admin/userProductType')
const AdminStoreHandle =  require('./lib/routes/admin/store')
const AdminUserToppingHandle =  require('./lib/routes/admin/userTopping')
const ServiceHandle = require('./lib/routes/admin/services')
const AdminOrderHandle = require('./lib/routes/admin/order')
const AdminUserStoreHandle = require('./lib/routes/admin/userStore')
const AdminUserProductHandle = require('./lib/routes/admin/userProduct')
const AdminMemberHandle = require('./lib/routes/admin/member')

const UserStoreHandle =  require('./lib/routes/user/store')
const UserProductHandle =  require('./lib/routes/user/product')
const UserProductTypeHandle =  require('./lib/routes/user/productType')
const SyncHandle =  require('./lib/routes/sync')
const OrderHandle =  require('./lib/routes/order')
const StatisticHandle =  require('./lib/routes/user/statistic')
const UserPromoteHandle =  require('./lib/routes/user/promote')
const PromoteHandle =  require('./lib/routes/promote')
const CategoryStrategyHandle =  require('./lib/routes/categoryStrategy');
const UserStaffHandle = require('./lib/routes/user/staff');
const UserToppingGroupHandle = require('./lib/routes/user/toppingGroup');
const UserToppingHandle = require('./lib/routes/user/topping');

// Start server, socket
const app = express();
const server = require('http').Server(app);

app.use(bodyParser.json());
app.use(cors())
// This is for test purpose

app.post('/api/v1.0/product-type/list', ProductTypeHandle.list)
app.post('/api/v1.0/product-type/list-default', ProductTypeHandle.listDefault)

app.post('/api/v1.0/product/list', ProductHandle.list)
app.post('/api/v1.0/product/find-page-product', tokenToUserMiddleware, ProductHandle.findPageProduct)
app.post('/api/v1.0/product/get', tokenToUserMiddleware, ProductHandle.get)

app.post('/api/v1.0/store/list', tokenToUserMiddleware, StoreHandle.list)
app.post('/api/v1.0/store/get', StoreHandle.get)
app.post('/api/v1.0/store/list-with-product', tokenToUserMiddleware, StoreHandle.listStoreProduct)
app.post('/api/v1.0/store/get-config-errand-fee', StoreHandle.getConfigErrandFee)
app.post('/api/v1.0/store/get-banner', tokenToUserMiddleware, StoreHandle.getBanners)
app.post('/api/v1.0/store/config-view-image', tokenToUserMiddleware, StoreHandle.getConfigViewImage)
app.post('/api/v1.0/store/list-branch', StoreHandle.listBranch)

//for admin
app.post('/api/v1.0/admin/product-type/create', AdminProductTypeHandle.create)
app.post('/api/v1.0/admin/product-type/list', AdminProductTypeHandle.list)
app.post('/api/v1.0/admin/product-type/inactive', AdminProductTypeHandle.inactive)
app.post('/api/v1.0/admin/product-type/modify', AdminProductTypeHandle.modify)

app.post('/api/v1.0/admin/product/create', AdminProductHandle.create)
app.post('/api/v1.0/admin/product/list', AdminProductHandle.list)
app.post('/api/v1.0/admin/product/inactive', AdminProductHandle.inactive)
app.post('/api/v1.0/admin/product/modify', AdminProductHandle.modify)

app.post('/api/v1.0/admin/store/statistic', AdminStoreHandle.statistic)
app.post('/api/v1.0/admin/store/nearest', AdminStoreHandle.listNearestStore)
app.post('/api/v1.0/admin/store/unblock', AdminStoreHandle.unBlock)
app.post('/api/v1.0/admin/store/export', AdminStoreHandle.exportStoresData)

app.post('/api/v1.0/admin/user-store/list', tokenToUserTickbox, AdminUserStoreHandle.list)
app.post('/api/v1.0/admin/user-store/confirm', tokenToUserTickbox, AdminUserStoreHandle.confirm)
app.post('/api/v1.0/admin/user-store/update', tokenToUserTickbox, AdminUserStoreHandle.update)
app.post('/api/v1.0/admin/user-store/note', tokenToUserTickbox, AdminUserStoreHandle.note)
app.post('/api/v1.0/admin/user-store/go-live', tokenToUserTickbox, AdminUserStoreHandle.golive)
app.post('/api/v1.0/admin/user-store/go-link', tokenToUserTickbox, AdminUserStoreHandle.golink)
app.post('/api/v1.0/admin/user-store/notifi', tokenToUserTickbox, AdminUserStoreHandle.notifi)
app.post('/api/v1.0/admin/user-store/delete', tokenToUserTickbox, AdminUserStoreHandle.delete)

app.post('/api/v1.0/admin/user-product/list', tokenToUserTickbox, AdminUserProductHandle.list)
app.post('/api/v1.0/admin/user-product/confirm', tokenToUserTickbox, AdminUserProductHandle.confirm)
app.post('/api/v1.0/admin/user-product/update', tokenToUserTickbox, AdminUserProductHandle.update)
app.post('/api/v1.0/admin/user-product/list-business-type', tokenToUserTickbox, AdminUserProductHandle.listBussinessType)
app.post('/api/v1.0/admin/user-product/list-sub-type', tokenToUserTickbox, AdminUserProductHandle.listSubType)
app.post('/api/v1.0/admin/user-product/inactive', tokenToUserTickbox, AdminUserProductHandle.inactive)

app.post('/api/v1.0/admin/user-product-type/confirm', AdminUserProductTypeHandle.confirm)
app.post('/api/v1.0/admin/user-product-type/list', AdminUserProductTypeHandle.list)
app.post('/api/v1.0/admin/user-product-type/inactive', AdminUserProductTypeHandle.inactive)
app.post('/api/v1.0/admin/user-product-type/modify', AdminUserProductTypeHandle.modify)

app.post('/api/v1.0/admin/user-topping/confirm', AdminUserToppingHandle.confirm)
app.post('/api/v1.0/admin/user-topping/confirm-topping', AdminUserToppingHandle.confirmTopping)
app.post('/api/v1.0/admin/user-topping/list', AdminUserToppingHandle.list)
app.post('/api/v1.0/admin/user-topping/inactive', AdminUserToppingHandle.inactive)
app.post('/api/v1.0/admin/user-topping/inactive-group', AdminUserToppingHandle.inactiveToppingGroup)
app.post('/api/v1.0/admin/user-topping/modify', AdminUserToppingHandle.modify)
app.post('/api/v1.0/admin/user-topping/modify-topping', AdminUserToppingHandle.modifyTopping)

app.post('/api/v1.0/admin/member/money-topup', tokenToUserTickbox, AdminMemberHandle.moneyTopup)
app.post('/api/v1.0/admin/member/info', tokenToUserTickbox, AdminMemberHandle.info)
app.post('/api/v1.0/admin/member/history-topup', tokenToUserTickbox, AdminMemberHandle.topupHistory)

app.post('/api/v1.0/admin/services/list', ServiceHandle.list)

app.post('/api/v1.0/admin/order/reject', AdminOrderHandle.reject);
app.post('/api/v1.0/admin/order/confirm', AdminOrderHandle.confirmOrder);
app.post('/api/v1.0/admin/order/statistic', AdminOrderHandle.statistic);

//for shop
app.post('/api/v1.0/user/store/get', tokenToUserMiddleware, getStaffInfMiddleware, UserStoreHandle.get)
app.post('/api/v1.0/user/store/create', tokenToUserMiddleware, UserStoreHandle.create)
app.post('/api/v1.0/user/store/inactive', tokenToUserMiddleware, getStaffInfMiddleware, UserStoreHandle.inactive)
app.post('/api/v1.0/user/store/modify', tokenToUserMiddleware, UserStoreHandle.modify)
app.post('/api/v1.0/user/store/list-business-type', UserStoreHandle.listBusinessType)
app.post('/api/v1.0/user/store/get-banner', tokenToUserMiddleware, UserStoreHandle.getBanners)
app.post('/api/v1.0/user/store/config-business-type', tokenToUserMiddleware, UserStoreHandle.configBusinessType)
app.post('/api/v1.0/user/store/config-sub-type', UserStoreHandle.configSubType)
app.post('/api/v1.0/user/store/list-sub-type', tokenToUserMiddleware, UserStoreHandle.listSubType)
app.post('/api/v1.0/user/store/preview', UserStoreHandle.preview)
app.post('/api/v1.0/user/store/list-news-title', UserStoreHandle.listNewsTitle)
app.post('/api/v1.0/user/store/list-news', UserStoreHandle.listNews)
app.post('/api/v1.0/user/store/create-branch', tokenToUserMiddleware, UserStoreHandle.createBranch);
app.post('/api/v1.0/user/store/list-branch', tokenToUserMiddleware, UserStoreHandle.listBranch);
app.post('/api/v1.0/user/store/choose-branch', tokenToUserMiddleware, UserStoreHandle.chooseBranch);
app.post('/api/v1.0/user/store/config-branch', UserStoreHandle.configBranch);

app.post('/api/v1.0/user/product/get', tokenToUserMiddleware, getStaffInfMiddleware, UserProductHandle.get)
app.post('/api/v1.0/user/product/list', UserProductHandle.list)
app.post('/api/v1.0/user/product/create', tokenToUserMiddleware, getStaffInfMiddleware, UserProductHandle.create)
app.post('/api/v1.0/user/product/inactive', tokenToUserMiddleware, getStaffInfMiddleware, UserProductHandle.inactive)
app.post('/api/v1.0/user/product/modify', tokenToUserMiddleware, getStaffInfMiddleware, UserProductHandle.modify)
app.post('/api/v1.0/user/product/create-by-bar-code', tokenToUserMiddleware, UserProductHandle.createByBarCode)
app.post('/api/v1.0/user/product/list-unit', tokenToUserMiddleware, UserProductHandle.listUnit)
app.post('/api/v1.0/user/product/config-show-barcode', tokenToUserMiddleware, getStaffInfMiddleware, UserProductHandle.configShowBarCode)
app.post('/api/v1.0/user/product/count', tokenToUserMiddleware, UserProductHandle.count)
app.post('/api/v1.0/user/product/list-product-preparing', tokenToUserMiddleware, UserProductHandle.listProductPreparing)
app.post('/api/v1.0/user/product/count-product-preparing', tokenToUserMiddleware, UserProductHandle.countProductPreparing)
app.post('/api/v1.0/user/product/sort', UserProductHandle.sort)

app.post('/api/v1.0/user/product-type/list', UserProductTypeHandle.list)
app.post('/api/v1.0/user/product-type/create', tokenToUserMiddleware, getStaffInfMiddleware, UserProductTypeHandle.create)
app.post('/api/v1.0/user/product-type/inactive', tokenToUserMiddleware, getStaffInfMiddleware, UserProductTypeHandle.inactive)
app.post('/api/v1.0/user/product-type/modify', tokenToUserMiddleware, getStaffInfMiddleware, UserProductTypeHandle.modify)
app.post('/api/v1.0/user/product-type/config-add-product-type', tokenToUserMiddleware, getStaffInfMiddleware, UserProductTypeHandle.configAddProductType)
app.post('/api/v1.0/user/product-type/sort', UserProductTypeHandle.sort)

// staff
app.post('/api/v1.0/user/staff/create', tokenToUserMiddleware, UserStaffHandle.create);
app.post('/api/v1.0/user/staff/modify', tokenToUserMiddleware, UserStaffHandle.modify);
app.post('/api/v1.0/user/staff/inactive', tokenToUserMiddleware, UserStaffHandle.inactive);
app.post('/api/v1.0/user/staff/list', tokenToUserMiddleware, UserStaffHandle.list);
app.post('/api/v1.0/user/staff/get', tokenToUserMiddleware, UserStaffHandle.get);
app.post('/api/v1.0/user/staff/change-status', tokenToUserMiddleware, UserStaffHandle.changeStatus);

// toppingGroup
app.post('/api/v1.0/user/toppingGroup/create', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingGroupHandle.create);
app.post('/api/v1.0/user/toppingGroup/modify', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingGroupHandle.modify);
app.post('/api/v1.0/user/toppingGroup/inactive', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingGroupHandle.inactive);
app.post('/api/v1.0/user/toppingGroup/list', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingGroupHandle.list);
app.post('/api/v1.0/user/toppingGroup/get', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingGroupHandle.get);

// topping
app.post('/api/v1.0/user/topping/create', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingHandle.create);
app.post('/api/v1.0/user/topping/modify', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingHandle.modify);
app.post('/api/v1.0/user/topping/inactive', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingHandle.inactive);
app.post('/api/v1.0/user/topping/list', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingHandle.list);
app.post('/api/v1.0/user/topping/get', tokenToUserMiddleware, getStaffInfMiddleware, UserToppingHandle.get);

app.post('/api/v1.0/sycn/product-type', SyncHandle.productType)
app.post('/api/v1.0/sycn/product', SyncHandle.product)
app.post('/api/v1.0/sycn/store', SyncHandle.store)

// order
app.post('/api/v1.0/order/create', tokenToUserMiddleware, OrderHandle.create);
app.post('/api/v1.0/order-store/add-processing', OrderHandle.addProcessingOrder);
app.post('/api/v1.0/order/list-for-merchant', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.listForMerchant);
app.post('/api/v1.0/order/get-for-merchant', tokenToUserMiddleware, OrderHandle.getForMerchant);
app.post('/api/v1.0/order/reject-for-merchant', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.rejectForMerchant);
app.post('/api/v1.0/order/confirm-order', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.confirmOrder);
app.post('/api/v1.0/order/list-for-customer', tokenToUserMiddleware, OrderHandle.listForCustomer);
app.post('/api/v1.0/order/get-for-customer', tokenToUserMiddleware, OrderHandle.getForCustomer);
app.post('/api/v1.0/order/reject-for-customer', tokenToUserMiddleware, OrderHandle.rejectForCustomer);
app.post('/api/v1.0/order/get-time-line', tokenToUserMiddleware, OrderHandle.getTimeLine);
app.post('/api/v1.0/order/config-show-store', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.configShowStore);
app.post('/api/v1.0/order/config-bar-code', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.configBarCodeScan);
app.post('/api/v1.0/order/count-order-pending', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.countOrderPending);
app.post('/api/v1.0/order/list-reasons', tokenToUserMiddleware, OrderHandle.listReasonsReject);
app.post('/api/v1.0/order/config-show-contact', tokenToUserMiddleware, OrderHandle.configContact);
app.post('/api/v1.0/order/config-wait-time', tokenToUserMiddleware, OrderHandle.configWaitTime);
app.post('/api/v1.0/order/wait-order', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.waitOrder);
app.post('/api/v1.0/order/update-status-merchant', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.updateStatusForMerchant);
app.post('/api/v1.0/order/retry-for-merchant', tokenToUserMiddleware, getStaffInfMiddleware, OrderHandle.retryForMerchant);
app.post('/api/v1.0/order/config-self-delivery', tokenToUserMiddleware, OrderHandle.configSelfDelivery);
app.post('/api/v1.0/order/list-by-id', tokenToUserMiddleware, OrderHandle.listByIds);
app.post('/api/v1.0/order/config-message-warning', tokenToUserMiddleware, OrderHandle.configMessageWarning);

// statistics
app.post('/api/v1.0/statistic/income', tokenToUserMiddleware, getStaffInfMiddleware, StatisticHandle.incomeStatistic);

// promote
app.post('/api/v1.0/user/promote/create', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.create);
app.post('/api/v1.0/user/promote/modify', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.modify);
app.post('/api/v1.0/user/promote/inactive', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.inactive);
app.post('/api/v1.0/user/promote/list', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.list);
app.post('/api/v1.0/user/promote/get', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.get);
app.post('/api/v1.0/user/promote/count', tokenToUserMiddleware, UserPromoteHandle.count);
app.post('/api/v1.0/user/promote/list-co-sponsors', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.listCoSponsors);
app.post('/api/v1.0/user/promote/register-co-sponsors', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.registerCoSponsors);
app.post('/api/v1.0/user/promote/inactive-co-sponsors', tokenToUserMiddleware, UserPromoteHandle.inactiveCoSponsors);
app.post('/api/v1.0/user/promote/config-co-sponsors', tokenToUserMiddleware, getStaffInfMiddleware, UserPromoteHandle.configCoSponsors);

app.post('/api/v1.0/promote/get', tokenToUserMiddleware, PromoteHandle.get);
app.post('/api/v1.0/promote/list', tokenToUserMiddleware, PromoteHandle.list);
app.post('/api/v1.0/promote/count', tokenToUserMiddleware, PromoteHandle.count);
app.post('/api/v1.0/promote/config-show-promote', tokenToUserMiddleware, PromoteHandle.configShowPromote);

// CategoryStrategyHandle
app.post('/api/v1.0/category-strategy/list', tokenToUserMiddleware, CategoryStrategyHandle.list);
app.post('/api/v1.0/category-strategy/list-store', tokenToUserMiddleware, CategoryStrategyHandle.listStore);

// orderType
app.post('/api/v1.0/order-type/get', tokenToUserMiddleware, OrderTypeHandle.get);

const port = _.get(config, 'port', 3000);
server.listen(port, () => {
  logger.logInfo(moment().format('DD/MM/YYYY HH:mm:ss'), ' - ', 'Server listening at port:', port)

  logger.logError([`${moment().format('DD/MM/YYYY HH:mm:ss')} - Service has been started at port: ${port}`])
});

process.on('uncaughtException', (err) => {
  logger.logError(['uncaughtException', err])
});
