
const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const moment = require('moment')
const ms = require('ms')
const tool = require('./lib/utils/tool');

var workbook = new Excel.Workbook();
let index = 0;
let region = '';
let stores = [];

workbook.xlsx.readFile(`${ABSPATH}/DSHeyU.xlsx`)
  .then(function () {
    var worksheet = workbook.getWorksheet(4);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber >= 2 && rowNumber < 75 && row.values && row.values.length) {
        const phone = row.values[4].trim().replace(/ /g, '');

        const getStore = (next) => {
          UserStoreModel
            .findOne({
              phone,
              level: 1
            }, '_id')
            .sort({createdAt: -1})
            .lean()
            .exec((err, results) => {
              console.log('haha:results', err, results)
              stores.push(results._id)
              next();
            })
        }

        async.waterfall([
          getStore
        ], (err, data) => {
          // err && _.isError(err) && (data = {
          //   code: CONSTANTS.CODE.SYSTEM_ERROR,
          //   message: MESSAGES.SYSTEM.ERROR
          // });
          index++;
          console.log('haha:result', rowNumber, index);
          console.log('haha:err', err, data);
          if (index === 73) {
            console.log('haha:', stores)
          }
        })
      }
    });
  });


  [ObjectId('637891494543934e37870fef'),ObjectId('64b5efab0c02150d1b4f7f51'), ObjectId('64b5efac0c02150d1b4f81a8'), ObjectId('64b5efab0c02150d1b4f7ed0'),ObjectId('64b5efac0c02150d1b4f7fa4'), ObjectId('64b5efab0c02150d1b4f7f55'), ObjectId('64b5efab0c02150d1b4f7eed'),ObjectId('64b5efac0c02150d1b4f8194'), ObjectId('64b5efac0c02150d1b4f7fa2'), ObjectId('64b5efab0c02150d1b4f7ece'),ObjectId('64b5efab0c02150d1b4f7f59'), ObjectId('64b5efab0c02150d1b4f7eca'), ObjectId('64b5efac0c02150d1b4f819c'),ObjectId('64b5efab0c02150d1b4f7ec8'), ObjectId('64b5efac0c02150d1b4f8020'), ObjectId('64b5efac0c02150d1b4f8b4d'),ObjectId('64b5efac0c02150d1b4f8196'), ObjectId('64b5efab0c02150d1b4f7f53'), ObjectId('64b5efad0c02150d1b4f8b5f'),ObjectId('64b5efab0c02150d1b4f7f24'), ObjectId('64b5efac0c02150d1b4f801c'), ObjectId('64b5efac0c02150d1b4f7fae'),ObjectId('64b5efac0c02150d1b4f7f9e'), ObjectId('64b5efad0c02150d1b4f96ba'), ObjectId('64b5efac0c02150d1b4f8014'),ObjectId('64b5efab0c02150d1b4f7f5b'), ObjectId('64b5efad0c02150d1b4f8b59'), ObjectId('64b5efab0c02150d1b4f7f26'),ObjectId('64b5efac0c02150d1b4f81a6'), ObjectId('64b5efab0c02150d1b4f7f4f'), ObjectId('64b5efac0c02150d1b4f7fa0'),ObjectId('64b5efac0c02150d1b4f802a'), ObjectId('64b5efac0c02150d1b4f81ae'), ObjectId('64b5efab0c02150d1b4f7f22'),ObjectId('64b5efac0c02150d1b4f8026'), ObjectId('64b5efac0c02150d1b4f7fa8'), ObjectId('64b5efab0c02150d1b4f7f57'),ObjectId('64b5efac0c02150d1b4f8024'), ObjectId('64b5efac0c02150d1b4f801e'), ObjectId('64b5efac0c02150d1b4f7fac'),ObjectId('64b5efac0c02150d1b4f81ac'), ObjectId('64b5efac0c02150d1b4f8b4f'), ObjectId('64b5efad0c02150d1b4f8b61'),ObjectId('64b5efac0c02150d1b4f819a'), ObjectId('64b5efac0c02150d1b4f81a0'), ObjectId('64b5efac0c02150d1b4f8012'),ObjectId('64b5efac0c02150d1b4f8b53'), ObjectId('64b5efac0c02150d1b4f8018'), ObjectId('64b5efab0c02150d1b4f7ecc'),ObjectId('64b5efab0c02150d1b4f7f4d'), ObjectId('64b5efac0c02150d1b4f819e'), ObjectId('64b5efab0c02150d1b4f7f20'),ObjectId('64b5efad0c02150d1b4f8b63'), ObjectId('64b5efac0c02150d1b4f81a4'), ObjectId('64b5efad0c02150d1b4f8b57'),ObjectId('64b5efad0c02150d1b4f96bc'), ObjectId('64b5efac0c02150d1b4f81a2'), ObjectId('64b5efad0c02150d1b4f8b5b'),ObjectId('64b5efab0c02150d1b4f7f1c'), ObjectId('64b5efab0c02150d1b4f7f5d'), ObjectId('64b5efac0c02150d1b4f8198'),ObjectId('64b5efad0c02150d1b4f8b5d'), ObjectId('64b5efac0c02150d1b4f801a'), ObjectId('64b5efab0c02150d1b4f7f1e'),ObjectId('64b5efab0c02150d1b4f7f1a'), ObjectId('64b5efac0c02150d1b4f81aa'), ObjectId('64b5efac0c02150d1b4f7faa'),ObjectId('64b5efab0c02150d1b4f7f4b'), ObjectId('64b5efac0c02150d1b4f8b55'), ObjectId('64b5efac0c02150d1b4f8028'),ObjectId('64b5efac0c02150d1b4f7fa6'), ObjectId('64b5efac0c02150d1b4f8016'), ObjectId('64b5efac0c02150d1b4f8b51'),ObjectId('64b5efac0c02150d1b4f8022')]