const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const tool = require('./lib/utils/tool');
const locationHelper = require('./lib/utils/location');

UserStoreModel
  .find({}, 'workingTime')
  .lean()
  .exec((err, results) => {
    if (err) {
      return console.log('haha:err', err);
    }

    if (!results || !results.length) {
      return console.log('haha:no store');
    }

    results.forEach((item, i) => {
      const timeSettings = {
        '0': [{
          startTime: item.workingTime.from,
          endTime: item.workingTime.to
        }],
        '1': [{
          startTime: item.workingTime.from,
          endTime: item.workingTime.to
        }],
        '2': [{
          startTime: item.workingTime.from,
          endTime: item.workingTime.to
        }],
        '3': [{
          startTime: item.workingTime.from,
          endTime: item.workingTime.to
        }],
        '4': [{
          startTime: item.workingTime.from,
          endTime: item.workingTime.to
        }],
        '5': [{
          startTime: item.workingTime.from,
          endTime: item.workingTime.to
        }],
        '6': [{
          startTime: item.workingTime.from,
          endTime: item.workingTime.to
        }],
      }

      StoreModel
        .update({
          _id: item._id
        }, {
          timeSettings
        })
        .lean()
        .exec((err, result) => {
          if (err) {
            return console.log('haha:err', i, err);
          }

          console.log('haha:result', i, result);
        })
    })
  })
