// get store
// get address five star
// create branch

const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const UserStoreModel = require('./lib/models/userStore');
const UserProductTypeModel = require('./lib/models/userProductType');
const ProductTypeModel = require('./lib/models/productType');
const UserToppingModel = require('./lib/models/userTopping');
const ToppingModel = require('./lib/models/topping');
const UserToppingGroupModel = require('./lib/models/userToppingGroup');
const ToppingGroupModel = require('./lib/models/toppingGroup');
const UserProductModel = require('./lib/models/userProduct');
const ProductModel = require('./lib/models/product');
const _ = require('lodash')

let storeArr = [
  "64b5efab0c02150d1b4f7f51",
  "64b5efac0c02150d1b4f81a8",
  "64b5efab0c02150d1b4f7ed0",
  "64b5efac0c02150d1b4f7fa4",
  "64b5efab0c02150d1b4f7f55",
  "64b5efab0c02150d1b4f7eed",
  "64b5efac0c02150d1b4f8194",
  "64b5efac0c02150d1b4f7fa2",
  "64b5efab0c02150d1b4f7ece",
  "64b5efab0c02150d1b4f7f59",
  "64b5efab0c02150d1b4f7eca",
  "64b5efac0c02150d1b4f819c",
  "64b5efab0c02150d1b4f7ec8",
  "64b5efac0c02150d1b4f8020",
  "64b5efac0c02150d1b4f8b4d",
  "64b5efac0c02150d1b4f8196",
  "64b5efab0c02150d1b4f7f53",
  "64b5efad0c02150d1b4f8b5f",
  "64b5efab0c02150d1b4f7f24",
  "64b5efac0c02150d1b4f801c",
  "64b5efac0c02150d1b4f7fae",
  "64b5efac0c02150d1b4f7f9e",
  "64b5efad0c02150d1b4f96ba",
  "64b5efac0c02150d1b4f8014",
  "64b5efab0c02150d1b4f7f5b",
  "64b5efad0c02150d1b4f8b59",
  "64b5efab0c02150d1b4f7f26",
  "64b5efac0c02150d1b4f81a6",
  "64b5efab0c02150d1b4f7f4f",
  "64b5efac0c02150d1b4f7fa0",
  "64b5efac0c02150d1b4f802a",
  "64b5efac0c02150d1b4f81ae",
  "64b5efab0c02150d1b4f7f22",
  "64b5efac0c02150d1b4f8026",
  "64b5efac0c02150d1b4f7fa8",
  "64b5efab0c02150d1b4f7f57",
  "64b5efac0c02150d1b4f8024",
  "64b5efac0c02150d1b4f801e",
  "64b5efac0c02150d1b4f7fac",
  "64b5efac0c02150d1b4f81ac",
  "64b5efac0c02150d1b4f8b4f",
  "64b5efad0c02150d1b4f8b61",
  "64b5efac0c02150d1b4f819a",
  "64b5efac0c02150d1b4f81a0",
  "64b5efac0c02150d1b4f8012",
  "64b5efac0c02150d1b4f8b53",
  "64b5efac0c02150d1b4f8018",
  "64b5efab0c02150d1b4f7ecc",
  "64b5efab0c02150d1b4f7f4d",
  "64b5efac0c02150d1b4f819e",
  "64b5efab0c02150d1b4f7f20",
  "64b5efad0c02150d1b4f8b63",
  "64b5efac0c02150d1b4f81a4",
  "64b5efad0c02150d1b4f8b57",
  "64b5efad0c02150d1b4f96bc",
  "64b5efac0c02150d1b4f81a2",
  "64b5efad0c02150d1b4f8b5b",
  "64b5efab0c02150d1b4f7f1c",
  "64b5efab0c02150d1b4f7f5d",
  "64b5efac0c02150d1b4f8198",
  "64b5efad0c02150d1b4f8b5d",
  "64b5efac0c02150d1b4f801a",
  "64b5efab0c02150d1b4f7f1e",
  "64b5efab0c02150d1b4f7f1a",
  "64b5efac0c02150d1b4f81aa",
  "64b5efac0c02150d1b4f7faa",
  "64b5efab0c02150d1b4f7f4b",
  "64b5efac0c02150d1b4f8b55",
  "64b5efac0c02150d1b4f8028",
  "64b5efac0c02150d1b4f7fa6",
  "64b5efac0c02150d1b4f8016",
  "64b5efac0c02150d1b4f8b51",
  "64b5efac0c02150d1b4f8022"
]



storeArr.map(storeId => {
  UserStoreModel
    .findOne({ _id: storeId }, 'member level')
    .lean()
    .exec((error, store) => {
      if (error || !store) {
        return console.log('haha:err find store', storeId, error, store)
      }

      if (store.level === -3) {
        return console.log('haha deleted store', storeId, error)
      }

      UserProductTypeModel
        .update({ store: storeId, status: 1 }, {
          status: 0
        }, { multi: true })
        .exec((errUPT1, resultUPT1) => {
          console.log('haha remove product type', errUPT1, resultUPT1)

          UserProductTypeModel
            .update({ store: '63ddd12c1eb1a77a6d2d2497' }, {
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((errUPT, resultUPT) => {
              console.log('haha product type', errUPT, resultUPT)
            })
        })

      ProductTypeModel
        .update({ store: storeId, status: 1 }, {
          status: 0
        }, { multi: true })
        .exec((errPT1, resultPT1) => {
          console.log('haha remove product type', errPT1, resultPT1)

          ProductTypeModel
            .update({ store: '63ddd12c1eb1a77a6d2d2497' }, {
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((errPT, resultPT) => {
              console.log('haha product type', errPT, resultPT)
            })
        })

      UserToppingModel
        .update({ store: storeId, status: 1 }, {
          status: 0
        }, { multi: true })
        .exec((errUT1, resultUT1) => {
          console.log('haha remove product type', errUT1, resultUT1)

          UserToppingModel
            .update({ store: '63ddd12c1eb1a77a6d2d2497' }, {
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((errUT, resultUT) => {
              console.log('haha product type', errUT, resultUT)
            })
        })

      ToppingModel
        .update({ store: storeId, status: 1 }, {
          status: 0
        }, { multi: true })
        .exec((errT1, resultT1) => {
          console.log('haha remove product type', errT1, resultT1)

          ToppingModel
            .update({ store: '63ddd12c1eb1a77a6d2d2497' }, {
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((errT, resultT) => {
              console.log('haha product type', errT, resultT)
            })
        })

      UserToppingGroupModel
        .update({ store: storeId, status: 1 }, {
          status: 0
        }, { multi: true })
        .exec((errUTG1, resultUTG1) => {
          console.log('haha remove product type', errUTG1, resultUTG1)

          UserToppingGroupModel
            .update({ store: '63ddd12c1eb1a77a6d2d2497' }, {
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((errUTG, resultUTG) => {
              console.log('haha product type', errUTG, resultUTG)
            })
        })

      ToppingGroupModel
        .update({ store: storeId, status: 1 }, {
          status: 0
        }, { multi: true })
        .exec((errTG1, resultTG1) => {
          console.log('haha remove product type', errTG1, resultTG1)

          ToppingGroupModel
            .update({ store: '63ddd12c1eb1a77a6d2d2497' }, {
              $addToSet: {
                store: storeId
              }
            }, { multi: true })
            .exec((errTG, resultTG) => {
              console.log('haha product type', errTG, resultTG)
            })
        })

      UserProductModel
        .update({ store: storeId, status: 1 }, {
          status: 0
        }, { multi: true })
        .exec((errUP, resultUP) => {
          console.log('haha remove user product', errUP, resultUP)

          ProductModel
            .update({ store: storeId, status: 1 }, {
              status: 0
            }, { multi: true })
            .exec((errP, resultP) => {
              console.log('haha remove product', errP, resultP)

              UserProductModel
                .find({ store: '63ddd12c1eb1a77a6d2d2497' }, '-_id')
                .lean()
                .exec((err, results) => {
                  if (err) {
                    return console.log('haha:err find product', err)
                  }

                  results.map(result => {
                    UserProductModel
                      .create({ ...result, store: storeId, isAvailable: storeId, member: store.member }, (errCreate, resultCreate) => {
                        console.log('haha:create user product', errCreate, resultCreate)
                      })

                    ProductModel
                      .create({ ...result, store: storeId, isAvailable: storeId, member: store.member }, (errCreate, resultCreate) => {
                        console.log('haha:create product', errCreate, resultCreate)
                      })
                  })
                })
            })
        })
    })
})
