const rp = require('request-promise');
const async = require('async');
const LoshipStoreMarket = require('./lib/models/loshipStoreMarket')
const PharmacityStore = require('./lib/models/pharmacityStore');
const express = require('express');
const config = require('config');
const _ = require('lodash');
const uuid = require('uuid/v4')
const qs = require('querystring');
const path = require('fs');
const Logger = require('./lib/logger')
global.logger = Logger(`${__dirname}/logs`);
const mongoose = require('mongoose')
const tool = require('./lib/utils/tool');

const province_id = [1, 20, 32, 59]

PharmacityStore
  .find({province_id: {$in: province_id}})
  .lean()
  .exec((err, results) => {
    if (err) {
      return console.log('haha err find', err);
    }

    if (!results || !results.length) {
      return console.log('haha no store');
    }


  })
g