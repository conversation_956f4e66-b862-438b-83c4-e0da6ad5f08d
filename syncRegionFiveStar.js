// get store
// get address five star
// create branch

const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const moment = require('moment')
const ms = require('ms')
const tool = require('./lib/utils/tool');

var workbook = new Excel.Workbook();
let index = 0;
let region = '';

workbook.xlsx.readFile(`${ABSPATH}/DSHeyU.xlsx`)
  .then(function () {
    var worksheet = workbook.getWorksheet(4);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber >= 2 && rowNumber < 75 && row.values && row.values.length) {
        if (row.values[2].trim() === 'CẦN THƠ') {
          region = 'vietnam:cantho';
        } else if (row.values[2].trim() === 'Đà Nẵng') {
          region = 'vietnam:danang';
        } else if (row.values[2].trim() === 'Hải Phòng') {
          region = 'vietnam:haiphong';
        } else if (row.values[2].trim() === 'Hải Dương') {
          if (row.values[9].trim() === 'Hải Dương') {
            region = 'vietnam:haiduong';
          } else if (row.values[9].trim() === 'Chí Linh') {
            region = 'vietnam:haiduong-chilinh';
          } else if (row.values[9].trim() === 'Nam Sách') {
            region = 'vietnam:haiduong-namsach';
          }
        } else if (row.values[2].trim() === 'Nghệ An') {
          region = 'vietnam:nghean';
        } else if (row.values[2].trim() === 'Thanh Hóa') {
          region = 'vietnam:thanhhoa';
        } else if (row.values[2].trim() === 'Hà Tĩnh') {
          region = 'vietnam:hatinh';
        } else if (row.values[2].trim() === 'HN') {
          region = 'hn';
        } else if (row.values[2].trim() === 'HCM') {
          region = 'hcm';
        } else if (row.values[2].trim() === 'Thái Nguyên') {
          region = 'vietnam:thainguyen';
        } else if (row.values[2].trim() === 'Khánh Hòa') {
          region = 'vietnam:khanhhoa';
        } else if (row.values[2].trim() === 'Bắc Ninh') {
          region = 'vietnam:bacninh';
        } else if (row.values[2].trim() === 'Hưng Yên') {
          if (row.values[9].trim() === 'TP. Hưng Yên') {
            region = 'vietnam:hungyen';
          } else if (row.values[9].trim() === 'Khoái Châu') {
            region = 'vietnam:hungyen-khoaichau';
          } else if (row.values[9].trim() === 'Phù Cừ') {
            region = 'vietnam:hungyen-phucu';
          }
        }

        const phone = row.values[4].trim().replace(/ /g, '');
        console.log('haha:region', rowNumber, phone, region)

        const updateStore = (next) => {
          UserStoreModel
            .update({
              phone
            }, {
              region
            }, { multi: true })
            .exec((err, result) => {
              console.log('haha:err', err, result)
              StoreModel
                .update({
                  phone
                }, {
                  region
                }, { multi: true })
                .exec((err, result) => {
                  console.log('haha:er', err, result)
                  if (err) {
                    return next(err);
                  }

                  next();
                })
            })
        }

        async.waterfall([
          updateStore
        ], (err, data) => {
          // err && _.isError(err) && (data = {
          //   code: CONSTANTS.CODE.SYSTEM_ERROR,
          //   message: MESSAGES.SYSTEM.ERROR
          // });
          index++;
          console.log('haha:result', rowNumber, index);
          console.log('haha:err', err, data);
        })
      }
    });
  });
