// get store
// get address five star
// create branch

const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const UserStoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const moment = require('moment')
const ms = require('ms')
const tool = require('./lib/utils/tool');

var workbook = new Excel.Workbook();
let index = 0;
let region = '';

workbook.xlsx.readFile(`${ABSPATH}/DSHeyU.xlsx`)
  .then(function () {
    var worksheet = workbook.getWorksheet(4);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber >= 2 && rowNumber < 75 && row.values && row.values.length) {
        const phone = row.values[4].trim().replace(/ /g, '');

        const updateStore = (next) => {
          UserStoreModel
            .update({
              phone
            }, {
              focus: 1
            }, { multi: true })
            .exec((err, result) => {
              next();
            })
        }

        async.waterfall([
          updateStore
        ], (err, data) => {
          // err && _.isError(err) && (data = {
          //   code: CONSTANTS.CODE.SYSTEM_ERROR,
          //   message: MESSAGES.SYSTEM.ERROR
          // });
          index++;
          console.log('haha:result', rowNumber, index);
          console.log('haha:err', err, data);
        })
      }
    });
  });
