const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/store');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const tool = require('./lib/utils/tool');

// StoreModel
//   .find({ status: 1, createdAt: { $gte: 1632819600000 }, nameAlias: /vinmart/, 'location.coordinates': [0, 0] })
//   .lean()
//   .exec((err, results) => {
//     if (err) {
//       return console.log('haha:err', err);
//     }

//     if (!results || !results.length) {
//       return console.log('haha:no store');
//     }

//     results.forEach((item, i) => {
//       const options = {
//         method: 'POST',
//         uri: `${config.proxyRequestServer.google}/api/v1.0/google/name-to-location`,
//         body: {
//           text: item.address.trim()
//         },
//         json: true // Automatically stringifies the body to JSON
//       };

//       rp(options)
//         .then((result) => {
//           // console.log('haha:result', rowNumber, storeArr.length, result);
//           if (result.code === 200) {
//             // objPush.location.coordinates = [result.data.lng, result.data.lat];
//             // storeArr.push(objPush);
//             StoreModel
//               .update({
//                 _id: item._id
//               }, {
//                 location: {
//                   type: 'Point',
//                   coordinates: [result.data.lng, result.data.lat]
//                 }
//               })
//               .lean()
//               .exec((err, res) => {
//                 if (err) {
//                   return console.log('haha:err update:', err)
//                 }

//                 console.log('haha res update', i, res)
//               })

//             // if (storeArr.length === 1230) {
//             //   StoreModel.insertMany(storeArr, (err) => {
//             //     if (err) {
//             //       return console.log('haha:err:insert', err);
//             //     }

//             //     console.log('haha:done');
//             //   });
//             // }
//           } else {
//             // objPush.location.coordinates = [0, 0];
//             // storeArr.push(objPush);

//             // if (storeArr.length === 1230) {
//             //   StoreModel.insertMany(storeArr, (err) => {
//             //     if (err) {
//             //       return console.log('haha:err:insert', err);
//             //     }

//             //     console.log('haha:done');
//             //   });
//             // }
//             console.log('haha:err google', result)
//           }
//         })
//         .catch((err) => {
//           console.log('haha:catch:err', err);
//           // objPush.location.coordinates = [0, 0];
//           // storeArr.push(objPush);

//           // if (storeArr.length === 1230) {
//           //   StoreModel.insertMany(storeArr, (err) => {
//           //     if (err) {
//           //       return console.log('haha:err:insert', err);
//           //     }

//           //     console.log('haha:done');
//           //   });
//           // }
//         });
//     })
//   })

// return;

var workbook = new Excel.Workbook();
let storeArr = [];
let region = '';
let timeout = 0;

workbook.xlsx.readFile(`${ABSPATH}/vm+.xlsx`)
  .then(function () {
    var worksheet = workbook.getWorksheet(1);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber > 15) {
        return;
      }

      setTimeout(() => {
        timeout += 1000;
        if (row.values[3].trim() === 'CẦN THƠ') {
          region = 'vietnam:cantho';
        } else if (row.values[3].trim() === 'ĐÀ NẴNG') {
          region = 'vietnam:danang';
        } else if (row.values[3].trim() === 'HẢI PHÒNG') {
          region = 'vietnam:haiphong';
        } else if (row.values[3].trim() === 'NGHỆ AN') {
          region = 'vietnam:nghean';
        } else if (row.values[3].trim() === 'THANH HÓA') {
          region = 'vietnam:thanhhoa';
        } else if (row.values[3].trim() === 'HÀ NỘI') {
          region = 'hn';
        } else if (row.values[3].trim() === 'THÁI NGUYÊN') {
          region = 'vietnam:thainguyen';
        }

        let name = `Vinmart+ ${row.values[2].trim().substring(8)}`;
        console.log('haha:name', name, rowNumber);

        let objPush = {
          status: 1,
          phone: ['02471066866'],
          productTypes: [
            "6142ce07efd48c3dda53a635",
            "6142ce07efd48c3dda53a636",
            "6142ce07efd48c3dda53a637",
            "6142ce07efd48c3dda53a638",
            "6142ce07efd48c3dda53a639",
            "6142ce07efd48c3dda53a63a",
            "6142ce07efd48c3dda53a63b",
            "6142ce07efd48c3dda53a63c"
          ],
          category: '60bb290e2368ec53d2782d40',
          address: row.values[4].trim(),
          addressAlias: tool.change_alias(row.values[4].trim()),
          region,
          name: name,
          nameAlias: tool.change_alias(name),
          timeSettings: {
            "0": [
              {
                "startTime": 28800000,
                "endTime": 79200000
              }
            ],
            "1": [
              {
                "startTime": 28800000,
                "endTime": 79200000
              }
            ],
            "2": [
              {
                "startTime": 28800000,
                "endTime": 79200000
              }
            ],
            "3": [
              {
                "startTime": 28800000,
                "endTime": 79200000
              }
            ],
            "4": [
              {
                "startTime": 28800000,
                "endTime": 79200000
              }
            ],
            "5": [
              {
                "startTime": 28800000,
                "endTime": 79200000
              }
            ],
            "6": [
              {
                "startTime": 28800000,
                "endTime": 79200000
              }
            ]
          },
          hasProduct: 0,
          background: 'https://media.heyu.asia/uploads/new-image-service/2021-09-15-default-background.png',
          image: 'https://media.heyu.asia/uploads/store/2021-09-16-logovinmart.png',
          productSearch: [],
          service: '613ef6b37b4adc4587d630e5',
          storeNote: 'Lưu ý: \n - Giá có thể thay đổi theo từng thời điểm\n - Bạn có thể yêu cầu tài xế lấy hóa đơn mua thuốc/mua hàng và thanh toán theo đúng hóa đơn (nếu cần)',
          description: '',
          location: {
            type: 'Point',
            coordinates: []
          },
          createdAt: Date.now(),
          updatedAt: Date.now()
        }

        const options = {
          method: 'POST',
          uri: `${config.proxyRequestServer.google}/api/v1.0/google/name-to-location`,
          body: {
            text: row.values[4].trim()
          },
          json: true // Automatically stringifies the body to JSON
        };

        rp(options)
          .then((result) => {
            console.log('haha:result', rowNumber, storeArr.length, result);
            if (result.code === 200) {
              objPush.location.coordinates = [result.data.lng, result.data.lat];
              storeArr.push(objPush);

              if (storeArr.length === 15) {
                StoreModel.insertMany(storeArr, (err) => {
                  if (err) {
                    return console.log('haha:err:insert', err);
                  }

                  console.log('haha:done');
                });
              }
            } else {
              objPush.location.coordinates = [0, 0];
              storeArr.push(objPush);

              if (storeArr.length === 15) {
                StoreModel.insertMany(storeArr, (err) => {
                  if (err) {
                    return console.log('haha:err:insert', err);
                  }

                  console.log('haha:done');
                });
              }
            }
          })
          .catch((err) => {
            console.log('haha:catch:err', storeArr.length, err);
            objPush.location.coordinates = [0, 0];
            storeArr.push(objPush);

            if (storeArr.length === 15) {
              StoreModel.insertMany(storeArr, (err) => {
                if (err) {
                  return console.log('haha:err:insert', err);
                }

                console.log('haha:done');
              });
            }
          });

        return;
      }, timeout);
    });
  });
