const rp = require('request-promise');
const async = require('async');
const LoshipStoreMarket = require('./lib/models/loshipStoreMarket')
const StoreModel = require('./lib/models/store');
const express = require('express');
const config = require('config');
const _ = require('lodash');
const uuid = require('uuid/v4')
const qs = require('querystring');
const path = require('fs');
const Logger = require('./lib/logger')
global.logger = Logger(`${__dirname}/logs`);
const mongoose = require('mongoose')
const tool = require('./lib/utils/tool');
const cityId = 219;
// const downloader = require('image-downloader')



// transfer
LoshipStoreMarket
  .find({typeData: 'medicine'})
  .lean()
  .exec((err, data) => {
    console.log('haha:err', data.length);
    if (err) {
      return console.log('haha:err', err);
    }

    if (!data || !data.length) {
      return console.log('haha:no store medicine');
    }

    data.forEach((item, i) => {
      let objCreate = {
        name: item.name.trim(),
        nameAlias: tool.change_alias(item.name.trim()),
        address: item.address.full.trim(),
        location: {
          type: 'Point',
          coordinates: [item.long, item.lat]
        },
        addressAlias: tool.change_alias(item.address.full.trim()),
        region: item.regionData === 'hanoi' ? 'hn' : `vietnam:${item.regionData}`,
        phone: [item.phone],
        hasProduct: 0,
        status: 1,
        ref: {
          id: item.id,
          refName: 'loship_medicine'
        },
        service: '609f759d0f5f34653f164ba5',
        productTypes: [],
        category: '',
        timeSettings: {},
        image: '',
        background: '',
        productSearch: [],
        description: '',
        note: '',
        storeNote: 'Lưu ý: \n - Giá có thể thay đổi theo từng thời điểm\n - Bạn có thể yêu cầu tài xế lấy hóa đơn mua thuốc/mua hàng và thanh toán theo đúng hóa đơn (nếu cần)',
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
    })
  })
