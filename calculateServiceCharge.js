const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const OrderStoreModel = require('./lib/models/orderStore');
const ms = require('ms');

let totalServiceCharge = 0;
let length = 0;
let hour = 0;
const currentDate = new Date();
const ms1h = ms('1h');
let startTime = currentDate.setHours(0, 0, 0, 0); // 1642611600000
let endTime = startTime + ms1h;
const now = Date.now();
const region = '';

calculate = () => {
  const query = { status: 4, createdAt: { $gte: startTime, $lt: endTime }, serviceChargeMerchant: { $gt: 0 } };
  if (region) {
    query.region = region;
  }

  OrderStoreModel
    .find(query, 'serviceChargeMerchant')
    .lean()
    .exec((err, orders) => {
      if (err) {
        return console.log('haha:err', err);
      }

      console.log('haha:orders', `${hour}h`, orders.length)
      if (!orders || !orders.length) {
        changeTime();

        return;
      }

      length += orders.length;
      orders.map(order => {
        totalServiceCharge += order.serviceChargeMerchant;
      })

      changeTime();
    })
}

changeTime = () => {
  if (endTime < now) {
    startTime = endTime;
    endTime = startTime + ms1h;
    if (hour < 23) {
      hour++;
    } else {
      hour = 0;
    }

    calculate();
  } else {
    console.log('haha:totalServiceCharge', length, totalServiceCharge);
  }
}

calculate();
