const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const ProductTypeModel = require('./lib/models/productType');
const ProductModel = require('./lib/models/product');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const tool = require('./lib/utils/tool');
const locationHelper = require('./lib/utils/location');

ProductTypeModel
  .find({}, '_id')
  .lean()
  .exec((err, results) => {
    if (err) {
      return console.log('haha:err', err);
    }

    if (!results || !results.length) {
      return console.log('haha:no product type');
    }

    results.map((item, i) => {
      ProductModel
        .findOne({productType: item._id}, '_id')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return console.log('haha:err1', i, err);
          }

          ProductTypeModel
            .update({
              _id: item._id
            }, {
              hasProduct: 1
            })
            .lean()
            .exec((err, result) => {
              if (err) {
                return console.log('haha:err2', i, err);
              }

              console.log('haha:result', i, result);
            })
        })
    })
  })
