const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const StoreModel = require('./lib/models/userStore');
const config = require('config')
const async = require('async')
const _ = require('lodash')
const path = require('path');
const ABSPATH = path.dirname(process.mainModule.filename);
const Excel = require('exceljs');
const rp = require('request-promise')
const tool = require('./lib/utils/tool');
const locationHelper = require('./lib/utils/location');

StoreModel
  .find({ region: { $exists: false } }, 'location')
  .lean()
  .exec((err, results) => {
    if (err) {
      return console.log('haha:err', err);
    }

    if (!results || !results.length) {
      return console.log('haha:no store');
    }

    results.forEach((item, i) => {
      const location = {
        lat: item.location.coordinates[1],
        lng: item.location.coordinates[0]
      }

      locationHelper
        .getRegionByLatLng(location, 2, (err, regionName) => {
          if (err) {
            return console.log('haha:err', i, err);
          }

          StoreModel
            .update({
              _id: item._id,
            }, {
              region: regionName
            })
            .lean()
            .exec((err, result) => {
              if (err) {
                return console.log('haha:err', i, err);
              }

              console.log('haha:result', i, result);
            })
        })
    })
  })
