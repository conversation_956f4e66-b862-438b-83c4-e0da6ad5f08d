const Logger = require('./lib/logger')

// Global variables
global.logger = Logger(`${__dirname}/logs`);
const CONSTANTS = require('./lib/const')
const config = require('config')
const _ = require('lodash')
const async = require('async')
const UserStoreModel = require('./lib/models/userStore');
const MemberModel = require('./lib/models/member');
const StaffModel = require('./lib/models/staff');
const StaffLogModel = require('./lib/models/staffLog');
const userId = '63e0b74eb1aec7289164c03b';
let memberId;

UserStoreModel
  .find({
    member: userId
  }, 'phone region')
  .lean()
  .exec((err, results) => {
    if (results && results.length) {
      results.map((storeInf, index) => {
        const phone = storeInf.phone[0]

        const getMemberInf = (next) => {
          MemberModel
            .findOne({ phone })
            .lean()
            .exec((err, result) => {
              if (err) {
                console.log('haha:err', phone, err)
                return next(err);
              }

              if (!result) {
                MemberModel
                  .create({
                    phone,
                    status: 1,
                    region: storeInf.region
                  }, (error, data) => {
                    if (error || !data) {
                      console.log('haha err create', phone, error)
                      return next(error);
                    }

                    memberId = data._id;

                    next();
                  })
              } else {
                memberId = result._id;

                next();
              }
            })
        }

        const checkMember = (next) => {
          if (!memberId) {
            console.log('k tồn tại member', memberId)
            return next(new Error('Member not found'))
          }

          if (userId === memberId.toString()) {
            console.log('haha Bạn không thể tự thêm chính mình làm nhân viên. Xin cảm ơn.', phone)
            return next(new Error('Bạn không thể tự thêm chính mình làm nhân viên. Xin cảm ơn.'));
          }

          UserStoreModel
            .findOne({
              member: memberId
            }, '_id')
            .lean()
            .exec((err, result) => {
              if (err) {
                console.log('haha err check member', err)
                return next(err);
              }

              if (result) {
                console.log('haha đã sở hữu cửa hàng', result._id)
                return next(new Error('Staff owned store'));
              }

              next();
            })
        }

        const checkStaffOtherStore = (next) => {
          StaffModel
            .findOne({
              member: memberId,
              status: 1
            })
            .lean()
            .exec((err, result) => {
              if (err) {
                console.log('haha err check staff', err)
                return next(err);
              }

              if (result) {
                console.log('haha đã là nv cửa hàng khác', result._id)
                return next(new Error('Staff exists other store'));
              }

              next();
            })
        }

        const checkStaffExists = (next) => {
          StaffModel
            .findOne({
              store: storeInf._id,
              member: memberId
            })
            .lean()
            .exec((err, result) => {
              if (err) {
                console.log('haha err check staff exists', err)
                return next(err);
              }

              if (result) {
                console.log('haha đã là nv cửa hàng', result._id)
                return next(new Error('Staff exists other store'));
              }

              next();
            })
        }

        const createStaff = (next) => {
          StaffModel
            .create({
              store: storeInf._id,
              phone,
              member: memberId,
              role: 'staff'
            }, (err, result) => {
              if (err) {
                return next(err)
              }

              StaffLogModel
                .create({
                  type: 0,
                  staff: result._id,
                  member: userId,
                  data: result
                })

              console.log('haha done ', index, phone, storeInf._id)
              next(null, {
                code: CONSTANTS.CODE.SUCCESS
              })
            })
        }

        async.waterfall([
          getMemberInf,
          checkMember,
          checkStaffOtherStore,
          checkStaffExists,
          createStaff
        ], (err, data) => {
          console.log('haha', phone, err, data)
        })
      })
    }
  })
